# TestRecommenderRelationService 测试类使用说明

## 概述

`TestRecommenderRelationService` 是为 `RecommenderRelationService` 服务类创建的完整测试套件，参考了项目中 `TestUserService` 的测试模式和风格。

## 测试覆盖范围

### 1. 酒店关系列表查询测试

#### 基础功能测试
- **testGetRecommenderHotelRelationListSuccess**: 正常流程测试
- **testGetHotelRelationListWithKeyword**: 关键词搜索测试
- **testGetHotelRelationListWithDateFilter**: 日期筛选测试

#### 分页和排序测试
- **testGetHotelRelationListWithDifferentPageSizes**: 不同分页大小测试（50/100/150/200）
- **testGetHotelRelationListWithDifferentSortTypes**: 不同排序类型测试

### 2. 导游供应商列表查询测试

#### 基础功能测试
- **testGetTourGuideSupplierListSuccess**: 正常流程测试
- **testGetGuideSupplierListWithKeyword**: 关键词搜索测试
- **testGetGuideSupplierListWithDateFilter**: 日期筛选测试

#### 筛选功能测试
- **testGetGuideSupplierListWithProductionLineFilter**: 产线筛选测试（司机/导游/司兼导）
- **testGetGuideSupplierListWithControlStatusFilter**: 状态筛选测试（正常/管控/全部）

#### 分页测试
- **testGetGuideSupplierListWithDifferentPageSizes**: 不同分页大小测试

### 3. 边界条件和异常测试

#### 酒店关系测试
- **testGetHotelRelationListWithInvalidUserId**: 无效用户ID测试
- **testGetHotelRelationListWithEmptyKeyword**: 空关键词测试
- **testGetHotelRelationListWithReversedDateRange**: 日期范围颠倒测试

#### 导游供应商测试
- **testGetGuideSupplierListWithInvalidRecommenderId**: 无效推荐方ID测试
- **testGetGuideSupplierListWithInvalidProductionLine**: 无效产线类型测试
- **testGetGuideSupplierListWithInvalidControlStatus**: 无效状态筛选测试
- **testGetGuideSupplierListWithLargePageNum**: 极大页码测试
- **testGetGuideSupplierListWithCombinedFilters**: 组合筛选条件测试

### 4. 性能测试

- **testGetHotelRelationListPerformance**: 酒店关系列表查询性能测试
- **testGetGuideSupplierListPerformance**: 导游供应商列表查询性能测试

## 运行方式

### 运行所有测试
```bash
mvn test -Dtest=TestRecommenderRelationService
```

### 运行特定测试方法
```bash
# 运行酒店关系列表正常流程测试
mvn test -Dtest=TestRecommenderRelationService#testGetRecommenderHotelRelationListSuccess

# 运行导游供应商列表正常流程测试
mvn test -Dtest=TestRecommenderRelationService#testGetTourGuideSupplierListSuccess

# 运行性能测试
mvn test -Dtest=TestRecommenderRelationService#testGetHotelRelationListPerformance
```

### 运行特定类别的测试
```bash
# 运行所有酒店关系相关测试（通过方法名模式匹配）
mvn test -Dtest=TestRecommenderRelationService -Dtest.methods="*Hotel*"

# 运行所有导游供应商相关测试
mvn test -Dtest=TestRecommenderRelationService -Dtest.methods="*Guide*"
```

## 测试数据要求

### 数据库准备
确保测试数据库中包含以下测试数据：

1. **用户数据**: 至少包含ID为1的有效用户
2. **推荐方数据**: 至少包含ID为1的有效推荐方
3. **酒店关系数据**: 包含一些酒店供应商关系记录
4. **导游关系数据**: 包含一些导游供应商关系记录

### 测试参数说明

#### 酒店关系查询参数
- `userId`: 用户ID（从token解析获取）
- `keyword`: 搜索关键词（供应商编码、酒店名称、供应商姓名）
- `relationStartDate/relationEndDate`: 建立关系时间范围
- `sortType`: 排序类型（Integer）：
  - `1`: 建立时间从新到旧（默认）
  - `2`: 上架服务数从高到低
  - `3`: 成单数从高到低
  - 注：都相同的情况，按该条数据的订单号升序排列
- `pageNum/pageSize`: 分页参数

#### 导游供应商查询参数
- `recommenderId`: 推荐方ID（从token解析获取）
- `productionLine`: 产线筛选（1=司机，2=导游，3=司兼导）
- `controlStatus`: 状态筛选（normal=正常，controlled=管控，all=全部）
- `keyword`: 搜索关键词（导游ID或导游姓名）
- `relationStartDate/relationEndDate`: 建立关系时间范围
- `sortType`: 排序类型（Integer）：
  - `1`: 建立时间从新到旧（默认）
  - `2`: 服务分从高到低
  - `3`: 上架服务数从高到低
  - `4`: 成单数从高到低
- `pageNum/pageSize`: 分页参数

## 测试结果分析

### 日志输出
每个测试方法都会输出：
- 测试开始标识
- 执行时间统计
- 详细的响应结果（JSON格式）

### 性能基准
- 单次查询执行时间应控制在合理范围内
- 性能测试会执行10次并计算平均执行时间
- 可根据实际业务需求调整性能基准

### 异常处理验证
- 验证服务层对无效参数的处理
- 验证边界条件的处理逻辑
- 确保异常情况下返回合理的响应结果

## 扩展建议

### 1. 添加更多测试场景
- 并发测试
- 大数据量测试
- 网络异常模拟测试

### 2. 集成测试
- 结合Controller层进行端到端测试
- 模拟真实的HTTP请求和响应

### 3. Mock测试
- 使用Mockito模拟依赖组件
- 隔离测试Service层的业务逻辑

### 4. 测试数据管理
- 使用@Transactional注解确保测试数据隔离
- 考虑使用测试数据构建器模式

## 注意事项

1. **数据依赖**: 测试依赖数据库中的真实数据，确保测试环境数据完整
2. **执行顺序**: 测试方法之间相互独立，可以任意顺序执行
3. **资源清理**: 测试不会修改数据，只进行查询操作
4. **日志级别**: 建议将测试日志级别设置为INFO以便观察测试过程
5. **超时设置**: 对于性能测试，可以设置合理的超时时间

## 维护建议

1. **定期更新**: 随着业务逻辑变更，及时更新测试用例
2. **覆盖率检查**: 定期检查测试覆盖率，确保关键逻辑都有测试覆盖
3. **性能监控**: 定期运行性能测试，监控服务性能变化
4. **文档同步**: 保持测试文档与代码同步更新
