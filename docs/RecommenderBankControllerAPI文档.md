# RecommenderBankController API文档

## 接口概述

推荐方银行账户管理接口，提供银行账户信息的查询、保存和删除功能。

**基础路径**: `/recommender/bank`

**认证方式**: 需要在请求头中携带Authorization token

---

## 1. 获取推荐方银行账户信息

### 接口基本信息
- **接口路径**: `GET /recommender/bank`
- **接口描述**: 获取当前推荐方的银行账户信息
- **认证要求**: 需要Authorization token

### 请求参数

#### 请求头参数
```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." // 用户认证token，必填
}
```

#### 请求体参数
无需请求体参数

### 响应结果

#### 成功响应 (200)
```json
{
  "code": 200, // 响应状态码
  "message": "OK", // 响应消息
  "data": {
    "id": 1, // 银行账户ID
    "accountName": "张三", // 开户名
    "accountCertificateType": 1, // 开户证件类型：1=身份证 2=护照 3=港澳通行证 4=台胞证 5=外国人永久居留证 6=其他
    "accountCertificateTypeDesc": "身份证", // 开户证件类型描述
    "accountIdentifier": "******************", // 开户证件号码
    "accountPhone": "***********", // 开户手机号
    "bankCardNo": "622202*********0123", // 银行卡号/对公账号
    "bankName": "中国工商银行", // 开户银行
    "bankCode": "102", // 银行编号
    "branchCode": "************", // 支行编号
    "branchName": "中国工商银行广州分行", // 支行名称
    "province": "广东省", // 所在省份
    "city": "广州市", // 所在城市
    "bankAddress": "广州市天河区珠江新城", // 银行地址
    "validateStatus": 3, // 验证状态：0=未验证 1=三要素失败 2=待人工审核 3=审核通过 4=审核驳回
    "rejectReason": null, // 审核驳回原因/验证结果详情
    "auditSubmitTime": "2025-01-30T10:00:00", // 提交审核时间
    "isDraft": false, // 是否草稿：true=草稿，false=已提交审核
    "usageStatus": 1, // 使用状态：0=未使用 1=当前使用 2=历史使用 3=已废弃
    "createTime": "2025-01-30T09:00:00", // 创建时间
    "updateTime": "2025-01-30T10:00:00" // 更新时间
  }
}
```

#### 错误响应
```json
{
  "code": 401, // 错误状态码
  "message": "api.user.token.token-not-resolve-user", // 错误消息
  "data": null // 数据为空
}
```

### 错误码说明
- `401`: Token解析失败或用户ID为空

---

## 2. 保存或更新银行账户信息

### 接口基本信息
- **接口路径**: `POST /recommender/bank`
- **接口描述**: 保存或更新推荐方的银行账户信息
- **认证要求**: 需要Authorization token

### 请求参数

#### 请求头参数
```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // 用户认证token，必填
  "Content-Type": "application/json" // 请求内容类型
}
```

#### 请求体参数
```json
{
  "id": 1, // 银行账户ID（修改时需要，新增时可为空）
  "userId": 1, // 用户ID（系统自动设置，前端无需传递）
  "accountName": "张三", // 开户名，必填
  "accountCertificateType": 1, // 开户证件类型：1=身份证 2=护照 3=港澳通行证 4=台胞证 5=外国人永久居留证 6=其他（个人类型必填）
  "accountIdentifier": "******************", // 开户证件号码，最大长度50字符
  "accountPhone": "***********", // 开户手机号，必填，最大长度20字符
  "bankCardNo": "622202*********0123", // 银行卡号/对公账号，必填
  "bankName": "中国工商银行", // 开户银行，必填
  "bankCode": "102", // 银行编号
  "branchCode": "************", // 支行编号
  "branchName": "中国工商银行广州分行", // 支行名称，必填
  "province": "广东省", // 所在省份，必填
  "city": "广州市", // 所在城市，必填
  "bankAddress": "广州市天河区珠江新城" // 银行地址
}
```

### 响应结果

#### 成功响应 (200)
```json
{
  "code": 200,  // 响应状态码
  "message": "OK",  // 响应消息
  "data": ********* // 银行账户ID
} 
```

#### 错误响应
```json
{
  "code": 400, // 错误状态码
  "message": "开户名不能为空", // 参数验证错误消息
  "data": null // 数据为空
}
```

```json
{
  "code": 401, // 错误状态码
  "message": "api.user.token.token-not-resolve-user", // Token错误消息
  "data": null // 数据为空
}
```

### 错误码说明
- `400`: 参数验证失败（如必填字段为空、字段长度超限等）
- `401`: Token解析失败或用户ID为空
- `500`: 服务器内部错误

### 字段验证规则
- `accountName`: 必填，不能为空
- `accountPhone`: 必填，不能为空，最大长度20字符
- `bankCardNo`: 必填，不能为空
- `bankName`: 必填，不能为空
- `branchName`: 必填，不能为空
- `province`: 必填，不能为空
- `city`: 必填，不能为空
- `accountIdentifier`: 最大长度50字符

---

## 3. 删除银行账户信息

### 接口基本信息
- **接口路径**: `GET /recommender/bank/{bankAccountId}`
- **接口描述**: 删除指定的银行账户信息
- **认证要求**: 需要Authorization token

### 请求参数

#### 请求头参数
```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." // 用户认证token，必填
}
```

#### 路径参数
- `bankAccountId`: 银行账户ID，必填，Long类型

#### 请求示例
```
GET /recommender/bank/123
```

### 响应结果

#### 成功响应 (200)
```json
{
  "code": 200, // 响应状态码
  "message": "OK", // 响应消息
  "data": true // 删除结果：true=成功，false=失败
}
```

#### 错误响应
```json
{
  "code": 401, // 错误状态码
  "message": "api.user.token.token-not-resolve-user", // 错误消息
  "data": null // 数据为空
}
```

```json
{
  "code": 404, // 错误状态码
  "message": "银行账户不存在", // 错误消息
  "data": null // 数据为空
}
```

### 错误码说明
- `401`: Token解析失败或用户ID为空
- `404`: 银行账户不存在或无权限删除
- `500`: 服务器内部错误

---

## 调用示例

### 1. 获取银行账户信息示例

```bash
curl -X GET "http://localhost:8080/recommender/bank" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 2. 保存银行账户信息示例

```bash
curl -X POST "http://localhost:8080/recommender/bank" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "accountName": "张三",
    "accountCertificateType": 1,
    "accountIdentifier": "******************",
    "accountPhone": "***********",
    "bankCardNo": "622202*********0123",
    "bankName": "中国工商银行",
    "branchName": "中国工商银行广州分行",
    "province": "广东省",
    "city": "广州市"
  }'
```

### 3. 删除银行账户信息示例

```bash
curl -X GET "http://localhost:8080/recommender/bank/123" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

---

## 注意事项

1. **认证要求**: 所有接口都需要在请求头中携带有效的Authorization token
2. **数据验证**: 保存接口会进行严格的参数验证，请确保必填字段不为空
3. **权限控制**: 用户只能操作自己的银行账户信息
4. **数据安全**: 银行卡号等敏感信息会进行加密存储
5. **审核流程**: 银行账户信息提交后需要经过审核才能生效

## 枚举值说明

### 开户证件类型 (accountCertificateType)
- `1`: 身份证
- `2`: 护照
- `3`: 港澳通行证
- `4`: 台胞证
- `5`: 外国人永久居留证
- `6`: 其他

### 验证状态 (validateStatus)
- `0`: 未验证
- `1`: 三要素失败
- `2`: 待人工审核
- `3`: 审核通过
- `4`: 审核驳回

### 使用状态 (usageStatus)
- `0`: 未使用
- `1`: 当前使用
- `2`: 历史使用
- `3`: 已废弃
