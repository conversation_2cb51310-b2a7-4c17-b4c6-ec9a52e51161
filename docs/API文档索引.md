# 推荐方系统 API 文档索引

## 文档概述

本文档集合包含推荐方系统所有Controller的完整API接口文档，每个Controller都有独立的文档文件，详细描述了接口的请求参数、响应结果、调用示例等信息。

## 系统信息

- **项目名称**: somytrip-recommend-system
- **基础域名**: `http://localhost:8080` (开发环境)
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

---

## API 文档列表

### 1. RecommenderBankController - 银行账户管理
**文档文件**: [RecommenderBankControllerAPI文档.md](./RecommenderBankControllerAPI文档.md)

**功能概述**: 推荐方银行账户信息的管理，包括查询、保存和删除功能。

**接口列表**:
- `GET /recommender/bank` - 获取推荐方银行账户信息
- `POST /recommender/bank` - 保存或更新银行账户信息
- `GET /recommender/bank/{bankAccountId}` - 删除银行账户信息

**主要特性**:
- 支持个人和企业银行账户
- 银行卡信息加密存储
- 三要素验证和人工审核
- 完整的审核状态管理

---

### 2. RecommenderController - 认证资料管理
**文档文件**: [RecommenderControllerAPI文档.md](./RecommenderControllerAPI文档.md)

**功能概述**: 推荐方认证资料的管理，包括个人和企业认证信息的保存和审核详情查询。

**接口列表**:
- `POST /recommender/profile` - 保存/更新认证资料
- `GET /recommender/audit-detail/{recommenderId}` - 查询审核详情

**主要特性**:
- 支持个人和企业两种类型
- 身份证件和营业执照上传
- 草稿保存和正式提交
- 完整的审核流程管理

---

### 3. RecommenderOrderController - 订单管理
**文档文件**: [RecommenderOrderControllerAPI文档.md](./RecommenderOrderControllerAPI文档.md)

**功能概述**: 推荐方订单列表的查询，支持多维度筛选和分页。

**接口列表**:
- `POST /recommender/order/list` - 查询推荐方订单列表

**主要特性**:
- 支持酒店和导游订单查询
- 多维度筛选（时间、类型、关键词）
- 分页和排序功能
- 分佣金额统计

---

### 4. RecommenderRelationController - 关系管理
**文档文件**: [RecommenderRelationControllerAPI文档.md](./RecommenderRelationControllerAPI文档.md)

**功能概述**: 推荐方与酒店、导游供应商关系的管理和查询。

**接口列表**:
- `POST /recommender/relation/hotel/list` - 查询推荐方酒店关系列表
- `POST /recommender/relation/guide/list` - 查询推荐方导游供应商列表

**主要特性**:
- 酒店关系管理（房型数量、成单统计）
- 导游关系管理（产线筛选、服务评分）
- 关系状态管理
- 详细的统计信息

---

## 通用规范

### 1. 认证机制
所有接口都需要在请求头中携带Authorization token：
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. 响应格式
所有接口都使用统一的响应格式：
```json
{
  "code": 200,        // 状态码：200=成功，其他=失败
  "message": "OK",    // 响应消息
  "data": {}          // 响应数据，失败时为null
}
```

### 3. 错误码说明
- `200`: 请求成功
- `400`: 参数错误或业务逻辑错误
- `401`: 认证失败或token无效
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 4. 日期时间格式
- **日期格式**: `yyyy-MM-dd` (如: 2025-01-30)
- **日期时间格式**: `yyyy-MM-ddTHH:mm:ss` (如: 2025-01-30T10:00:00)

### 5. 分页参数
```json
{
  "pageNum": 1,       // 页码，从1开始
  "pageSize": 20,     // 每页大小
  "total": 100,       // 总记录数
  "totalPages": 5,    // 总页数
  "hasNext": true,    // 是否有下一页
  "hasPrevious": false // 是否有上一页
}
```

### 6. 数据脱敏规则
- **手机号**: 138****8000 (中间4位用*替换)
- **身份证号**: 440102****1234 (中间部分用*替换)
- **银行卡号**: 6222****890123 (中间部分用*替换)

---

## 业务流程

### 1. 推荐方注册流程
1. 提交认证资料 (`POST /recommender/profile`)
2. 等待审核
3. 查询审核结果 (`GET /recommender/audit-detail/{recommenderId}`)
4. 审核通过后配置银行账户 (`POST /recommender/bank`)

### 2. 关系建立流程
1. 系统自动建立推荐方与供应商的关系
2. 查询酒店关系 (`POST /recommender/relation/hotel/list`)
3. 查询导游关系 (`POST /recommender/relation/guide/list`)

### 3. 订单查询流程
1. 查询订单列表 (`POST /recommender/order/list`)
2. 支持多维度筛选和统计
3. 查看分佣金额详情

---

## 开发注意事项

### 1. 接口调用频率
- 建议控制接口调用频率，避免频繁请求
- 分页查询时合理设置每页大小

### 2. 数据安全
- 所有敏感数据都已进行加密存储
- 接口返回的敏感信息已脱敏处理
- 严格的权限控制，用户只能访问自己的数据

### 3. 性能优化
- 大数据量查询时建议使用分页
- 合理使用筛选条件减少数据量
- 避免频繁的全量数据查询

### 4. 错误处理
- 所有接口都有完善的错误处理机制
- 详细的错误信息帮助定位问题
- 建议在客户端实现重试机制

---

## 联系信息

- **开发团队**: 推荐方系统开发组
- **技术支持**: 请联系相关技术人员
- **文档版本**: v1.0
- **最后更新**: 2025-01-30

---

## 更新日志

### v1.0 (2025-01-30)
- 初始版本发布
- 完成所有Controller的API文档
- 包含完整的接口说明和调用示例
- 添加通用规范和业务流程说明

---

## 附录

### A. 常用枚举值

#### 推荐方类型
- `1`: 个人
- `2`: 企业

#### 证件类型
- `1`: 身份证
- `2`: 护照
- `3`: 港澳通行证
- `4`: 台胞证
- `5`: 外国人永久居留证
- `6`: 其他

#### 订单类型
- `1`: 酒店订单
- `2`: 导游订单

#### 审核状态
- `0`: 待审核
- `1`: 审核通过
- `2`: 审核未通过

#### 关系状态
- `1`: 正常
- `2`: 管控/暂停

### B. 测试环境信息
- **环境地址**: http://localhost:8080
- **测试账号**: 请联系管理员获取
- **数据库**: 使用测试数据库，数据会定期清理

### C. 生产环境部署
- **域名**: 待定
- **SSL证书**: 生产环境强制使用HTTPS
- **监控**: 完整的接口监控和告警机制
