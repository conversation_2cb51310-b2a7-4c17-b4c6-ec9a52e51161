# IdentityVerificationController 频率限制升级说明

## 修改概述

对`IdentityVerificationController.java`中的`recognizeCertificate`方法进行了频率限制升级，从单一的分钟级限制升级为双重限制机制，以更好地控制API调用频率。

## 修改详情

### 1. 常量定义更新

#### 修改前
```java
/**
 * OCR证件信息redis键前缀
 */
private final String CERTIFICATE_REDISKEY = "certificate:";

/**
 * 同一用户1分钟内最多请求3次
 */
private final int MAX_LIMIT = 3;

/**
 * redis过期时间（秒）
 */
private final int EXPIRE_TIME = 60;
```

#### 修改后
```java
/**
 * OCR证件信息redis键前缀 - 分钟级限制
 */
private final String CERTIFICATE_MINUTE_REDISKEY = "certificate:minute:";

/**
 * OCR证件信息redis键前缀 - 天级限制
 */
private final String CERTIFICATE_DAY_REDISKEY = "certificate:day:";

/**
 * 同一用户1分钟内最多请求15次
 */
private final int MAX_MINUTE_LIMIT = 15;

/**
 * 同一用户1天内最多请求30次
 */
private final int MAX_DAY_LIMIT = 30;

/**
 * 分钟级redis过期时间（秒）
 */
private final int MINUTE_EXPIRE_TIME = 60;

/**
 * 天级redis过期时间（秒）
 */
private final int DAY_EXPIRE_TIME = 24 * 60 * 60;
```

### 2. Redis键设计

#### 分钟级限制键格式
```
certificate:minute:{type}:{userId}
```
- 示例：`certificate:minute:1:12345`
- 过期时间：60秒

#### 天级限制键格式
```
certificate:day:{type}:{userId}:{date}
```
- 示例：`certificate:day:1:12345:2025-01-30`
- 过期时间：24小时（86400秒）

### 3. 频率限制逻辑升级

#### 双重检查机制
1. **分钟级检查**：同一用户1分钟内最多请求15次
2. **天级检查**：同一用户1天内最多请求30次
3. **任一超限即拒绝**：两个维度的限制任何一个超限都会拒绝请求

#### 错误提示优化
- **分钟级超限**：
  ```
  请求过于频繁，1分钟内最多请求15次，请XX秒后再试
  ```
- **天级超限**：
  ```
  今日请求次数已达上限（30次），请X小时X分钟后再试
  ```

### 4. 核心代码逻辑

#### Redis计数器管理
```java
// 分钟级计数器
String minuteRedisKey = CERTIFICATE_MINUTE_REDISKEY + type + ":" + userId;
minuteCount = stringRedisTemplate.opsForValue().increment(minuteRedisKey, 1);

// 天级计数器
String dayRedisKey = CERTIFICATE_DAY_REDISKEY + type + ":" + userId + ":" + 
                    java.time.LocalDate.now().toString();
dayCount = stringRedisTemplate.opsForValue().increment(dayRedisKey, 1);
```

#### 异常处理保持不变
```java
try {
    // Redis操作
} catch (Exception e) {
    // Redis操作异常，记录日志但允许请求继续
    log.info("Redis操作异常，无法进行防刷限制，用户ID: {}", userId);
    minuteCount = 1L;
    dayCount = 1L;
}
```

## 技术特性

### 1. 保持原有技术栈
- ✅ 继续使用Redis + StringRedisTemplate
- ✅ 保持原有的异常处理机制
- ✅ 保持原有的日志记录风格
- ✅ 保持原有的返回格式

### 2. Redis键命名规范
- **前缀区分**：使用不同前缀区分分钟级和天级限制
- **层次结构**：`类型:时间维度:证件类型:用户ID[:日期]`
- **唯一性保证**：通过用户ID和日期确保键的唯一性

### 3. 过期时间管理
- **分钟级**：60秒自动过期
- **天级**：24小时自动过期
- **自动清理**：Redis自动清理过期键，无需手动维护

### 4. 错误提示人性化
- **明确限制类型**：区分是分钟限制还是天限制
- **剩余时间显示**：精确显示需要等待的时间
- **友好的时间格式**：天级限制显示"X小时X分钟"格式

## 业务影响

### 1. 限制调整对比

| 维度 | 修改前 | 修改后 | 变化 |
|------|--------|--------|------|
| 分钟级 | 3次/分钟 | 15次/分钟 | 放宽5倍 |
| 天级 | 无限制 | 30次/天 | 新增限制 |

### 2. 用户体验提升
- **更灵活的短期使用**：分钟级限制从3次提升到15次
- **合理的长期控制**：新增天级30次限制，防止滥用
- **清晰的错误提示**：用户明确知道超限原因和等待时间

### 3. 系统保护增强
- **API成本控制**：通过天级限制控制整体API调用成本
- **防刷保护**：双重限制机制更好地防止恶意刷接口
- **资源合理分配**：避免单个用户过度占用API资源

## 测试建议

### 1. 功能测试
```bash
# 测试分钟级限制
for i in {1..16}; do
  curl -X POST "http://localhost:8080/api/certificate/ocr/recognize" \
    -H "Authorization: Bearer {token}" \
    -F "file=@test.jpg" \
    -F "type=1"
  echo "Request $i completed"
done

# 测试天级限制（需要模拟30次请求）
```

### 2. Redis键验证
```bash
# 查看Redis中的键
redis-cli keys "certificate:minute:*"
redis-cli keys "certificate:day:*"

# 查看键的过期时间
redis-cli TTL "certificate:minute:1:12345"
redis-cli TTL "certificate:day:1:12345:2025-01-30"
```

### 3. 异常场景测试
- Redis服务不可用时的降级处理
- Redis操作超时的异常处理
- 并发请求的计数器准确性

## 监控建议

### 1. 关键指标监控
- 分钟级限制触发次数
- 天级限制触发次数
- Redis操作异常次数
- 用户请求分布情况

### 2. 告警设置
- Redis连接异常告警
- 频率限制触发率过高告警
- API调用量异常增长告警

## 注意事项

### 1. 部署注意事项
- 确保Redis服务稳定运行
- 验证Redis键的过期时间设置正确
- 测试异常情况下的降级处理

### 2. 运维注意事项
- 定期清理Redis中的过期键（自动清理）
- 监控Redis内存使用情况
- 关注API调用量的变化趋势

### 3. 业务注意事项
- 向用户说明新的频率限制规则
- 提供合理的错误提示和引导
- 考虑为VIP用户提供更高的限制额度

## 总结

本次升级实现了更加灵活和合理的频率限制机制：
- **短期更宽松**：分钟级限制从3次提升到15次，满足用户正常使用需求
- **长期有控制**：新增天级30次限制，防止API滥用
- **体验更友好**：提供清晰的错误提示和等待时间
- **系统更稳定**：保持原有的异常处理和降级机制

这种双重限制机制既保证了用户的正常使用体验，又有效控制了API调用成本和系统资源消耗。
