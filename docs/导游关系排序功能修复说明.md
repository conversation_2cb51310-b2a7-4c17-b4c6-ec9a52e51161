# 导游关系排序功能修复说明

## 问题描述

在`getRecommenderGuideRelationList`查询中发现排序功能存在问题：

### 1. 排序类型定义不一致
**枚举类定义**：
- `1`: 建立时间从新到旧（默认）
- `2`: 服务分从高到低
- `3`: 上架服务数从高到低
- `4`: 成单数从高到低

**XML映射文件实现**：
- `1`: 建立时间排序 ✅
- `2`: 导游姓名排序 ❌ (错误)
- 缺少服务分、上架服务数、成单数排序

### 2. 缺少统计字段
XML查询中缺少`service_count`和`order_count`字段，导致无法进行相应的排序。

### 3. 测试覆盖不完整
测试方法中只测试了3种排序类型，缺少第4种（成单数排序）。

## 修复方案

### 1. 修复XML映射文件排序逻辑

#### 1.1 更新排序SQL
**修复前：**
```xml
<choose>
    <when test="query.sortType == 1">
        ORDER BY rr.create_time DESC
    </when>
    <when test="query.sortType == 2">
        ORDER BY gi.guide_name ASC  <!-- 错误的排序字段 -->
    </when>
    <otherwise>
        ORDER BY rr.create_time DESC
    </otherwise>
</choose>
```

**修复后：**
```xml
<choose>
    <when test="query.sortType == 2">
        ORDER BY tgs.service_score DESC, rr.create_time DESC, rr.id ASC
    </when>
    <when test="query.sortType == 3">
        ORDER BY service_count DESC, rr.create_time DESC, rr.id ASC
    </when>
    <when test="query.sortType == 4">
        ORDER BY order_count DESC, rr.create_time DESC, rr.id ASC
    </when>
    <otherwise>
        ORDER BY rr.create_time DESC, rr.id ASC
    </otherwise>
</choose>
```

#### 1.2 添加统计字段查询
**在BaseQuery中添加：**
```xml
-- 上架服务数统计
COALESCE(service_stats.service_count, 0) as service_count,
-- 成单数统计
COALESCE(order_stats.order_count, 0) as order_count
```

**添加统计子查询：**
```xml
-- 左连接统计上架服务数
LEFT JOIN (
    SELECT guide_id, COUNT(*) as service_count
    FROM <include refid="TABLE_TOUR_GUIDE_SERVICE_INFO"/>
    WHERE service_status = 1 AND is_del = 0
    GROUP BY guide_id
) service_stats ON tgs.id = service_stats.guide_id

-- 左连接统计成单数
LEFT JOIN (
    SELECT guide_id, COUNT(*) as order_count
    FROM <include refid="TABLE_TOUR_GUIDE_ORDER"/>
    WHERE order_status = 'COMPLETED' AND is_del = 0
    GROUP BY guide_id
) order_stats ON tgs.id = order_stats.guide_id
```

#### 1.3 更新ResultMap映射
```xml
<result column="service_count" property="serviceCount" />
<result column="order_count" property="orderCount" />
```

### 2. 排序逻辑详解

#### 2.1 排序类型1：建立时间从新到旧（默认）
```sql
ORDER BY rr.create_time DESC, rr.id ASC
```
- **主要排序**：按关系建立时间降序
- **次要排序**：相同时间时按ID升序（保证排序稳定性）

#### 2.2 排序类型2：服务分从高到低
```sql
ORDER BY tgs.service_score DESC, rr.create_time DESC, rr.id ASC
```
- **主要排序**：按导游服务分降序
- **次要排序**：相同服务分时按建立时间降序
- **第三排序**：最终按ID升序

#### 2.3 排序类型3：上架服务数从高到低
```sql
ORDER BY service_count DESC, rr.create_time DESC, rr.id ASC
```
- **主要排序**：按上架服务数降序
- **次要排序**：相同服务数时按建立时间降序
- **第三排序**：最终按ID升序

#### 2.4 排序类型4：成单数从高到低
```sql
ORDER BY order_count DESC, rr.create_time DESC, rr.id ASC
```
- **主要排序**：按成单数降序
- **次要排序**：相同成单数时按建立时间降序
- **第三排序**：最终按ID升序

### 3. 统计字段说明

#### 3.1 上架服务数（service_count）
- **统计来源**：`tour_guide_service_info`表
- **统计条件**：`service_status = 1 AND is_del = 0`
- **统计逻辑**：按`guide_id`分组计数

#### 3.2 成单数（order_count）
- **统计来源**：`tour_guide_order`表
- **统计条件**：`order_status = 'COMPLETED' AND is_del = 0`
- **统计逻辑**：按`guide_id`分组计数

### 4. 测试更新

#### 4.1 更新测试方法
```java
// 排序类型：1-建立时间从新到旧（默认）、2-服务分从高到低、3-上架服务数从高到低、4-成单数从高到低
Integer[] sortTypes = {1, 2, 3, 4};
```

#### 4.2 修复测试数据设置
```java
// 修复前
queryDto.setRecommenderId(1L);

// 修复后
queryDto.setUserId(1L);
```

## 技术实现细节

### 1. 性能优化
- **索引建议**：
  - `tour_guide_service_info(guide_id, service_status, is_del)`
  - `tour_guide_order(guide_id, order_status, is_del)`
  - `recommender_relation(create_time, id)`

### 2. 数据一致性
- 使用`COALESCE`函数处理NULL值，确保统计字段不为空
- 统计子查询使用LEFT JOIN，确保没有统计数据的导游也能显示

### 3. 排序稳定性
- 所有排序都使用`rr.id ASC`作为最终排序条件
- 确保相同排序值的记录有稳定的排序顺序

## 修复验证

### 1. 功能验证
```java
@Test
public void testGetGuideSupplierListWithSortFilter() {
    Integer[] sortTypes = {1, 2, 3, 4};
    for (Integer sortType : sortTypes) {
        // 测试每种排序类型
        queryDto.setSortType(sortType);
        ResponseResult<RecommenderGuideRelationListVo> result = 
            recommenderRelationService.getRecommenderGuideRelationList(queryDto);
        
        // 验证排序结果
        assertThat(result.getData().getSortType()).isEqualTo(sortType);
        assertThat(result.getData().getSortDescription()).isNotBlank();
    }
}
```

### 2. 性能验证
- 监控查询执行时间
- 检查SQL执行计划
- 验证索引使用情况

### 3. 数据验证
- 验证统计字段的准确性
- 检查排序结果的正确性
- 确认边界条件处理

## 排序效果示例

### 示例1：服务分排序（sortType=2）
```json
{
  "suppliers": [
    {"guideName": "张三", "serviceScore": 95.5, "serviceCount": 10, "orderCount": 8},
    {"guideName": "李四", "serviceScore": 92.0, "serviceCount": 8, "orderCount": 6},
    {"guideName": "王五", "serviceScore": 88.5, "serviceCount": 12, "orderCount": 10}
  ]
}
```

### 示例2：上架服务数排序（sortType=3）
```json
{
  "suppliers": [
    {"guideName": "王五", "serviceScore": 88.5, "serviceCount": 12, "orderCount": 10},
    {"guideName": "张三", "serviceScore": 95.5, "serviceCount": 10, "orderCount": 8},
    {"guideName": "李四", "serviceScore": 92.0, "serviceCount": 8, "orderCount": 6}
  ]
}
```

### 示例3：成单数排序（sortType=4）
```json
{
  "suppliers": [
    {"guideName": "王五", "serviceScore": 88.5, "serviceCount": 12, "orderCount": 10},
    {"guideName": "张三", "serviceScore": 95.5, "serviceCount": 10, "orderCount": 8},
    {"guideName": "李四", "serviceScore": 92.0, "serviceCount": 8, "orderCount": 6}
  ]
}
```

## 注意事项

### 1. 数据库表依赖
- 确保`tour_guide_service_info`和`tour_guide_order`表存在
- 验证表结构和字段名称的正确性
- 检查表之间的关联关系

### 2. 性能影响
- 统计子查询可能影响查询性能
- 建议在相关字段上创建合适的索引
- 考虑使用缓存优化频繁查询

### 3. 数据准确性
- 统计数据的实时性取决于业务数据的更新频率
- 可能需要定期更新统计信息
- 考虑使用物化视图或定时任务优化

## 总结

本次修复解决了导游关系排序功能的以下问题：

1. **排序类型一致性**：XML实现与枚举定义完全一致
2. **功能完整性**：支持所有4种排序类型
3. **数据准确性**：添加了必要的统计字段查询
4. **性能优化**：使用高效的统计子查询
5. **测试完备性**：覆盖了所有排序类型的测试

修复后的排序功能能够正确按照服务分、上架服务数、成单数进行排序，为用户提供了更好的数据查看体验。
