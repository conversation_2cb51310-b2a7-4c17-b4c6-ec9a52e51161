# 银行卡验证服务架构重构说明

## 重构概述

将银行卡验证服务的频率限制逻辑从Controller层迁移到Service层，实现更好的代码分层和可测试性。

## 重构目标

1. **分离关注点**：Controller只负责HTTP请求处理，Service负责业务逻辑
2. **提高可测试性**：Service层方法可以独立进行单元测试
3. **增强复用性**：频率限制逻辑可以被其他地方复用
4. **改善代码结构**：遵循单一职责原则

## 修改文件清单

### 1. 新增文件
- `src/main/java/tripai/recommend/system/exception/AuthenticationException.java` - 认证异常类

### 2. 修改文件
- `src/main/java/tripai/recommend/system/service/TencentBankCardVerifyService.java` - Service接口
- `src/main/java/tripai/recommend/system/service/impl/TencentBankCardVerifyServiceImpl.java` - Service实现
- `src/main/java/tripai/recommend/system/controller/BankCardVerifyController.java` - Controller

## 详细修改内容

### 1. 新增认证异常类

#### AuthenticationException.java
```java
public class AuthenticationException extends RuntimeException {
    private final int code;
    
    public AuthenticationException(int code, String message) {
        super(message);
        this.code = code;
    }
    
    public int getCode() {
        return code;
    }
}
```

**作用**：处理认证相关的异常，支持携带HTTP状态码。

### 2. Service接口扩展

#### TencentBankCardVerifyService.java
```java
// 新增方法
BankCardVerifyResultVo verifyBankCardWithRateLimit(String token, BankCardVerifyDto verifyDto);
```

**变更**：
- 保留原有的`verifyBankCard`方法（向后兼容）
- 新增`verifyBankCardWithRateLimit`方法（带频率限制）

### 3. Service实现类重构

#### TencentBankCardVerifyServiceImpl.java

##### 新增依赖和常量
```java
// 频率限制相关常量
private final String BANK_VERIFY_MINUTE_REDISKEY = "bank_verify:minute:";
private final String BANK_VERIFY_DAY_REDISKEY = "bank_verify:day:";
private final int MAX_MINUTE_LIMIT = 15;
private final int MAX_DAY_LIMIT = 30;
private final int MINUTE_EXPIRE_TIME = 60;
private final int DAY_EXPIRE_TIME = 24 * 60 * 60;

// 新增依赖注入
@Resource
private StringRedisTemplate stringRedisTemplate;
```

##### 新增核心方法
```java
// 主要业务方法
public BankCardVerifyResultVo verifyBankCardWithRateLimit(String token, BankCardVerifyDto verifyDto)

// 辅助方法
private UserInfoVo parseUserFromToken(String token)
private BankCardVerifyResultVo checkRateLimit(Long userId)
```

##### 业务流程
```
Token解析 → 用户验证 → 频率限制检查 → 银行卡验证 → 返回结果
```

### 4. Controller简化

#### BankCardVerifyController.java

##### 移除内容
- 所有频率限制相关的常量定义
- Redis相关的依赖注入
- Token解析逻辑
- 频率限制检查逻辑

##### 简化后的方法
```java
@PostMapping("/verify")
public ResponseResult<BankCardVerifyResultVo> verifyBankCard(
    @RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token,
    @RequestBody @Valid BankCardVerifyDto verifyDto) {
    
    try {
        BankCardVerifyResultVo result = tencentBankCardVerifyService.verifyBankCardWithRateLimit(token, verifyDto);
        return ResponseResult.ok(result);
    } catch (AuthenticationException e) {
        return ResponseResult.fail(e.getCode(), e.getMessage());
    } catch (Exception e) {
        log.error("银行卡验证失败", e);
        return ResponseResult.fail("银行卡验证失败: " + e.getMessage());
    }
}
```

## 架构对比

### 重构前架构
```
Controller层：
├── HTTP请求处理
├── Token解析和验证
├── Redis频率限制检查
├── 调用Service进行银行卡验证
└── 返回响应

Service层：
└── 银行卡验证业务逻辑
```

### 重构后架构
```
Controller层：
├── HTTP请求处理
├── 调用Service方法
├── 异常处理
└── 返回响应

Service层：
├── Token解析和验证
├── Redis频率限制检查
├── 银行卡验证业务逻辑
└── 完整的业务流程编排
```

## 技术优势

### 1. 分层清晰
- **Controller层**：专注于HTTP协议处理
- **Service层**：专注于业务逻辑实现
- **异常层**：统一的异常处理机制

### 2. 可测试性提升
```java
// Service层可以独立测试
@Test
public void testVerifyBankCardWithRateLimit() {
    // 可以直接测试Service方法，无需启动Web容器
    BankCardVerifyResultVo result = service.verifyBankCardWithRateLimit(token, dto);
    // 断言验证
}
```

### 3. 代码复用性
- 频率限制逻辑可以被其他Service复用
- Token解析逻辑可以被其他业务场景使用

### 4. 单一职责
- Controller只负责HTTP请求响应
- Service负责完整的业务逻辑
- 每个类的职责更加明确

## 向后兼容性

### 1. 接口兼容
- 保留原有的`verifyBankCard`方法
- HTTP接口签名保持不变
- 客户端无需修改

### 2. 行为兼容
- 频率限制规则保持不变（15次/分钟，30次/天）
- 错误提示格式保持不变
- Redis键设计保持不变

## 测试策略

### 1. 单元测试
```java
// Service层单元测试
@Test
public void testTokenValidation() { }

@Test
public void testRateLimitMinute() { }

@Test
public void testRateLimitDay() { }

@Test
public void testBankCardVerification() { }
```

### 2. 集成测试
```java
// Controller层集成测试
@Test
public void testVerifyBankCardEndpoint() { }

@Test
public void testAuthenticationException() { }
```

### 3. 性能测试
- Redis操作性能测试
- 并发请求处理测试
- 频率限制准确性测试

## 部署注意事项

### 1. 依赖检查
- 确保Redis服务正常运行
- 验证JWT工具类正常工作
- 检查异常处理机制

### 2. 配置验证
- Redis连接配置
- 腾讯云API配置
- 日志配置

### 3. 监控指标
- Service层方法调用次数
- 频率限制触发次数
- 异常发生频率
- 响应时间分布

## 性能影响

### 1. 正面影响
- Controller层逻辑简化，响应更快
- Service层逻辑集中，便于优化
- 异常处理更加统一

### 2. 注意事项
- Service层方法调用链稍微增长
- 异常抛出和捕获的开销
- 需要关注内存使用情况

## 后续优化建议

### 1. 缓存优化
- 考虑添加用户信息缓存
- 优化Redis操作性能
- 实现更智能的频率限制算法

### 2. 监控增强
- 添加详细的业务指标监控
- 实现实时告警机制
- 提供可视化的监控面板

### 3. 扩展性考虑
- 支持动态调整频率限制参数
- 支持不同用户级别的差异化限制
- 考虑分布式环境下的一致性

## 总结

通过这次架构重构，我们实现了：

1. **更清晰的代码分层**：Controller专注HTTP处理，Service专注业务逻辑
2. **更好的可测试性**：Service层方法可以独立测试
3. **更高的代码复用性**：业务逻辑可以被其他地方复用
4. **更强的可维护性**：单一职责原则，代码更易维护

这次重构为后续的功能扩展和性能优化奠定了良好的基础。
