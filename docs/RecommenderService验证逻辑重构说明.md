# RecommenderService验证逻辑重构说明

## 重构概述

将原本混乱的验证逻辑重构为清晰、可维护的结构，解决了代码重复、逻辑混乱、可读性差等问题。

## 原有问题分析

### 1. 逻辑混乱
- 个人和企业验证逻辑混在一个方法中
- 嵌套层级过深，难以理解
- 验证顺序不清晰

### 2. 重复代码
- 银行信息验证有重复逻辑
- 相似的字段验证重复编写
- 错误消息格式不统一

### 3. 可维护性差
- 修改一个类型的验证需要在多个地方修改
- 新增验证规则困难
- 测试覆盖困难

### 4. 可读性差
- 方法过长（100+行）
- 职责不单一
- 缺少清晰的分层结构

## 重构方案

### 1. 分层验证结构

```
validateRequest (主入口)
├── validateBasicFields (基础字段验证)
├── validatePersonalRecommender (个人类型验证)
│   └── validatePersonalBankConsistency (个人银行一致性)
├── validateEnterpriseRecommender (企业类型验证)
│   ├── validateEnterpriseBankConsistency (企业银行一致性)
│   └── validateEnterpriseRequiredBankFields (企业必填字段)
└── validateBankInfo (银行信息验证)
    ├── validateCommonBankFields (通用银行字段)
    ├── validatePersonalBankFields (个人银行字段)
    └── validateEnterpriseBankFields (企业银行字段)
```

### 2. 方法职责划分

| 方法名 | 职责 | 验证内容 |
|--------|------|----------|
| `validateRequest` | 主入口，协调各个验证步骤 | 调用其他验证方法 |
| `validateBasicFields` | 验证基础字段 | 姓名、证件号、银行信息非空 |
| `validatePersonalRecommender` | 验证个人类型特有逻辑 | 姓名长度、身份证格式、证件类型、照片 |
| `validateEnterpriseRecommender` | 验证企业类型特有逻辑 | 企业名称长度、信用代码、营业执照 |
| `validatePersonalBankConsistency` | 验证个人与银行信息一致性 | 姓名一致、身份证号一致 |
| `validateEnterpriseBankConsistency` | 验证企业与银行信息一致性 | 企业名称一致、纳税人识别号一致 |
| `validateBankInfo` | 验证银行信息 | 协调通用和特定字段验证 |
| `validateCommonBankFields` | 验证银行通用字段 | 开户名、手机号、银行卡号等 |
| `validatePersonalBankFields` | 验证个人银行特定字段 | 证件类型、证件号格式 |
| `validateEnterpriseBankFields` | 验证企业银行特定字段 | 统一社会信用代码 |

## 重构前后对比

### 重构前（问题代码）
```java
private void validateRequest(RecommenderReqDto dto) {
    // 100+行的混乱逻辑
    if (StrUtil.isBlank(dto.getName())) {
        throw new BusinessException("推荐方名称不能为空");
    }
    // ... 更多混乱的嵌套逻辑
    if (dto.getType() == 1) {
        // 个人验证逻辑
        if (!dto.getIsDraft() && (dto.getIdCardFrontUrl() == null || dto.getIdCardBackUrl() == null)) {
            throw new BusinessException("个人推荐方必须上传身份证正反面照片");
        }
        // ... 更多嵌套
        if (dto.getBankInfo() == null) {
            throw new BusinessException("推荐方银行卡信息不能为空");
        }
        // ... 重复的银行验证逻辑
    } else if (dto.getType() == 2) {
        // 企业验证逻辑，又是一堆嵌套
        // ... 重复的银行验证逻辑
    }
}
```

### 重构后（清晰代码）
```java
private void validateRequest(RecommenderReqDto dto) {
    log.info("验证请求参数开始，参数：{}", JSON.toJSONString(dto));

    // 1. 基础字段验证
    validateBasicFields(dto);

    // 2. 根据类型进行专门验证
    if (dto.getType() == 1) {
        validatePersonalRecommender(dto);
    } else if (dto.getType() == 2) {
        validateEnterpriseRecommender(dto);
    } else {
        throw new BusinessException("推荐方类型无效");
    }

    // 3. 银行信息验证
    validateBankInfo(dto.getBankInfo(), dto.getType());

    log.info("验证请求参数完成");
}
```

## 重构优势

### 1. 清晰的结构
- **单一职责**：每个方法只负责一种验证
- **分层明确**：基础验证 → 类型验证 → 银行验证
- **逻辑清晰**：验证流程一目了然

### 2. 消除重复代码
- **通用字段提取**：`validateCommonBankFields`
- **一致性验证分离**：个人和企业分别处理
- **错误消息统一**：格式一致的异常信息

### 3. 易于维护
- **独立修改**：修改个人验证不影响企业验证
- **易于扩展**：新增验证规则只需添加对应方法
- **便于测试**：每个方法可以独立测试

### 4. 提高可读性
- **方法名语义化**：一看就知道验证什么
- **减少嵌套**：最多2层嵌套
- **注释清晰**：每个方法都有明确说明

## 验证流程图

```
开始验证
    ↓
验证基础字段 (姓名、证件号、银行信息非空)
    ↓
判断推荐方类型
    ↓
┌─────────────────┬─────────────────┐
│   个人类型 (1)    │   企业类型 (2)    │
│                 │                 │
│ 验证个人特有字段   │ 验证企业特有字段   │
│ - 姓名长度       │ - 企业名称长度    │
│ - 身份证格式     │ - 信用代码格式    │
│ - 证件类型       │ - 营业执照       │
│ - 身份证照片     │                 │
│                 │                 │
│ 验证个人银行一致性 │ 验证企业银行一致性 │
│ - 姓名一致       │ - 企业名称一致    │
│ - 身份证号一致   │ - 纳税人识别号一致│
└─────────────────┴─────────────────┘
    ↓
验证银行信息
    ↓
验证通用银行字段 (开户名、手机号、银行卡号等)
    ↓
根据类型验证特定银行字段
    ↓
┌─────────────────┬─────────────────┐
│   个人银行字段    │   企业银行字段    │
│ - 证件类型       │ - 统一社会信用代码│
│ - 证件号格式     │                 │
└─────────────────┴─────────────────┘
    ↓
验证完成
```

## 应用建议

### 1. 替换原有代码
将重构后的验证逻辑替换原有的`validateRequest`和`validateBankInfo`方法。

### 2. 更新测试用例
根据新的方法结构，更新对应的单元测试：
- 为每个验证方法编写独立测试
- 测试各种异常情况
- 验证错误消息的准确性

### 3. 代码审查
- 确保所有验证逻辑都被正确迁移
- 验证异常消息的一致性
- 检查是否有遗漏的验证规则

### 4. 性能考虑
- 重构后的代码结构更清晰，但方法调用层级增加
- 实际性能影响微乎其微（验证逻辑本身很快）
- 可维护性的提升远大于微小的性能开销

## 扩展性

### 1. 新增推荐方类型
如果需要支持新的推荐方类型（如个体工商户），只需：
```java
} else if (dto.getType() == 3) {
    validateIndividualBusinessRecommender(dto);
}
```

### 2. 新增验证规则
在对应的验证方法中添加新的验证逻辑，不影响其他验证。

### 3. 自定义验证器
可以进一步抽象为验证器模式：
```java
public interface RecommenderValidator {
    void validate(RecommenderReqDto dto);
}

public class PersonalRecommenderValidator implements RecommenderValidator {
    // 个人验证逻辑
}

public class EnterpriseRecommenderValidator implements RecommenderValidator {
    // 企业验证逻辑
}
```

## 总结

通过这次重构：

1. **解决了代码混乱问题**：清晰的分层结构
2. **消除了重复代码**：提取公共验证逻辑
3. **提高了可维护性**：独立的验证方法
4. **增强了可读性**：语义化的方法名和清晰的逻辑
5. **便于测试**：每个方法可以独立测试
6. **易于扩展**：新增验证规则简单

建议采用重构后的代码结构，这将大大提高代码的质量和可维护性。
