# RecommenderServiceImpl银行卡验证方法调用升级说明

## 修改概述

将`RecommenderServiceImpl.java`中的银行卡验证调用从普通方法升级为带频率限制的方法，以便统计API调用次数和用户使用情况。

## 修改目标

1. **统计目的**：通过使用带频率限制的方法来统计API调用次数和用户使用情况
2. **参数传递**：确保userId能够正确传递到验证服务
3. **保持功能**：维持现有的验证逻辑和异常处理机制
4. **方法链路**：完善从上层到底层的参数传递链路

## 修改详情

### 1. 方法签名修改

#### saveProfile方法调用修改
```java
// 修改前
Boolean saveBankInfo = saveBankInfo(recommenderInfo.getId(), dto.getBankInfo(), dto.getIsDraft());

// 修改后
Boolean saveBankInfo = saveBankInfo(recommenderInfo.getId(), dto.getBankInfo(), dto.getIsDraft(), dto.getUserId());
```

**变更说明**：
- 在调用`saveBankInfo`时新增`dto.getUserId()`参数
- 确保userId从最上层传递下来

#### saveBankInfo方法签名修改
```java
// 修改前
private Boolean saveBankInfo(Long recommenderId, RecommenderBankInfoDto bankInfoDto, Boolean isDraft)

// 修改后
private Boolean saveBankInfo(Long recommenderId, RecommenderBankInfoDto bankInfoDto, Boolean isDraft, Long userId)
```

**变更说明**：
- 新增`Long userId`参数
- 用于向下传递给银行卡验证方法

#### performBankCardVerification方法签名修改
```java
// 修改前
private BankCardVerifyResultVo performBankCardVerification(RecommenderBankInfoDto bankInfoDto)

// 修改后
private BankCardVerifyResultVo performBankCardVerification(Long userId, RecommenderBankInfoDto bankInfoDto)
```

**变更说明**：
- 新增`Long userId`参数作为第一个参数
- 用于调用带频率限制的验证方法

### 2. 方法调用修改

#### saveBankInfo方法中的调用修改
```java
// 修改前
BankCardVerifyResultVo verifyResult = performBankCardVerification(bankInfoDto);

// 修改后
BankCardVerifyResultVo verifyResult = performBankCardVerification(userId, bankInfoDto);
```

**变更说明**：
- 传递userId参数给验证方法
- 在异常日志中也增加了userId的记录

#### performBankCardVerification方法中的服务调用修改
```java
// 修改前
BankCardVerifyResultVo result = tencentBankCardVerifyService.verifyBankCard(verifyDto);

// 修改后
BankCardVerifyResultVo result = tencentBankCardVerifyService.verifyBankCardWithRateLimit(userId, verifyDto);
```

**变更说明**：
- 从普通验证方法改为带频率限制的验证方法
- 传递userId参数用于频率限制统计

### 3. 日志记录增强

#### 验证开始日志
```java
// 修改前
log.info("开始银行卡三要素验证，卡号: {}, 姓名: {}", 
        maskBankCardNumber(bankInfoDto.getBankCardNo()), bankInfoDto.getAccountName());

// 修改后
log.info("开始银行卡三要素验证（带频率限制统计），用户ID: {}, 卡号: {}, 姓名: {}", 
        userId, maskBankCardNumber(bankInfoDto.getBankCardNo()), bankInfoDto.getAccountName());
```

#### 验证完成日志
```java
// 修改前
log.info("银行卡三要素验证完成，结果: {}, 描述: {}", 
        result.getSuccess(), result.getDescription());

// 修改后
log.info("银行卡三要素验证完成，用户ID: {}, 结果: {}, 描述: {}", 
        userId, result.getSuccess(), result.getDescription());
```

#### 异常日志
```java
// 修改前
log.error("银行卡三要素验证失败，recommenderId: {}, error: {}", recommenderId, e.getMessage());

// 修改后
log.error("银行卡三要素验证失败，recommenderId: {}, userId: {}, error: {}", recommenderId, userId, e.getMessage());
```

## 参数传递链路

### 完整的调用链路
```
saveProfile(RecommenderReqDto dto)
  ↓ dto.getUserId()
saveBankInfo(recommenderId, bankInfoDto, isDraft, userId)
  ↓ userId
performBankCardVerification(userId, bankInfoDto)
  ↓ userId
tencentBankCardVerifyService.verifyBankCardWithRateLimit(userId, verifyDto)
```

### 参数来源
- **userId**: 来自`RecommenderReqDto.getUserId()`
- **传递路径**: `saveProfile` → `saveBankInfo` → `performBankCardVerification` → `verifyBankCardWithRateLimit`

## 技术优势

### 1. 统计功能增强
- **API调用统计**：每次银行卡验证都会被纳入频率限制统计
- **用户行为分析**：可以分析用户的银行卡验证使用模式
- **成本控制**：通过频率限制控制API调用成本

### 2. 日志追踪完善
- **用户维度追踪**：所有日志都包含userId，便于问题排查
- **调用链路清晰**：从上层到底层的完整参数传递记录
- **统计标识明确**：日志中明确标注"带频率限制统计"

### 3. 功能保持不变
- **验证逻辑**：银行卡三要素验证逻辑完全不变
- **异常处理**：异常处理机制保持不变
- **状态管理**：验证状态更新逻辑保持不变

## 频率限制影响

### 1. 统计维度
- **分钟级统计**：同一用户1分钟内最多15次
- **天级统计**：同一用户1天内最多30次
- **Redis键格式**：`bank_verify:minute:{userId}` 和 `bank_verify:day:{userId}:{date}`

### 2. 超限处理
如果用户在推荐方认证过程中触发频率限制：
- **返回错误**：验证方法会返回频率限制错误
- **状态设置**：银行信息状态会被设置为验证失败
- **错误记录**：详细的错误信息会被记录到`rejectReason`字段

### 3. 业务影响
- **正常用户**：不会受到影响，因为正常认证不会频繁调用
- **异常用户**：如果短时间内多次提交认证，可能会触发限制
- **系统保护**：防止恶意或异常的大量API调用

## 监控建议

### 1. 关键指标
- 推荐方认证中的银行卡验证调用次数
- 频率限制触发次数（在认证场景中）
- 验证成功率和失败率
- 用户认证行为模式

### 2. 告警设置
- 认证场景中频率限制触发率过高告警
- 银行卡验证失败率异常告警
- 用户认证异常行为告警

### 3. 数据分析
- 用户认证时间分布
- 银行卡验证重试模式
- 认证成功率趋势

## 测试建议

### 1. 功能测试
```java
@Test
public void testSaveProfileWithBankVerification() {
    // 测试完整的认证流程，确保userId正确传递
}

@Test
public void testBankVerificationWithRateLimit() {
    // 测试银行卡验证的频率限制功能
}
```

### 2. 参数传递测试
```java
@Test
public void testUserIdParameterPassing() {
    // 验证userId在整个调用链路中的正确传递
}
```

### 3. 频率限制测试
```java
@Test
public void testRateLimitInAuthentication() {
    // 测试在认证场景中的频率限制行为
}
```

## 注意事项

### 1. 向后兼容性
- 修改只涉及内部方法调用，不影响外部接口
- 认证流程的业务逻辑保持不变
- 用户体验不受影响

### 2. 性能考虑
- 频率限制检查会增加少量的Redis操作开销
- 对于正常的认证流程，性能影响可以忽略
- 建议监控Redis操作的响应时间

### 3. 异常处理
- 频率限制异常会被正确处理并记录
- 不会影响银行信息的保存
- 用户可以在限制解除后重新提交认证

## 总结

通过这次修改，我们实现了：

1. **统计功能完善**：银行卡验证调用被纳入频率限制统计
2. **参数传递优化**：userId在整个调用链路中正确传递
3. **日志追踪增强**：所有相关日志都包含userId信息
4. **功能保持稳定**：验证逻辑和异常处理机制保持不变

这个修改既满足了统计需求，又保持了系统的稳定性和用户体验。通过频率限制机制，我们可以更好地监控和控制API的使用情况，同时为后续的数据分析和成本优化提供了基础。
