# RecommenderOrderService分支分析和测试执行指南

## 1. 方法分支分析

### 1.1 方法概述
- **接口位置**: `src/main/java/tripai/recommend/system/service/RecommenderOrderService.java`
- **实现类**: `RecommenderOrderServiceImpl`
- **测试的两个方法**:
  1. `getRecommenderOrderListByUserId(Long userId, RecommenderOrderQueryDto queryDto)` - 通过用户ID查询推荐方订单列表
  2. `getRecommenderOrderList(RecommenderOrderQueryDto queryDto)` - 查询推荐方订单列表

### 1.2 getRecommenderOrderListByUserId方法分支结构

```java
@Override
public ResponseResult<RecommenderOrderListVo> getRecommenderOrderListByUserId(Long userId, RecommenderOrderQueryDto queryDto) {
    try {
        // 分支B1: userId null检查
        if (userId == null) {
            return ResponseResult.fail("用户ID不能为空");
        }

        // 分支B2: queryDto null检查和处理
        if (queryDto == null) {
            queryDto = new RecommenderOrderQueryDto();
        }

        // 分支B3: 推荐方信息存在性检查
        if (recommenderInfo == null) {
            return ResponseResult.fail("推荐方不存在");
        }

        // 调用getRecommenderOrderList方法
        return getRecommenderOrderList(queryDto);

    } catch (Exception e) {
        // 分支B4: 异常处理
        return ResponseResult.fail("查询订单列表失败");
    }
}
```

### 1.3 getRecommenderOrderList方法分支结构

```java
public ResponseResult<RecommenderOrderListVo> getRecommenderOrderList(RecommenderOrderQueryDto queryDto) {
    try {
        // 分支B1: queryDto null检查
        if (queryDto == null) {
            return ResponseResult.fail("查询条件不能为空");
        }

        // 分支B2: recommenderId null检查
        if (queryDto.getRecommenderId() == null) {
            return ResponseResult.fail("推荐方ID不能为空");
        }

        // 分支B3: 推荐方存在性检查
        if (recommenderInfo == null) {
            return ResponseResult.fail("推荐方不存在");
        }

        // 分支B4: 订单类型验证
        if (queryDto.getOrderType() != null && !OrderTypeEnum.isValidCode(queryDto.getOrderType())) {
            // 无效的订单类型设置为null
        }

        // 分支B5: 关键词处理
        if (StrUtil.isNotBlank(queryDto.getKeyword())) {
            // 处理关键词（trim）
        }

        // 分支B6-B8: 查询订单列表
        if (orderType == null) {
            // B6: 查询全部订单
        } else if (OrderTypeEnum.isHotelOrder(orderType)) {
            // B7: 查询酒店订单
        } else if (OrderTypeEnum.isTourGuideOrder(orderType)) {
            // B8: 查询导游订单
        }

        return ResponseResult.ok(result);

    } catch (Exception e) {
        // 分支B16: 异常处理
        return ResponseResult.fail("查询订单列表失败");
    }
}
```

## 2. 分支详细分析

### 2.1 getRecommenderOrderListByUserId方法分支分析

| 分支ID | 分支类型 | 分支条件 | 返回结果 | 测试方法 |
|--------|----------|----------|----------|----------|
| B1 | if/else | `userId == null` | 失败："用户ID不能为空" | `test02_GetOrderListByUserId_BranchB1_UserIdNull` |
| B2 | if/else | `queryDto == null` | 创建默认queryDto并继续 | `test03_GetOrderListByUserId_BranchB2_QueryDtoNull` |
| B3 | if/else | `recommenderInfo == null` | 失败："推荐方不存在" | `test04_GetOrderListByUserId_BranchB3_RecommenderNotExists` |
| B4 | try/catch | `catch (Exception e)` | 失败："查询订单列表失败" | `test05_GetOrderListByUserId_BranchB4_ExceptionHandling` |

### 2.2 getRecommenderOrderList方法分支分析

| 分支ID | 分支类型 | 分支条件 | 返回结果 | 测试方法 |
|--------|----------|----------|----------|----------|
| B1 | if/else | `queryDto == null` | 失败："查询条件不能为空" | `test07_GetOrderList_BranchB1_QueryDtoNull` |
| B2 | if/else | `queryDto.getRecommenderId() == null` | 失败："推荐方ID不能为空" | `test08_GetOrderList_BranchB2_RecommenderIdNull` |
| B3 | if/else | `recommenderInfo == null` | 失败："推荐方不存在" | `test09_GetOrderList_BranchB3_RecommenderNotExists` |
| B4 | if/else | `!OrderTypeEnum.isValidCode(orderType)` | 设置orderType为null | `test10_GetOrderList_BranchB4_InvalidOrderType` |
| B5 | if/else | `StrUtil.isNotBlank(keyword)` | 处理关键词 | `test11_GetOrderList_BranchB5_KeywordProcessing` |
| B6 | if分支 | `orderType == null` | 查询全部订单 | `test12_GetOrderList_BranchB6_QueryAllOrders` |
| B7 | else if分支 | `OrderTypeEnum.isHotelOrder(orderType)` | 查询酒店订单 | `test13_GetOrderList_BranchB7_QueryHotelOrders` |
| B8 | else if分支 | `OrderTypeEnum.isTourGuideOrder(orderType)` | 查询导游订单 | `test14_GetOrderList_BranchB8_QueryTourGuideOrders` |
| B16 | try/catch | `catch (Exception e)` | 失败："查询订单列表失败" | `test15_GetOrderList_BranchB16_ExceptionHandling` |

## 3. 测试方法执行顺序

### 3.1 测试执行顺序说明

测试方法按照以下顺序设计和执行：

1. **数据完整性验证**（最优先）
2. **getRecommenderOrderListByUserId方法分支测试**（test02-test06）
3. **getRecommenderOrderList方法分支测试**（test07-test16）
4. **综合场景测试**（test17）

### 3.2 具体测试方法列表

| 执行顺序 | 测试方法 | 目标分支 | 测试场景 |
|---------|----------|----------|----------|
| 01 | `test01_DataIntegrityVerification` | 数据完整性 | 验证测试环境和依赖 |
| 02 | `test02_GetOrderListByUserId_BranchB1_UserIdNull` | getOrderListByUserId-B1 | userId为null |
| 03 | `test03_GetOrderListByUserId_BranchB2_QueryDtoNull` | getOrderListByUserId-B2 | queryDto为null |
| 04 | `test04_GetOrderListByUserId_BranchB3_RecommenderNotExists` | getOrderListByUserId-B3 | 推荐方不存在 |
| 05 | `test05_GetOrderListByUserId_BranchB4_ExceptionHandling` | getOrderListByUserId-B4 | 异常处理 |
| 06 | `test06_GetOrderListByUserId_SuccessScenario` | getOrderListByUserId-成功 | 完整流程成功 |
| 07 | `test07_GetOrderList_BranchB1_QueryDtoNull` | getOrderList-B1 | queryDto为null |
| 08 | `test08_GetOrderList_BranchB2_RecommenderIdNull` | getOrderList-B2 | recommenderId为null |
| 09 | `test09_GetOrderList_BranchB3_RecommenderNotExists` | getOrderList-B3 | 推荐方不存在 |
| 10 | `test10_GetOrderList_BranchB4_InvalidOrderType` | getOrderList-B4 | 无效订单类型 |
| 11 | `test11_GetOrderList_BranchB5_KeywordProcessing` | getOrderList-B5 | 关键词处理 |
| 12 | `test12_GetOrderList_BranchB6_QueryAllOrders` | getOrderList-B6 | 查询全部订单 |
| 13 | `test13_GetOrderList_BranchB7_QueryHotelOrders` | getOrderList-B7 | 查询酒店订单 |
| 14 | `test14_GetOrderList_BranchB8_QueryTourGuideOrders` | getOrderList-B8 | 查询导游订单 |
| 15 | `test15_GetOrderList_BranchB16_ExceptionHandling` | getOrderList-B16 | 异常处理 |
| 16 | `test16_GetOrderList_SuccessScenario` | getOrderList-成功 | 完整流程成功 |
| 17 | `test17_ComprehensiveScenarioTest` | 综合 | 综合场景验证 |

## 4. 运行测试验证

### 4.1 运行单个测试方法

```bash
# 按顺序运行单个测试方法
mvn test -Dtest=TestRecommenderOrderService#test01_DataIntegrityVerification
mvn test -Dtest=TestRecommenderOrderService#test02_GetOrderListByUserId_BranchB1_UserIdNull
mvn test -Dtest=TestRecommenderOrderService#test07_GetOrderList_BranchB1_QueryDtoNull
# ... 依此类推
```

### 4.2 运行所有测试

```bash
# 运行整个测试类
mvn test -Dtest=TestRecommenderOrderService

# 运行测试并生成覆盖率报告
mvn clean test jacoco:report -Dtest=TestRecommenderOrderService
```

### 4.3 运行特定方法的测试

```bash
# 运行getRecommenderOrderListByUserId相关测试
mvn test -Dtest=TestRecommenderOrderService#test*GetOrderListByUserId*

# 运行getRecommenderOrderList相关测试
mvn test -Dtest=TestRecommenderOrderService#test*GetOrderList_Branch*

# 运行异常处理测试
mvn test -Dtest=TestRecommenderOrderService#test*Exception*
```

## 5. 预期测试结果

### 5.1 成功测试的日志输出

每个测试方法都会输出详细的日志信息：

```
=== 测试getRecommenderOrderListByUserId方法 - 分支B1：userId为null的情况 ===
测试数据: userId = null, queryDto = {...}
执行时间: 15ms
测试结果: {"code":400,"message":"用户ID不能为空","data":null}
✅ 分支B1测试通过：userId为null时正确返回失败结果
```

### 5.2 分支覆盖率验证

使用JaCoCo验证分支覆盖率：

1. 生成覆盖率报告：`mvn clean test jacoco:report`
2. 打开报告：`target/site/jacoco/index.html`
3. 导航到`RecommenderOrderServiceImpl`类
4. 确认两个方法的所有分支显示为绿色（已覆盖）

### 5.3 预期覆盖率结果

- **getRecommenderOrderListByUserId方法分支覆盖率**: 100%
- **getRecommenderOrderList方法分支覆盖率**: 100%
- **整体行覆盖率**: ≥95%
- **方法覆盖率**: 100%

## 6. Mock策略说明

### 6.1 Mock Bean配置

```java
@MockBean
private RecommenderMapper recommenderMapper;

@MockBean
private RecommenderOrderMapper recommenderOrderMapper;
```

### 6.2 Mock使用场景

| Mock对象 | Mock方法 | 使用场景 |
|---------|----------|----------|
| `recommenderMapper` | `selectOne()` | 模拟通过用户ID查询推荐方信息 |
| `recommenderMapper` | `selectById()` | 模拟通过推荐方ID查询推荐方信息 |
| `recommenderOrderMapper` | `selectRecommenderOrderList()` | 模拟查询全部订单列表 |
| `recommenderOrderMapper` | `selectRecommenderHotelOrderList()` | 模拟查询酒店订单列表 |
| `recommenderOrderMapper` | `selectRecommenderTourGuideOrderList()` | 模拟查询导游订单列表 |
| `recommenderOrderMapper` | `selectRecommenderOrderCount()` | 模拟查询订单总数 |
| `recommenderOrderMapper` | `selectRecommenderOrderCommissionSum()` | 模拟查询分佣总额 |

### 6.3 Mock验证

每个测试方法都包含Mock调用验证：

```java
// 验证Mock调用次数和参数
verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
verify(recommenderOrderMapper, times(1)).selectRecommenderOrderList(any());
verify(recommenderOrderMapper, times(1)).selectRecommenderOrderCount(any());
```

## 7. 测试数据管理

### 7.1 测试数据构建

```java
private RecommenderOrderQueryDto buildValidQueryDto() {
    RecommenderOrderQueryDto queryDto = new RecommenderOrderQueryDto();
    queryDto.setRecommenderId(1L);
    queryDto.setOrderType(null); // 默认查询全部
    queryDto.setStartDate(LocalDate.now().minusDays(30));
    queryDto.setEndDate(LocalDate.now());
    queryDto.setPageNum(1);
    queryDto.setPageSize(10);
    return queryDto;
}
```

### 7.2 Mock数据构建

```java
private List<RecommenderOrderVo> buildMockOrderList() {
    List<RecommenderOrderVo> orderList = new ArrayList<>();
    
    // 酒店订单
    RecommenderOrderVo hotelOrder = new RecommenderOrderVo();
    hotelOrder.setOrderId("H202501290001");
    hotelOrder.setOrderType(1);
    hotelOrder.setCommissionAmount(new BigDecimal("80.00"));
    orderList.add(hotelOrder);
    
    // 导游订单
    RecommenderOrderVo tourGuideOrder = new RecommenderOrderVo();
    tourGuideOrder.setOrderId("T202501290002");
    tourGuideOrder.setOrderType(2);
    tourGuideOrder.setCommissionAmount(new BigDecimal("70.00"));
    orderList.add(tourGuideOrder);
    
    return orderList;
}
```

## 8. 故障排除

### 8.1 常见问题

#### 问题1：Mock不生效
**解决方案**：
- 确保使用了`@MockBean`注解
- 检查Mock方法签名是否正确
- 验证`when().thenReturn()`设置

#### 问题2：订单类型验证失败
**解决方案**：
- 检查OrderTypeEnum的实现
- 确认订单类型常量值
- 验证isValidCode、isHotelOrder、isTourGuideOrder方法

#### 问题3：分支未覆盖
**解决方案**：
- 检查测试数据是否正确
- 确认Mock设置是否触发了目标分支
- 查看测试日志确认执行路径

### 8.2 调试技巧

1. **启用详细日志**：查看方法执行路径
2. **使用断点调试**：逐步验证分支执行
3. **检查Mock调用**：确认Mock被正确调用

## 9. 总结

### 9.1 测试特点

- **完整的分支覆盖**：getRecommenderOrderListByUserId方法4个分支，getRecommenderOrderList方法9个分支
- **有序的测试执行**：按逻辑顺序设计测试方法（test01-test17）
- **详细的分支标注**：每个测试方法都明确标注目标分支
- **专业的Mock策略**：使用Mock模拟各种查询场景
- **完整的验证机制**：包含断言和Mock调用验证

### 9.2 预期效果

通过这套测试方法，两个方法应该能够达到：
- ✅ 100%分支覆盖率
- ✅ 完整的异常处理测试
- ✅ 全面的业务逻辑验证
- ✅ 可靠的回归测试保障

按照本指南执行测试，您应该能够验证`getRecommenderOrderListByUserId`和`getRecommenderOrderList`方法的所有分支都被正确覆盖。
