# TestRecommenderService图片字段格式修正说明

## 修正概述

根据数据库实际存储格式，将测试类中所有图片相关字段从简单的URL字符串格式修正为JSON格式的`FileInfo`对象。

## 数据库存储格式

数据库中图片字段存储的是JSON格式：
```json
{
  "url": "http://example.com/rms/avatar.jpg",
  "name": "avatar.jpg", 
  "type": 1,
  "bucket": "rms"
}
```

## FileInfo实体类结构

```java
public class FileInfo {
    private String url;      // 文件URL
    private String name;     // 文件名
    private Integer type;    // 文件类型
    private String bucket;   // 存储桶
}
```

## 修正的字段

### 1. RecommenderReqDto中的图片字段
- `idCardFrontUrl` - 身份证正面照片
- `idCardBackUrl` - 身份证背面照片

### 2. RecommenderInfo中的图片字段
- `idCardFrontUrl` - 身份证正面照片
- `idCardBackUrl` - 身份证背面照片
- `avatarUrl` - 头像
- `businessLicenseUrl` - 营业执照

## 修正前后对比

### 修正前（错误格式）
```java
dto.setIdCardFrontUrl("http://example.com/front.jpg");
dto.setIdCardBackUrl("http://example.com/back.jpg");
```

### 修正后（正确格式）
```java
dto.setIdCardFrontUrl(buildFileInfoJson("http://example.com/rms/idcard_front.jpg", "idcard_front.jpg"));
dto.setIdCardBackUrl(buildFileInfoJson("http://example.com/rms/idcard_back.jpg", "idcard_back.jpg"));
```

## 新增的辅助方法

### 1. 基础方法
```java
private String buildFileInfoJson(String url, String fileName) {
    FileInfo fileInfo = new FileInfo();
    fileInfo.setUrl(url);
    fileInfo.setName(fileName);
    fileInfo.setType(1); // 默认类型为1
    fileInfo.setBucket("rms"); // 默认bucket为rms
    
    return JSON.toJSONString(fileInfo);
}
```

### 2. 带自定义类型的方法
```java
private String buildFileInfoJson(String url, String fileName, Integer type) {
    FileInfo fileInfo = new FileInfo();
    fileInfo.setUrl(url);
    fileInfo.setName(fileName);
    fileInfo.setType(type);
    fileInfo.setBucket("rms");
    
    return JSON.toJSONString(fileInfo);
}
```

### 3. 完全自定义的方法
```java
private String buildFileInfoJson(String url, String fileName, Integer type, String bucket) {
    FileInfo fileInfo = new FileInfo();
    fileInfo.setUrl(url);
    fileInfo.setName(fileName);
    fileInfo.setType(type);
    fileInfo.setBucket(bucket);
    
    return JSON.toJSONString(fileInfo);
}
```

## 修正的测试方法

### 1. buildValidRecommenderReqDto方法
```java
dto.setIdCardFrontUrl(buildFileInfoJson("http://example.com/rms/idcard_front.jpg", "idcard_front.jpg"));
dto.setIdCardBackUrl(buildFileInfoJson("http://example.com/rms/idcard_back.jpg", "idcard_back.jpg"));
```

### 2. buildMockRecommenderInfo方法
```java
recommenderInfo.setIdCardFrontUrl(buildFileInfoJson("http://example.com/rms/idcard_front.jpg", "idcard_front.jpg"));
recommenderInfo.setIdCardBackUrl(buildFileInfoJson("http://example.com/rms/idcard_back.jpg", "idcard_back.jpg"));
recommenderInfo.setAvatarUrl(buildFileInfoJson("http://example.com/rms/avatar.jpg", "avatar.jpg"));
recommenderInfo.setBusinessLicenseUrl(buildFileInfoJson("http://example.com/rms/business_license.jpg", "business_license.jpg"));
```

### 3. 测试方法中的Mock设置
```java
// test15_GetAuditDetail_BranchB4_PersonalType
mockRecommenderInfo.setIdCardFrontUrl(buildFileInfoJson("http://example.com/rms/idcard_front.jpg", "idcard_front.jpg"));
mockRecommenderInfo.setIdCardBackUrl(buildFileInfoJson("http://example.com/rms/idcard_back.jpg", "idcard_back.jpg"));

// test16_GetAuditDetail_BranchB4_EnterpriseType
mockRecommenderInfo.setBusinessLicenseUrl(buildFileInfoJson("http://example.com/rms/business_license.jpg", "business_license.jpg"));
```

## 断言验证的修正

### 修正前（精确匹配）
```java
assertThat(result.getData().getIdCardFrontUrl()).isEqualTo("http://example.com/front.jpg");
```

### 修正后（包含匹配）
```java
assertThat(result.getData().getIdCardFrontUrl()).contains("http://example.com/rms/idcard_front.jpg");
```

**说明**：由于返回的是JSON字符串，使用`contains`方法验证URL是否包含在JSON中，而不是精确匹配整个JSON字符串。

## 生成的JSON示例

使用`buildFileInfoJson`方法生成的JSON格式：

### 身份证正面照片
```json
{
  "url": "http://example.com/rms/idcard_front.jpg",
  "name": "idcard_front.jpg",
  "type": 1,
  "bucket": "rms"
}
```

### 头像
```json
{
  "url": "http://example.com/rms/avatar.jpg",
  "name": "avatar.jpg",
  "type": 1,
  "bucket": "rms"
}
```

### 营业执照
```json
{
  "url": "http://example.com/rms/business_license.jpg",
  "name": "business_license.jpg",
  "type": 1,
  "bucket": "rms"
}
```

## 影响的测试方法

以下测试方法受到此次修正的影响：

1. `test01_DataIntegrityVerification` - 数据构建验证
2. `test03_SaveProfile_BranchB2_DraftModeSkipValidation` - 使用buildValidRecommenderReqDto
3. `test04_SaveProfile_BranchB2_ValidationException` - 使用buildValidRecommenderReqDto
4. `test05_SaveProfile_BranchB3_B4_IdentifierDuplicate` - 使用buildValidRecommenderReqDto
5. `test06_SaveProfile_BranchB3_CreateNewRecommender` - 使用buildValidRecommenderReqDto
6. `test07_SaveProfile_BranchB5_SaveRecommenderInfoFailed` - 使用buildMockRecommenderInfo
7. `test08_SaveProfile_BranchB6_SaveBankInfoFailed` - 使用buildMockRecommenderInfo
8. `test09_SaveProfile_BranchB7_ExceptionHandling` - 使用buildValidRecommenderReqDto
9. `test10_SaveProfile_SuccessScenario` - 使用buildMockRecommenderInfo
10. `test13_GetAuditDetail_BranchB3_AuditRecordExists` - 使用buildMockRecommenderInfo
11. `test14_GetAuditDetail_BranchB3_AuditRecordNotExists` - 使用buildMockRecommenderInfo
12. `test15_GetAuditDetail_BranchB4_PersonalType` - 直接设置图片URL
13. `test16_GetAuditDetail_BranchB4_EnterpriseType` - 直接设置图片URL
14. `test17_GetAuditDetail_BranchB5_BankInfoExists` - 使用buildMockRecommenderInfo
15. `test18_GetAuditDetail_BranchB5_BankInfoNotExists` - 使用buildMockRecommenderInfo
16. `test20_GetAuditDetail_SuccessScenario` - 使用buildMockRecommenderInfo
17. `test21_ComprehensiveScenarioTest` - 使用辅助方法

## 验证测试

修正完成后，建议运行以下测试验证：

```bash
# 运行所有测试
mvn test -Dtest=TestRecommenderService

# 运行特定的图片相关测试
mvn test -Dtest=TestRecommenderService#test15_GetAuditDetail_BranchB4_PersonalType
mvn test -Dtest=TestRecommenderService#test16_GetAuditDetail_BranchB4_EnterpriseType

# 运行数据完整性验证
mvn test -Dtest=TestRecommenderService#test01_DataIntegrityVerification
```

## 注意事项

1. **JSON格式**：确保生成的JSON格式与数据库存储格式一致
2. **断言方式**：使用`contains`而不是`isEqualTo`进行URL验证
3. **默认值**：type默认为1，bucket默认为"rms"
4. **扩展性**：提供了多个重载方法支持不同的自定义需求

## 总结

此次修正确保了测试数据与实际数据库存储格式的一致性，提高了测试的准确性和可靠性。所有图片相关字段现在都使用正确的JSON格式，符合`FileInfo`实体类的结构要求。
