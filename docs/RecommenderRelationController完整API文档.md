# 推荐方关系管理完整API文档

## 概述

本文档描述了推荐方关系管理的完整API接口，包括酒店供应商库和导游供应商库的管理功能。所有接口都集成在`RecommenderRelationController`控制器中，保持统一的设计模式。

## 接口列表

### 酒店供应商库管理

#### 1. 查询推荐方酒店供应商列表

**接口地址：** `POST /recommender/relation/hotel/list`

**接口描述：** 查询推荐方的酒店供应商列表，支持筛选、排序、搜索和分页功能

**请求头：**
```
Content-Type: application/json
Recommender-Token: {用户token}
```

**请求参数：**
```json
{
  "relationStartDate": "2025-01-01",
  "relationEndDate": "2025-07-25",
  "keyword": "酒店名称或供应商ID",
  "sortType": 1,
  "pageNum": 1,
  "pageSize": 50
}
```

**参数说明：**
- `relationStartDate`: 建立关系开始时间，格式：yyyy-MM-dd
- `relationEndDate`: 建立关系结束时间，格式：yyyy-MM-dd
- `keyword`: 搜索关键词，支持供应商ID、酒店ID/酒店名称
- `sortType`: 排序类型（Integer），可选值：
  - `1`: 建立时间从新到旧（默认）
  - `2`: 上架服务数从高到低
  - `3`: 成单数从高到低
  - 注：都相同的情况，按该条数据的订单号升序排列
- `pageNum`: 页码，从1开始，默认1
- `pageSize`: 每页大小，支持50/100/150/200，默认50

**响应示例：**
```json
{
  "code": 200,
  "message": "OK",
  "data": {
    "relations": [
      {
        "relationId": 1,
        "recommenderId": 1,
        "hotelId": 123,
        "hotelName": "北京国际酒店",
        "hotelSupplierCode": "SUP001",
        "supplierMobile": "137****8888",
        "supplierNickName": "张三",
        "contractHotelCount": 5,
        "onlineRoomCount": 20,
        "relationCreateTime": "2025-01-15 10:30:00",
        "orderCount": 15,
        "relationStatus": 1,
        "relationStatusDesc": "正常",
        "hotelStatus": 4,
        "hotelStatusDesc": "已上线",
        "hotelAddress": "北京市朝阳区",
        "starRate": 5,
        "score": "4.8",
        "cityName": "北京"
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 50,
    "totalPages": 2,
    "hasNext": true,
    "hasPrevious": false,
    "sortType": "create_time_desc",
    "sortDescription": "建立关系时间降序",
    "statistics": {
      "totalRelationCount": 100,
      "totalHotelCount": 85,
      "totalOnlineRoomCount": 1500,
      "totalOrderCount": 800,
      "activeRelationCount": 90,
      "suspendRelationCount": 10,
      "avgRoomCountPerRelation": 15.0,
      "avgOrderCountPerRelation": 8.0
    }
  }
}
```

#### 2. 查询推荐方酒店供应商详情

**接口地址：** `GET /recommender/relation/hotel/detail/{hotelId}`

**接口描述：** 查询指定酒店的详细信息

**请求头：**
```
Recommender-Token: {用户token}
```

**路径参数：**
- `hotelId`: 酒店ID

**响应示例：**
```json
{
  "code": 200,
  "message": "OK",
  "data": {
    "relationId": 1,
    "recommenderId": 1,
    "hotelId": 123,
    "hotelName": "北京国际酒店",
    "hotelSupplierCode": "SUP001",
    "supplierMobile": "137****8888",
    "supplierNickName": "张三",
    "contractHotelCount": 5,
    "onlineRoomCount": 20,
    "relationCreateTime": "2025-01-15 10:30:00",
    "orderCount": 15,
    "relationStatus": 1,
    "relationStatusDesc": "正常",
    "hotelStatus": 4,
    "hotelStatusDesc": "已上线",
    "hotelAddress": "北京市朝阳区",
    "starRate": 5,
    "score": "4.8",
    "cityName": "北京"
  }
}
```

#### 3. 查询推荐方酒店供应商统计信息

**接口地址：** `GET /recommender/relation/hotel/statistics`

**接口描述：** 查询推荐方酒店供应商的统计信息

**请求头：**
```
Recommender-Token: {用户token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "OK",
  "data": {
    "totalRelationCount": 100,
    "totalHotelCount": 85,
    "totalOnlineRoomCount": 1500,
    "totalOrderCount": 800,
    "activeRelationCount": 90,
    "suspendRelationCount": 10,
    "avgRoomCountPerRelation": 15.0,
    "avgOrderCountPerRelation": 8.0
  }
}
```

### 导游供应商库管理

#### 4. 查询推荐方导游供应商列表

**接口地址：** `POST /recommender/relation/guide/list`

**接口描述：** 查询推荐方的导游供应商列表，支持筛选、排序、搜索和分页功能

**请求头：**
```
Content-Type: application/json
Recommender-Token: {用户token}
```

**请求参数：**
```json
{
  "productionLine": 2,
  "controlStatus": "normal",
  "relationStartDate": "2025-01-01",
  "relationEndDate": "2025-07-25",
  "keyword": "张三",
  "sortType": 2,
  "pageNum": 1,
  "pageSize": 50
}
```

**参数说明：**
- `productionLine`: 产线筛选，可选值：1(司机)、2(导游)、3(司兼导)，null表示全部
- `controlStatus`: 状态筛选，可选值："normal"(正常)、"controlled"(管控)，null表示全部
- `relationStartDate`: 建立关系开始时间，格式：yyyy-MM-dd
- `relationEndDate`: 建立关系结束时间，格式：yyyy-MM-dd
- `keyword`: 搜索关键词，支持导游ID或导游姓名
- `sortType`: 排序类型（Integer），可选值：
  - `1`: 建立时间从新到旧（默认）
  - `2`: 服务分从高到低
  - `3`: 上架服务数从高到低
  - `4`: 成单数从高到低
- `pageNum`: 页码，从1开始，默认1
- `pageSize`: 每页大小，支持50/100/150/200，默认50

**响应示例：**
```json
{
  "code": 200,
  "message": "OK",
  "data": {
    "relations": [
      {
        "relationId": 1,
        "recommenderId": 1,
        "guideId": 123,
        "guideName": "张三",
        "phone": "137****8888",
        "productionLine": 2,
        "productionLineName": "导游",
        "serviceScore": 95.5,
        "controlStatus": 0,
        "controlStatusName": "正常",
        "serviceCount": 5,
        "relationCreateTime": "2025-01-15 10:30:00",
        "orderCount": 12,
        "relationStatus": 1,
        "relationStatusDesc": "正常",
        "userId": 123,
        "auditStatus": 1,
        "auditStatusName": "审核通过",
        "serviceCities": "北京,上海",
        "primaryServiceCity": "北京",
        "language": "中文,英文",
        "introduction": "专业导游，经验丰富",
        "rejectionCount": 2
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 50,
    "totalPages": 2,
    "hasNext": true,
    "hasPrevious": false,
    "sortType": "service_score_desc",
    "sortDescription": "服务分降序",
    "statistics": {
      "totalGuideCount": 100,
      "normalGuideCount": 85,
      "controlledGuideCount": 15,
      "driverCount": 20,
      "guideCount": 60,
      "driverGuideCount": 20,
      "totalServiceCount": 450,
      "totalOrderCount": 1200,
      "averageServiceScore": 88.5,
      "maxServiceScore": 100.0,
      "minServiceScore": 60.0,
      "monthlyNewGuideCount": 5,
      "weeklyNewGuideCount": 2,
      "dailyNewGuideCount": 0
    }
  }
}
```

#### 5. 查询推荐方导游供应商详情

**接口地址：** `GET /recommender/relation/guide/detail/{guideId}`

**接口描述：** 查询指定导游的详细信息

**请求头：**
```
Recommender-Token: {用户token}
```

**路径参数：**
- `guideId`: 导游ID

**响应示例：**
```json
{
  "code": 200,
  "message": "OK",
  "data": {
    "relationId": 1,
    "recommenderId": 1,
    "guideId": 123,
    "guideName": "张三",
    "phone": "137****8888",
    "productionLine": 2,
    "productionLineName": "导游",
    "serviceScore": 95.5,
    "controlStatus": 0,
    "controlStatusName": "正常",
    "serviceCount": 5,
    "relationCreateTime": "2025-01-15 10:30:00",
    "orderCount": 12,
    "relationStatus": 1,
    "relationStatusDesc": "正常",
    "userId": 123,
    "auditStatus": 1,
    "auditStatusName": "审核通过",
    "serviceCities": "北京,上海",
    "primaryServiceCity": "北京",
    "language": "中文,英文",
    "introduction": "专业导游，经验丰富",
    "rejectionCount": 2
  }
}
```

#### 6. 查询推荐方导游供应商统计信息

**接口地址：** `GET /recommender/relation/guide/statistics`

**接口描述：** 查询推荐方导游供应商的统计信息

**请求头：**
```
Recommender-Token: {用户token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "OK",
  "data": {
    "totalGuideCount": 100,
    "normalGuideCount": 85,
    "controlledGuideCount": 15,
    "driverCount": 20,
    "guideCount": 60,
    "driverGuideCount": 20,
    "totalServiceCount": 450,
    "totalOrderCount": 1200,
    "averageServiceScore": 88.5,
    "maxServiceScore": 100.0,
    "minServiceScore": 60.0,
    "monthlyNewGuideCount": 5,
    "weeklyNewGuideCount": 2,
    "dailyNewGuideCount": 0
  }
}
```

#### 7. 刷新推荐方导游供应商统计信息缓存

**接口地址：** `POST /recommender/relation/guide/statistics/refresh`

**接口描述：** 刷新导游供应商统计信息的缓存数据

**请求头：**
```
Recommender-Token: {用户token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "缓存刷新成功",
  "data": null
}
```

## 数据字段说明

### 酒店供应商信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| relationId | Long | 关系ID |
| recommenderId | Long | 推荐方ID |
| hotelId | Long | 酒店ID |
| hotelName | String | 酒店名称 |
| hotelSupplierCode | String | 酒店供应商编码 |
| supplierMobile | String | 供应商手机号（脱敏显示） |
| supplierNickName | String | 供应商昵称 |
| contractHotelCount | Integer | 签约酒店数 |
| onlineRoomCount | Integer | 已上线房型数 |
| relationCreateTime | String | 建立关系时间 |
| orderCount | Integer | 成单数 |
| relationStatus | Integer | 关系状态：1=正常，2=管控/暂停 |
| relationStatusDesc | String | 关系状态描述 |
| hotelStatus | Integer | 酒店状态 |
| hotelStatusDesc | String | 酒店状态描述 |
| hotelAddress | String | 酒店地址 |
| starRate | Integer | 酒店星级 |
| score | String | 酒店评分 |
| cityName | String | 酒店所在城市 |

### 导游供应商信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| relationId | Long | 关系ID |
| recommenderId | Long | 推荐方ID |
| guideId | Long | 导游ID |
| guideName | String | 导游姓名 |
| phone | String | 手机号（脱敏显示） |
| productionLine | Integer | 产线：1=司机，2=导游，3=司兼导 |
| productionLineName | String | 产线名称 |
| serviceScore | BigDecimal | 服务分 |
| controlStatus | Integer | 管控状态：0=正常，1=月度管控，2=永久管控，3=手动管控 |
| controlStatusName | String | 管控状态名称 |
| serviceCount | Integer | 上架服务数 |
| relationCreateTime | String | 建立关系时间 |
| orderCount | Integer | 成单数 |
| relationStatus | Integer | 关系状态：1=正常，2=管控 |
| relationStatusDesc | String | 关系状态描述 |
| userId | Long | 用户ID |
| auditStatus | Integer | 审核状态：0=待审核，1=审核通过，2=审核未通过 |
| auditStatusName | String | 审核状态名称 |
| serviceCities | String | 服务城市 |
| primaryServiceCity | String | 主要服务城市 |
| language | String | 服务语言 |
| introduction | String | 个人简介 |
| rejectionCount | Integer | 本月拒单次数 |

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 查询条件不能为空 | 请求参数为空 |
| 400 | 推荐方ID不能为空 | 推荐方ID参数缺失 |
| 400 | 酒店ID不能为空 | 酒店ID参数缺失 |
| 400 | 导游ID不能为空 | 导游ID参数缺失 |
| 401 | api.user.token.token-not-resolve-user | Token解析失败或用户信息无效 |
| 404 | 推荐方不存在 | 指定的推荐方ID不存在 |
| 404 | 酒店不存在 | 指定的酒店ID不存在 |
| 404 | 导游不存在 | 指定的导游ID不存在 |
| 500 | 查询失败 | 服务器内部错误 |

## 注意事项

1. **权限控制**：所有接口都需要有效的用户Token，推荐方只能查看自己的供应商信息
2. **数据脱敏**：手机号等敏感信息会自动脱敏显示
3. **分页限制**：每页最大支持200条记录，建议使用默认的50条/页
4. **排序性能**：按服务分和成单数排序时，数据量大的情况下可能响应较慢
5. **缓存机制**：统计信息有缓存机制，如需实时数据可调用刷新接口
6. **日期格式**：所有日期参数使用yyyy-MM-dd格式
7. **关键词搜索**：支持精确匹配和模糊匹配
