# RecommenderOrderController API文档

## 接口概述

推荐方订单管理接口，提供推荐方订单列表的查询功能。

**基础路径**: `/recommender/order`

**认证方式**: 需要在请求头中携带Authorization token

---

## 1. 查询推荐方订单列表

### 接口基本信息
- **接口路径**: `POST /recommender/order/list`
- **接口描述**: 根据查询条件获取推荐方的订单列表，支持分页、筛选和排序
- **认证要求**: 需要Authorization token

### 请求参数

#### 请求头参数
```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // 用户认证token，必填
  "Content-Type": "application/json" // 请求内容类型
}
```

#### 请求体参数
```json
{
  "recommenderId": 123, // 推荐方ID（可选，系统会根据用户ID自动查询）
  "orderType": 1, // 订单类型：null=全部，1=酒店订单，2=导游订单
  "settlementStartDate": "2025-01-01", // 结算开始时间，格式：yyyy-MM-dd
  "settlementEndDate": "2025-01-30", // 结算结束时间，格式：yyyy-MM-dd
  "keyword": "测试酒店", // 关键词搜索（订单号、酒店名称、导游供应商姓名）
  "pageNum": 1, // 页码（从1开始），默认值：1
  "pageSize": 20, // 每页大小，默认值：20
  "orderBy": "settlement_time", // 排序字段，默认值：settlement_time
  "orderDirection": "DESC" // 排序方向，默认值：DESC（降序）
}
```

### 响应结果

#### 成功响应 (200)
```json
{
  "code": 200, // 响应状态码
  "message": "OK", // 响应消息
  "data": {
    "orders": [ // 订单列表
      {
        "orderNo": "H202501300001", // 订单号
        "orderType": 1, // 订单类型代码：1=酒店，2=导游
        "orderTypeDesc": "酒店", // 订单类型描述
        "supplierName": "广州白云宾馆", // 供应商姓名（酒店名/导游名）
        "orderAmount": 50000, // 订单金额（用户实付，单位：分）
        "orderAmountYuan": 500.00, // 订单金额（用户实付，单位：元）
        "commissionAmount": 50.00, // 分佣金额（单位：元）
        "settlementStatus": "已结算", // 结算状态（固定显示"已结算"）
        "settlementTime": "2025-01-30T10:00:00", // 结算时间
        "bizId": 1001, // 业务ID（酒店ID或导游ID）
        "recommenderId": 123, // 推荐方ID
        "relationId": 456 // 关系ID
      },
      {
        "orderNo": "T202501300002", // 订单号
        "orderType": 2, // 订单类型代码：1=酒店，2=导游
        "orderTypeDesc": "导游", // 订单类型描述
        "supplierName": "张导游", // 供应商姓名（酒店名/导游名）
        "orderAmount": 30000, // 订单金额（用户实付，单位：分）
        "orderAmountYuan": 300.00, // 订单金额（用户实付，单位：元）
        "commissionAmount": 30.00, // 分佣金额（单位：元）
        "settlementStatus": "已结算", // 结算状态（固定显示"已结算"）
        "settlementTime": "2025-01-30T09:00:00", // 结算时间
        "bizId": 2001, // 业务ID（酒店ID或导游ID）
        "recommenderId": 123, // 推荐方ID
        "relationId": 789 // 关系ID
      }
    ],
    "total": 150, // 总记录数
    "pageNum": 1, // 当前页码
    "pageSize": 20, // 每页大小
    "totalPages": 8, // 总页数
    "totalCommissionAmount": 1500.00, // 分佣金额小计（当前查询结果的分佣金额总计）
    "hasNext": true, // 是否有下一页
    "hasPrevious": false // 是否有上一页
  }
}
```

#### 错误响应
```json
{
  "code": 401, // 错误状态码
  "message": "api.user.token.token-not-resolve-user", // Token错误消息
  "data": null // 数据为空
}
```

```json
{
  "code": 400, // 错误状态码
  "message": "推荐方不存在", // 业务错误消息
  "data": null // 数据为空
}
```

### 错误码说明
- `400`: 业务逻辑错误（如推荐方不存在、查询条件不能为空等）
- `401`: Token解析失败或用户ID为空
- `500`: 服务器内部错误

---

## 调用示例

### 1. 查询全部订单（默认参数）

```bash
curl -X POST "http://localhost:8080/recommender/order/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. 查询酒店订单（指定类型）

```bash
curl -X POST "http://localhost:8080/recommender/order/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "orderType": 1,
    "pageNum": 1,
    "pageSize": 10
  }'
```

### 3. 按时间范围查询订单

```bash
curl -X POST "http://localhost:8080/recommender/order/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "settlementStartDate": "2025-01-01",
    "settlementEndDate": "2025-01-30",
    "pageNum": 1,
    "pageSize": 20
  }'
```

### 4. 关键词搜索订单

```bash
curl -X POST "http://localhost:8080/recommender/order/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "白云宾馆",
    "orderType": 1,
    "pageNum": 1,
    "pageSize": 10
  }'
```

### 5. 自定义排序查询

```bash
curl -X POST "http://localhost:8080/recommender/order/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "orderBy": "commission_amount",
    "orderDirection": "DESC",
    "pageNum": 1,
    "pageSize": 20
  }'
```

---

## 参数说明

### 查询参数详解

#### orderType（订单类型）
- `null` 或不传：查询全部订单
- `1`：仅查询酒店订单
- `2`：仅查询导游订单

#### 时间范围查询
- `settlementStartDate`：结算开始时间，格式为 `yyyy-MM-dd`
- `settlementEndDate`：结算结束时间，格式为 `yyyy-MM-dd`
- 如果开始时间晚于结束时间，系统会自动交换

#### 关键词搜索
- `keyword`：支持搜索订单号、酒店名称、导游供应商姓名
- 模糊匹配，不区分大小写

#### 分页参数
- `pageNum`：页码，从1开始，默认值为1
- `pageSize`：每页大小，默认值为20
- 系统会自动验证并修正无效的分页参数

#### 排序参数
- `orderBy`：排序字段，默认为 `settlement_time`
- `orderDirection`：排序方向，`ASC`（升序）或 `DESC`（降序），默认为 `DESC`

### 响应数据详解

#### 订单信息字段
- `orderAmount`：订单金额，单位为分（整数）
- `orderAmountYuan`：订单金额，单位为元（小数，自动计算）
- `commissionAmount`：分佣金额，单位为元（小数）
- `settlementStatus`：结算状态，固定显示"已结算"

#### 分页信息字段
- `total`：总记录数
- `totalPages`：总页数（自动计算）
- `hasNext`：是否有下一页（自动计算）
- `hasPrevious`：是否有上一页（自动计算）
- `totalCommissionAmount`：当前查询结果的分佣金额总计

---

## 注意事项

1. **认证要求**: 接口需要在请求头中携带有效的Authorization token
2. **权限控制**: 用户只能查询自己作为推荐方的订单
3. **数据范围**: 只返回已结算的订单数据
4. **金额单位**: 
   - `orderAmount`：分（整数）
   - `orderAmountYuan`：元（小数，由系统自动计算）
   - `commissionAmount`：元（小数）
5. **分页限制**: 建议每页大小不超过100条，以保证查询性能
6. **时间格式**: 日期参数使用 `yyyy-MM-dd` 格式
7. **排序字段**: 支持按结算时间、分佣金额等字段排序

## 枚举值说明

### 订单类型 (orderType)
- `1`: 酒店订单
- `2`: 导游订单

### 排序方向 (orderDirection)
- `ASC`: 升序
- `DESC`: 降序

### 结算状态 (settlementStatus)
- `已结算`: 固定值，表示订单已完成结算

## 业务规则

1. **订单筛选**: 只显示已结算的订单
2. **权限验证**: 根据token中的用户ID查询对应的推荐方订单
3. **数据脱敏**: 敏感信息已进行适当脱敏处理
4. **分佣计算**: 分佣金额为实际到账金额
5. **时间范围**: 基于结算时间进行筛选，不是订单创建时间
