# RecommenderService两个方法分支分析和测试执行指南

## 1. 方法分支分析

### 1.1 方法概述
- **接口位置**: `src/main/java/tripai/recommend/system/service/RecommenderService.java`
- **实现类**: `RecommenderServiceImpl`
- **测试的两个方法**:
  1. `saveProfile(RecommenderReqDto dto)` - 保存推荐方认证资料
  2. `getAuditDetail(Long recommenderId)` - 获取推荐方审核详情

### 1.2 saveProfile方法分支结构

```java
@Override
@Transactional(rollbackFor = Exception.class)
public ResponseResult<RecommenderProfileVo> saveProfile(RecommenderReqDto dto) {
    try {
        // 分支B1: dto null检查
        if (dto == null) {
            return ResponseResult.fail("参数为空");
        }
        
        // 分支B2: 是否草稿检查
        if (!dto.getIsDraft()) {
            validateRequest(dto); // 可能抛出BusinessException
        }
        
        // 分支B3: 推荐方信息存在性检查
        if (recommenderInfo == null) {
            // 分支B4: 身份证号/统一社会信用代码重复检查
            if (existingInfo != null) {
                return ResponseResult.fail("该身份证号/统一社会信用代码已被使用");
            }
            // 创建新的推荐方信息
        }
        
        // 分支B5: 保存推荐方基本信息结果检查
        if (!saveRecommenderInfo) {
            return ResponseResult.fail("保存推荐方认证资料失败");
        }
        
        // 分支B6: 保存银行信息结果检查
        if (!saveBankInfo) {
            return ResponseResult.fail("保存推荐方银行信息资料失败");
        }
        
        return ResponseResult.ok(vo);
        
    } catch (Exception e) {
        // 分支B7: 异常处理
        return ResponseResult.fail("保存推荐方认证资料失败");
    }
}
```

### 1.3 getAuditDetail方法分支结构

```java
@Override
public ResponseResult<RecommenderAuditDetailVo> getAuditDetail(Long recommenderId) {
    try {
        // 分支B1: recommenderId null检查
        if (recommenderId == null) {
            return ResponseResult.fail("推荐方ID不能为空");
        }
        
        // 分支B2: 推荐方信息存在性检查
        if (recommenderInfo == null) {
            return ResponseResult.fail("推荐方不存在");
        }
        
        // 分支B3: 审核记录存在性检查
        if (auditRecord != null) {
            // 设置审核记录信息
        } else {
            // 设置默认状态
        }
        
        // 分支B4: 推荐方类型检查
        if (recommenderInfo.getType() == 1) {
            // 个人类型处理
        } else if (recommenderInfo.getType() == 2) {
            // 企业类型处理
        }
        
        // 分支B5: 银行信息存在性检查
        if (bankInfo != null) {
            // 设置银行信息
        }
        
        return ResponseResult.ok(result);
        
    } catch (Exception e) {
        // 分支B6: 异常处理
        return ResponseResult.fail("查询推荐方审核详情失败");
    }
}
```

## 2. 分支详细分析

### 2.1 saveProfile方法分支分析

| 分支ID | 分支类型 | 分支条件 | 返回结果 | 测试方法 |
|--------|----------|----------|----------|----------|
| B1 | if/else | `dto == null` | 失败："参数为空" | `test02_SaveProfile_BranchB1_DtoNull` |
| B2 | if/else | `!dto.getIsDraft()` | 执行验证/跳过验证 | `test03-04_SaveProfile_BranchB2_*` |
| B3 | if/else | `recommenderInfo == null` | 创建/更新流程 | `test05-06_SaveProfile_BranchB3_*` |
| B4 | if/else | `existingInfo != null` | 失败："该身份证号/统一社会信用代码已被使用" | `test05_SaveProfile_BranchB3_B4_IdentifierDuplicate` |
| B5 | if/else | `!saveRecommenderInfo` | 失败："保存推荐方认证资料失败" | `test07_SaveProfile_BranchB5_SaveRecommenderInfoFailed` |
| B6 | if/else | `!saveBankInfo` | 失败："保存推荐方银行信息资料失败" | `test08_SaveProfile_BranchB6_SaveBankInfoFailed` |
| B7 | try/catch | `catch (Exception e)` | 失败："保存推荐方认证资料失败" | `test09_SaveProfile_BranchB7_ExceptionHandling` |

### 2.2 getAuditDetail方法分支分析

| 分支ID | 分支类型 | 分支条件 | 返回结果 | 测试方法 |
|--------|----------|----------|----------|----------|
| B1 | if/else | `recommenderId == null` | 失败："推荐方ID不能为空" | `test11_GetAuditDetail_BranchB1_RecommenderIdNull` |
| B2 | if/else | `recommenderInfo == null` | 失败："推荐方不存在" | `test12_GetAuditDetail_BranchB2_RecommenderNotExists` |
| B3 | if/else | `auditRecord != null` | 设置审核信息/默认状态 | `test13-14_GetAuditDetail_BranchB3_*` |
| B4 | if/else | `recommenderInfo.getType() == 1/2` | 个人/企业类型处理 | `test15-16_GetAuditDetail_BranchB4_*` |
| B5 | if/else | `bankInfo != null` | 设置银行信息/保持null | `test17-18_GetAuditDetail_BranchB5_*` |
| B6 | try/catch | `catch (Exception e)` | 失败："查询推荐方审核详情失败" | `test19_GetAuditDetail_BranchB6_ExceptionHandling` |

## 3. 测试方法执行顺序

### 3.1 测试执行顺序说明

测试方法按照以下顺序设计和执行：

1. **数据完整性验证**（最优先）
2. **saveProfile方法分支测试**（test02-test10）
3. **getAuditDetail方法分支测试**（test11-test20）
4. **综合场景测试**（test21）

### 3.2 具体测试方法列表

| 执行顺序 | 测试方法 | 目标分支 | 测试场景 |
|---------|----------|----------|----------|
| 01 | `test01_DataIntegrityVerification` | 数据完整性 | 验证测试环境和依赖 |
| 02 | `test02_SaveProfile_BranchB1_DtoNull` | saveProfile-B1 | dto为null |
| 03 | `test03_SaveProfile_BranchB2_DraftModeSkipValidation` | saveProfile-B2 | 草稿模式跳过验证 |
| 04 | `test04_SaveProfile_BranchB2_ValidationException` | saveProfile-B2 | 非草稿模式验证异常 |
| 05 | `test05_SaveProfile_BranchB3_B4_IdentifierDuplicate` | saveProfile-B3+B4 | 身份证号重复 |
| 06 | `test06_SaveProfile_BranchB3_CreateNewRecommender` | saveProfile-B3 | 创建新推荐方 |
| 07 | `test07_SaveProfile_BranchB5_SaveRecommenderInfoFailed` | saveProfile-B5 | 保存推荐方信息失败 |
| 08 | `test08_SaveProfile_BranchB6_SaveBankInfoFailed` | saveProfile-B6 | 保存银行信息失败 |
| 09 | `test09_SaveProfile_BranchB7_ExceptionHandling` | saveProfile-B7 | 异常处理 |
| 10 | `test10_SaveProfile_SuccessScenario` | saveProfile-成功 | 完整流程成功 |
| 11 | `test11_GetAuditDetail_BranchB1_RecommenderIdNull` | getAuditDetail-B1 | recommenderId为null |
| 12 | `test12_GetAuditDetail_BranchB2_RecommenderNotExists` | getAuditDetail-B2 | 推荐方不存在 |
| 13 | `test13_GetAuditDetail_BranchB3_AuditRecordExists` | getAuditDetail-B3 | 审核记录存在 |
| 14 | `test14_GetAuditDetail_BranchB3_AuditRecordNotExists` | getAuditDetail-B3 | 审核记录不存在 |
| 15 | `test15_GetAuditDetail_BranchB4_PersonalType` | getAuditDetail-B4 | 个人类型推荐方 |
| 16 | `test16_GetAuditDetail_BranchB4_EnterpriseType` | getAuditDetail-B4 | 企业类型推荐方 |
| 17 | `test17_GetAuditDetail_BranchB5_BankInfoExists` | getAuditDetail-B5 | 银行信息存在 |
| 18 | `test18_GetAuditDetail_BranchB5_BankInfoNotExists` | getAuditDetail-B5 | 银行信息不存在 |
| 19 | `test19_GetAuditDetail_BranchB6_ExceptionHandling` | getAuditDetail-B6 | 异常处理 |
| 20 | `test20_GetAuditDetail_SuccessScenario` | getAuditDetail-成功 | 完整流程成功 |
| 21 | `test21_ComprehensiveScenarioTest` | 综合 | 综合场景验证 |

## 4. 运行测试验证

### 4.1 运行单个测试方法

```bash
# 按顺序运行单个测试方法
mvn test -Dtest=TestRecommenderService#test01_DataIntegrityVerification
mvn test -Dtest=TestRecommenderService#test02_SaveProfile_BranchB1_DtoNull
mvn test -Dtest=TestRecommenderService#test11_GetAuditDetail_BranchB1_RecommenderIdNull
# ... 依此类推
```

### 4.2 运行所有测试

```bash
# 运行整个测试类
mvn test -Dtest=TestRecommenderService

# 运行测试并生成覆盖率报告
mvn clean test jacoco:report -Dtest=TestRecommenderService
```

### 4.3 运行特定方法的测试

```bash
# 运行saveProfile相关测试
mvn test -Dtest=TestRecommenderService#test*SaveProfile*

# 运行getAuditDetail相关测试
mvn test -Dtest=TestRecommenderService#test*GetAuditDetail*

# 运行异常处理测试
mvn test -Dtest=TestRecommenderService#test*Exception*
```

## 5. 预期测试结果

### 5.1 成功测试的日志输出

每个测试方法都会输出详细的日志信息：

```
=== 测试saveProfile方法 - 分支B1：dto为null的情况 ===
执行时间: 12ms
测试结果: {"code":400,"message":"参数为空","data":null}
✅ 分支B1测试通过：dto为null时正确返回失败结果
```

### 5.2 分支覆盖率验证

使用JaCoCo验证分支覆盖率：

1. 生成覆盖率报告：`mvn clean test jacoco:report`
2. 打开报告：`target/site/jacoco/index.html`
3. 导航到`RecommenderServiceImpl`类
4. 确认`saveProfile`和`getAuditDetail`方法的所有分支显示为绿色（已覆盖）

### 5.3 预期覆盖率结果

- **saveProfile方法分支覆盖率**: 100%
- **getAuditDetail方法分支覆盖率**: 100%
- **整体行覆盖率**: ≥95%
- **方法覆盖率**: 100%

## 6. Mock策略说明

### 6.1 Mock Bean配置

```java
@MockBean
private RecommenderMapper recommenderMapper;

@MockBean
private RecommenderBankMapper recommenderBankMapper;

@MockBean
private RecommenderAuditRecordMapper recommenderAuditRecordMapper;
```

### 6.2 Mock使用场景

| Mock对象 | Mock方法 | 使用场景 |
|---------|----------|----------|
| `recommenderMapper` | `selectOne()` | 模拟推荐方信息查询 |
| `recommenderMapper` | `selectById()` | 模拟按ID查询推荐方 |
| `recommenderMapper` | `insert()` | 模拟插入推荐方信息 |
| `recommenderMapper` | `updateById()` | 模拟更新推荐方信息 |
| `recommenderBankMapper` | `selectOne()` | 模拟银行信息查询 |
| `recommenderBankMapper` | `insert()` | 模拟插入银行信息 |
| `recommenderBankMapper` | `updateById()` | 模拟更新银行信息 |
| `recommenderAuditRecordMapper` | `selectOne()` | 模拟审核记录查询 |

### 6.3 Mock验证

每个测试方法都包含Mock调用验证：

```java
// 验证Mock调用次数和参数
verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
verify(recommenderBankMapper, times(1)).insert(any());
verify(recommenderAuditRecordMapper, times(1)).selectOne(any(), eq(false));
```

## 7. 测试数据管理

### 7.1 测试数据构建

```java
private RecommenderReqDto buildValidRecommenderReqDto() {
    RecommenderReqDto dto = new RecommenderReqDto();
    dto.setUserId(1L);
    dto.setName("张三");
    dto.setType(1); // 个人类型
    // ... 设置其他字段
    return dto;
}
```

### 7.2 Mock数据构建

```java
private RecommenderInfo buildMockRecommenderInfo() {
    RecommenderInfo recommenderInfo = new RecommenderInfo();
    recommenderInfo.setId(1L);
    recommenderInfo.setUserId(1L);
    // ... 设置其他字段
    return recommenderInfo;
}
```

## 8. 故障排除

### 8.1 常见问题

#### 问题1：Mock不生效
**解决方案**：
- 确保使用了`@MockBean`注解
- 检查Mock方法签名是否正确
- 验证`when().thenReturn()`设置

#### 问题2：验证异常测试失败
**解决方案**：
- 检查DTO字段设置是否正确
- 确认验证逻辑是否按预期执行
- 查看异常是否被正确捕获

#### 问题3：分支未覆盖
**解决方案**：
- 检查测试数据是否正确
- 确认Mock设置是否触发了目标分支
- 查看测试日志确认执行路径

### 8.2 调试技巧

1. **启用详细日志**：查看方法执行路径
2. **使用断点调试**：逐步验证分支执行
3. **检查Mock调用**：确认Mock被正确调用

## 9. 总结

### 9.1 测试特点

- **完整的分支覆盖**：saveProfile方法7个分支，getAuditDetail方法6个分支
- **有序的测试执行**：按逻辑顺序设计测试方法（test01-test21）
- **详细的分支标注**：每个测试方法都明确标注目标分支
- **专业的Mock策略**：使用Mock模拟各种场景
- **完整的验证机制**：包含断言和Mock调用验证

### 9.2 预期效果

通过这套测试方法，两个方法应该能够达到：
- ✅ 100%分支覆盖率
- ✅ 完整的异常处理测试
- ✅ 全面的业务逻辑验证
- ✅ 可靠的回归测试保障

按照本指南执行测试，您应该能够验证`saveProfile`和`getAuditDetail`方法的所有分支都被正确覆盖。
