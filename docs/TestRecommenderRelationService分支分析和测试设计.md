# TestRecommenderRelationService分支分析和测试设计

## 概述

基于`RecommenderRelationController`和`RecommenderRelationService`的代码分析，生成了完整的分支覆盖率测试类，专注于代码逻辑分支的完整测试。

## 代码分支分析

### Controller层分支分析

#### 1. getRecommenderHotelRelationList方法
```java
@GetMapping("/hotel-relation-list")
public ResponseResult<RecommenderHotelRelationListVo> getRecommenderHotelRelationList(
    @RequestHeader("Authorization") String token,
    RecommenderHotelRelationQueryDto queryDto) {
```

**分支情况**：
- **B1**: token解析失败，userInfoVo为null → 返回401错误
- **B2**: userId为null → 返回401错误  
- **B3**: 正常流程 → 调用Service方法

#### 2. getRecommenderGuideSupplierList方法
```java
@GetMapping("/guide-supplier-list")
public ResponseResult<RecommenderGuideRelationListVo> getRecommenderGuideSupplierList(
    @RequestHeader("Authorization") String token,
    RecommenderGuideRelationQueryDto queryDto) {
```

**分支情况**：
- **B1**: token解析失败，userInfoVo为null → 返回401错误
- **B2**: userId为null → 返回401错误
- **B3**: 正常流程 → 调用Service方法

### Service层分支分析

#### 1. getRecommenderHotelRelationList方法
```java
public ResponseResult<RecommenderHotelRelationListVo> getRecommenderHotelRelationList(RecommenderHotelRelationQueryDto queryDto)
```

**分支情况**：
- **B1**: `if (queryDto == null)` → 返回"查询条件不能为空"
- **B2**: `if (queryDto.getUserId() == null)` → 返回"用户ID不能为空"
- **B3**: `if (recommenderInfo == null)` → 返回"推荐方不存在"
- **B4**: 正常流程 → 查询并返回数据
- **B5**: `catch (Exception e)` → 返回"查询推荐方酒店关系列表失败"

#### 2. getRecommenderGuideRelationList方法
```java
public ResponseResult<RecommenderGuideRelationListVo> getRecommenderGuideRelationList(RecommenderGuideRelationQueryDto queryDto)
```

**分支情况**：
- **B1**: `if (queryDto == null)` → 返回"查询条件不能为空"
- **B2**: `if (queryDto.getUserId() == null)` → 返回"用户ID不能为空"
- **B3**: `if (recommenderInfo == null)` → 返回"推荐方不存在"
- **B4**: 正常流程 → 查询并返回数据
- **B5**: `catch (Exception e)` → 返回"查询推荐方导游关系列表失败"

## 测试类设计

### 测试方法列表（共12个）

| 序号 | 测试方法 | 测试目标 | 分支类型 |
|------|----------|----------|----------|
| 01 | test01_DataIntegrityVerification | 数据完整性验证 | 基础验证 |
| 02 | test02_GetHotelRelationList_BranchB1_QueryDtoNull | queryDto为null | 参数验证 |
| 03 | test03_GetHotelRelationList_BranchB2_UserIdNull | userId为null | 参数验证 |
| 04 | test04_GetHotelRelationList_BranchB3_RecommenderNotExists | 推荐方不存在 | 业务验证 |
| 05 | test05_GetHotelRelationList_BranchB4_SuccessScenario | 正常流程 | 成功场景 |
| 06 | test06_GetHotelRelationList_BranchB5_ExceptionHandling | 异常处理 | 异常分支 |
| 07 | test07_GetGuideRelationList_BranchB1_QueryDtoNull | queryDto为null | 参数验证 |
| 08 | test08_GetGuideRelationList_BranchB2_UserIdNull | userId为null | 参数验证 |
| 09 | test09_GetGuideRelationList_BranchB3_RecommenderNotExists | 推荐方不存在 | 业务验证 |
| 10 | test10_GetGuideRelationList_BranchB4_SuccessScenario | 正常流程 | 成功场景 |
| 11 | test11_GetGuideRelationList_BranchB5_ExceptionHandling | 异常处理 | 异常分支 |
| 12 | test12_ComprehensiveScenarioTest | 综合场景 | 集成测试 |

### 测试特点

#### 1. 分支覆盖完整性
- ✅ **参数验证分支**：覆盖所有null参数情况
- ✅ **业务逻辑分支**：覆盖推荐方存在性验证
- ✅ **正常流程分支**：覆盖完整的业务流程
- ✅ **异常处理分支**：覆盖所有异常情况

#### 2. Mock策略设计
```java
@MockBean
private RecommenderMapper recommenderMapper;

@MockBean
private RecommenderRelationMapper recommenderRelationMapper;

@MockBean
private UserInfoService userInfoService;
```

#### 3. 测试数据构建
- **酒店关系查询DTO**：`buildValidHotelRelationQueryDto()`
- **导游关系查询DTO**：`buildValidGuideRelationQueryDto()`
- **推荐方信息**：`buildMockRecommenderInfo()`
- **酒店关系列表**：`buildMockHotelRelationList()`
- **导游关系列表**：`buildMockGuideRelationList()`

## 关键测试场景

### 1. 参数验证测试
```java
// 测试queryDto为null的情况
ResponseResult<RecommenderHotelRelationListVo> result = 
    recommenderRelationService.getRecommenderHotelRelationList(null);
assertThat(result.getMessage()).isEqualTo("查询条件不能为空");
```

### 2. 业务逻辑测试
```java
// 测试推荐方不存在的情况
when(recommenderMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(null);
ResponseResult<RecommenderHotelRelationListVo> result = 
    recommenderRelationService.getRecommenderHotelRelationList(queryDto);
assertThat(result.getMessage()).isEqualTo("推荐方不存在");
```

### 3. 正常流程测试
```java
// 测试正常查询流程
RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);
mockHotelRelationQueryResults();

ResponseResult<RecommenderHotelRelationListVo> result = 
    recommenderRelationService.getRecommenderHotelRelationList(queryDto);
assertThat(result.getCode()).isEqualTo(200);
```

### 4. 异常处理测试
```java
// 测试异常处理
when(recommenderMapper.selectOne(any(), eq(false)))
    .thenThrow(new RuntimeException("数据库查询异常"));
ResponseResult<RecommenderHotelRelationListVo> result = 
    recommenderRelationService.getRecommenderHotelRelationList(queryDto);
assertThat(result.getMessage()).isEqualTo("查询推荐方酒店关系列表失败");
```

## Mock设计详情

### 1. 推荐方信息Mock
```java
RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
when(recommenderMapper.selectOne(any(LambdaQueryWrapper.class), eq(false)))
    .thenReturn(mockRecommenderInfo);
```

### 2. 酒店关系查询Mock
```java
List<RecommenderHotelRelationVo> mockRelationList = buildMockHotelRelationList();
when(recommenderRelationMapper.selectRecommenderHotelRelationList(any()))
    .thenReturn(mockRelationList);
when(recommenderRelationMapper.selectRecommenderHotelRelationCount(any()))
    .thenReturn(2L);
when(recommenderRelationMapper.selectRecommenderHotelRelationStatistics(any()))
    .thenReturn(mockStatistics);
```

### 3. 导游关系查询Mock
```java
List<RecommenderGuideRelationVo> mockRelationList = buildMockGuideRelationList();
when(recommenderRelationMapper.selectRecommenderGuideRelationList(any()))
    .thenReturn(mockRelationList);
when(recommenderRelationMapper.selectRecommenderGuideRelationCount(any()))
    .thenReturn(2L);
when(recommenderRelationMapper.selectServiceCountByGuideId(any()))
    .thenReturn(5);
when(recommenderRelationMapper.selectOrderCountByGuideId(any(), any(), any()))
    .thenReturn(10);
```

### 4. 手机号解密Mock
```java
MobileInfo mockMobileInfo = new MobileInfo();
mockMobileInfo.setMobile("13800138000");
when(userInfoService.mobileAesDecryptPublic(any())).thenReturn(mockMobileInfo);
```

## 运行方式

### 1. 运行所有测试
```bash
mvn test -Dtest="tripai.recommend.system.mock.TestRecommenderRelationService"
```

### 2. 运行特定测试
```bash
# 运行数据完整性验证
mvn test -Dtest="TestRecommenderRelationService#test01_DataIntegrityVerification"

# 运行酒店关系列表测试
mvn test -Dtest="TestRecommenderRelationService#test02_GetHotelRelationList_BranchB1_QueryDtoNull"

# 运行导游关系列表测试
mvn test -Dtest="TestRecommenderRelationService#test07_GetGuideRelationList_BranchB1_QueryDtoNull"
```

### 3. 生成覆盖率报告
```bash
mvn clean test jacoco:report -Dtest="TestRecommenderRelationService"
```

## 预期覆盖率

### 方法覆盖率
- **getRecommenderHotelRelationList**: 100%
- **getRecommenderGuideRelationList**: 100%
- **validateAndProcessQueryParams**: 100%
- **validateAndProcessGuideQueryParams**: 100%
- **processHotelRelationDetail**: 100%
- **processGuideRelationDetail**: 100%

### 分支覆盖率
- **参数验证分支**: 100%
- **业务逻辑分支**: 100%
- **异常处理分支**: 100%
- **整体分支覆盖**: 预期达到95%以上

## 测试质量保证

### 1. 完整性
- 覆盖所有公共方法
- 覆盖所有分支路径
- 覆盖所有异常情况

### 2. 独立性
- 每个测试方法独立运行
- Mock状态正确重置
- 测试数据隔离

### 3. 可维护性
- 清晰的测试方法命名
- 详细的分支注释
- 合理的辅助方法抽取

### 4. 可读性
- 详细的日志输出
- 清晰的断言验证
- 完整的测试文档

## 总结

通过这个测试类，可以实现：

1. **完整的分支覆盖**：覆盖Service层的所有代码分支
2. **高质量的测试**：使用Mock数据，快速执行，结果可靠
3. **清晰的测试结构**：按照分支顺序组织，易于理解和维护
4. **详细的测试文档**：每个测试方法都有明确的分支说明

这个测试类为`RecommenderRelationService`提供了完整的分支覆盖率测试，确保代码质量和业务逻辑的正确性。
