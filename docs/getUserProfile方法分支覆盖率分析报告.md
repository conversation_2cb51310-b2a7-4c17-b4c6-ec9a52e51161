# getUserProfile方法分支覆盖率分析报告

## 1. 当前测试用例覆盖情况分析

### 1.1 现有测试方法
当前`TestUserInfoService`中针对`getUserProfile()`方法的测试用例：

1. **testGetUserProfileSuccess** - 正常流程测试
2. **testGetUserProfileWithInvalidUserId** - 无效用户ID测试  
3. **testGetUserProfileWithNullUserId** - 空用户ID测试
4. **testGetMultipleUserProfiles** - 批量查询测试
5. **testGetUserProfilePerformance** - 性能测试

### 1.2 覆盖率评估
**当前分支覆盖率：约40%**

## 2. 方法分支分析

### 2.1 主要执行路径分析

```java
@Override
public ResponseResult<UserProfileVo> getUserProfile(Long userId) {
    log.info("获取用户个人中心信息开始，userId：{}", userId);
    
    try {
        // 分支1: userId null检查
        if (userId == null) {
            log.error("获取用户个人中心信息失败，userId为空");
            return ResponseResult.fail("用户userId为空");
        }

        // 分支2: 用户基本信息查询
        UserInfo userInfo = this.getById(userId);
        if (userInfo == null) {
            log.error("获取用户个人中心信息失败，用户不存在，userId：{}", userId);
            return ResponseResult.fail("用户不存在");
        }

        // 分支3: 推荐方信息查询
        LambdaQueryWrapper<RecommenderInfo> recommenderWrapper = new LambdaQueryWrapper<>();
        recommenderWrapper.eq(RecommenderInfo::getUserId, userId)
                .eq(RecommenderInfo::getIsDel, 0);
        RecommenderInfo recommenderInfo = recommenderMapper.selectOne(recommenderWrapper, false);

        // 分支4: 社交账户信息查询
        LambdaQueryWrapper<SocialUser> socialWrapper = new LambdaQueryWrapper<>();
        socialWrapper.eq(SocialUser::getUid, userId)
                .eq(SocialUser::getSource, AuthDefaultSource.WECHAT_OPEN);
        SocialUser wechatUser = socialUserMapper.selectOne(socialWrapper, false);

        // 分支5: 银行账户信息查询
        RecommenderBank bankInfo = null;
        if (recommenderInfo != null) {
            LambdaQueryWrapper<RecommenderBank> bankWrapper = new LambdaQueryWrapper<>();
            bankWrapper.eq(RecommenderBank::getRecommenderId, recommenderInfo.getId())
                    .eq(RecommenderBank::getIsDel, 0);
            bankInfo = recommenderBankMapper.selectOne(bankWrapper, false);
        }

        // 分支6: 构建返回结果
        UserProfileVo result = buildUserProfileVo(userInfo, recommenderInfo, wechatUser, bankInfo);

        log.info("获取用户个人中心信息成功，userId：{}", userId);
        return ResponseResult.ok(result);

    } catch (Exception e) {
        log.error("获取用户个人中心信息失败，userId：{}", userId, e);
        return ResponseResult.fail("获取用户个人中心信息失败");
    }
}
```

### 2.2 buildUserProfileVo方法分支分析

```java
private UserProfileVo buildUserProfileVo(UserInfo userInfo, RecommenderInfo recommenderInfo,
                                         SocialUser wechatUser, RecommenderBank bankInfo) {
    UserProfileVo vo = new UserProfileVo();
    vo.setUserId(userInfo.getId());

    // 分支7: 推荐方信息处理
    if (recommenderInfo != null) {
        vo.setAvatarUrl(recommenderInfo.getAvatarUrl());
        vo.setName(recommenderInfo.getName());
        vo.setIdentityType(recommenderInfo.getType());
        vo.setInvitationCode(recommenderInfo.getInvitationCode());
        vo.setIdentifier(recommenderInfo.getIdentifier());
    }

    // 分支8: 手机号脱敏处理
    if (StrUtil.isNotBlank(userInfo.getMobile())) {
        vo.setMobile(DataMaskingUtil.maskMobile(userInfo.getMobile()));
    }
    
    // 分支9: 邮箱脱敏处理
    if (StrUtil.isNotBlank(userInfo.getEmail())) {
        vo.setEmail(DataMaskingUtil.maskEmail(userInfo.getEmail()));
    }

    // 分支10: 微信绑定状态（三元运算符）
    vo.setWechatBindStatus(wechatUser != null ? 1 : 0);

    // 分支11: 银行卡信息处理
    if (bankInfo != null) {
        vo.setBankCardNo(DataMaskingUtil.maskBankCardNo(bankInfo.getBankCardNo()));
        vo.setBankName(bankInfo.getBankName());
        vo.setBankValidateStatus(bankInfo.getValidateStatus());
    }
    
    return vo;
}
```

## 3. 缺失的测试场景识别

### 3.1 主方法缺失的分支测试

1. **异常处理分支** - 数据库查询异常
2. **推荐方信息为null的情况**
3. **微信用户信息为null的情况**
4. **银行信息为null的情况**
5. **推荐方存在但银行信息不存在的情况**

### 3.2 buildUserProfileVo方法缺失的分支测试

1. **手机号为空/null的情况**
2. **邮箱为空/null的情况**
3. **推荐方信息为null时的字段设置**
4. **银行信息为null时的字段设置**
5. **微信用户存在和不存在的两种情况**

### 3.3 边界条件和特殊值

1. **用户ID为0的情况**
2. **用户ID为负数的情况**
3. **用户ID为Long.MAX_VALUE的情况**
4. **数据库连接异常的情况**
5. **数据脱敏工具异常的情况**

## 4. 100%分支覆盖率测试用例补充

### 4.1 缺失分支的具体测试场景

| 分支编号 | 分支条件 | 当前覆盖状态 | 缺失场景 |
|---------|---------|-------------|---------|
| 1 | userId == null | ✅ 已覆盖 | - |
| 2 | userInfo == null | ✅ 已覆盖 | - |
| 3 | recommenderInfo 查询 | ❌ 未完全覆盖 | 推荐方不存在的情况 |
| 4 | wechatUser 查询 | ❌ 未覆盖 | 微信用户存在/不存在 |
| 5 | bankInfo 查询 | ❌ 未覆盖 | 银行信息存在/不存在 |
| 6 | 异常处理 | ❌ 未覆盖 | 数据库异常、其他异常 |
| 7 | recommenderInfo != null | ❌ 未完全覆盖 | null和非null情况 |
| 8 | StrUtil.isNotBlank(mobile) | ❌ 未覆盖 | 手机号空/非空 |
| 9 | StrUtil.isNotBlank(email) | ❌ 未覆盖 | 邮箱空/非空 |
| 10 | wechatUser != null ? 1 : 0 | ❌ 未覆盖 | 三元运算符两个分支 |
| 11 | bankInfo != null | ❌ 未覆盖 | 银行信息空/非空 |

### 4.2 当前覆盖率不足的原因

1. **测试数据单一**: 只测试了"正常用户"场景
2. **缺少边界条件**: 没有测试各种null值情况
3. **异常场景缺失**: 没有模拟数据库异常等情况
4. **组合场景不足**: 没有测试不同数据组合的情况

## 5. 测试覆盖率验证方法

### 5.1 推荐的测试覆盖率工具

#### 5.1.1 JaCoCo (推荐)
```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.8</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

#### 5.1.2 使用命令
```bash
# 运行测试并生成覆盖率报告
mvn clean test jacoco:report

# 查看覆盖率报告
open target/site/jacoco/index.html
```

#### 5.1.3 IDE集成
- **IntelliJ IDEA**: Run with Coverage
- **Eclipse**: EclEmma插件
- **VS Code**: Coverage Gutters插件

### 5.2 覆盖率指标说明

- **行覆盖率 (Line Coverage)**: 执行的代码行数比例
- **分支覆盖率 (Branch Coverage)**: 执行的分支数比例  
- **方法覆盖率 (Method Coverage)**: 调用的方法数比例
- **类覆盖率 (Class Coverage)**: 加载的类数比例

### 5.3 目标覆盖率标准

- **分支覆盖率**: 100% (关键业务方法)
- **行覆盖率**: ≥95%
- **方法覆盖率**: 100%
- **类覆盖率**: 100%

## 6. 具体的测试方法实现建议

基于以上分析，需要补充以下测试方法来达到100%分支覆盖率：

1. **testGetUserProfileWithNoRecommenderInfo** - 用户存在但推荐方信息不存在
2. **testGetUserProfileWithNoWechatBinding** - 用户未绑定微信
3. **testGetUserProfileWithWechatBinding** - 用户已绑定微信
4. **testGetUserProfileWithNoBankInfo** - 推荐方存在但无银行信息
5. **testGetUserProfileWithBankInfo** - 推荐方存在且有银行信息
6. **testGetUserProfileWithEmptyMobile** - 用户手机号为空
7. **testGetUserProfileWithEmptyEmail** - 用户邮箱为空
8. **testGetUserProfileWithDatabaseException** - 数据库查询异常
9. **testGetUserProfileWithMaskingException** - 数据脱敏异常
10. **testGetUserProfileBoundaryValues** - 边界值测试

这些测试方法将在下一部分提供具体的实现代码。
