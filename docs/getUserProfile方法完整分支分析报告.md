# getUserProfile方法完整分支分析报告

## 1. 分支分析任务

### 1.1 方法概述
- **方法位置**: `src/main/java/tripai/recommend/system/service/impl/UserInfoServiceImpl.java`
- **方法名**: `getUserProfile(Long userId)`
- **返回类型**: `ResponseResult<UserProfileVo>`
- **主要功能**: 获取用户个人中心信息，包括基本信息、推荐方信息、微信绑定状态、银行信息等

### 1.2 方法结构分析
方法包含两个主要部分：
1. **主方法**: `getUserProfile(Long userId)` (第521-569行)
2. **辅助方法**: `buildUserProfileVo(...)` (第575-609行)

## 2. 详细分支分析

### 2.1 主方法分支 (getUserProfile)

#### 分支B1: userId null检查
```java
if (userId == null) {
    log.error("获取用户个人中心信息失败，userId为空");
    return ResponseResult.fail("用户userId为空");
}
```
- **条件**: `userId == null`
- **True分支**: 返回失败结果，错误信息"用户userId为空"
- **False分支**: 继续执行后续逻辑

#### 分支B2: 用户基本信息存在性检查
```java
UserInfo userInfo = this.getById(userId);
if (userInfo == null) {
    log.error("获取用户个人中心信息失败，用户不存在，userId：{}", userId);
    return ResponseResult.fail("用户不存在");
}
```
- **条件**: `userInfo == null`
- **True分支**: 返回失败结果，错误信息"用户不存在"
- **False分支**: 继续执行后续逻辑

#### 分支B3: 推荐方信息查询
```java
RecommenderInfo recommenderInfo = recommenderMapper.selectOne(recommenderWrapper, false);
```
- **可能结果**: `recommenderInfo` 可能为null或非null
- **影响**: 影响后续银行信息查询和VO构建

#### 分支B4: 社交用户信息查询
```java
SocialUser wechatUser = socialUserMapper.selectOne(socialWrapper, false);
```
- **可能结果**: `wechatUser` 可能为null或非null
- **影响**: 影响微信绑定状态设置

#### 分支B5: 银行信息查询条件
```java
RecommenderBank bankInfo = null;
if (recommenderInfo != null) {
    LambdaQueryWrapper<RecommenderBank> bankWrapper = new LambdaQueryWrapper<>();
    bankWrapper.eq(RecommenderBank::getRecommenderId, recommenderInfo.getId())
            .eq(RecommenderBank::getIsDel, 0);
    bankInfo = recommenderBankMapper.selectOne(bankWrapper, false);
}
```
- **条件**: `recommenderInfo != null`
- **True分支**: 查询银行信息，`bankInfo`可能为null或非null
- **False分支**: `bankInfo`保持为null

#### 分支B6: 异常处理
```java
try {
    // 主要业务逻辑
} catch (Exception e) {
    log.error("获取用户个人中心信息失败，userId：{}", userId, e);
    return ResponseResult.fail("获取用户个人中心信息失败");
}
```
- **条件**: 任何异常发生
- **True分支**: 捕获异常，返回失败结果
- **False分支**: 正常执行，返回成功结果

### 2.2 辅助方法分支 (buildUserProfileVo)

#### 分支B7: 推荐方信息处理
```java
if (recommenderInfo != null) {
    vo.setAvatarUrl(recommenderInfo.getAvatarUrl());
    vo.setName(recommenderInfo.getName());
    vo.setIdentityType(recommenderInfo.getType());
    vo.setInvitationCode(recommenderInfo.getInvitationCode());
    vo.setIdentifier(recommenderInfo.getIdentifier());
}
```
- **条件**: `recommenderInfo != null`
- **True分支**: 设置推荐方相关字段
- **False分支**: 推荐方字段保持默认值(null)

#### 分支B8: 手机号脱敏处理
```java
if (StrUtil.isNotBlank(userInfo.getMobile())) {
    vo.setMobile(DataMaskingUtil.maskMobile(userInfo.getMobile()));
}
```
- **条件**: `StrUtil.isNotBlank(userInfo.getMobile())`
- **True分支**: 执行手机号脱敏处理
- **False分支**: 手机号字段保持默认值(null)

#### 分支B9: 邮箱脱敏处理
```java
if (StrUtil.isNotBlank(userInfo.getEmail())) {
    vo.setEmail(DataMaskingUtil.maskEmail(userInfo.getEmail()));
}
```
- **条件**: `StrUtil.isNotBlank(userInfo.getEmail())`
- **True分支**: 执行邮箱脱敏处理
- **False分支**: 邮箱字段保持默认值(null)

#### 分支B10: 微信绑定状态处理
```java
if (wechatUser != null){
    vo.setWechatBindStatus(Objects.equals(wechatUser.getSource(), QrChannelBindEnum.BIND_WECHAT.getDesc()) ? 1 : 0);
}
```
- **条件1**: `wechatUser != null`
  - **True分支**: 进入三元运算符判断
  - **False分支**: 微信绑定状态保持默认值(null)
- **条件2**: `Objects.equals(wechatUser.getSource(), QrChannelBindEnum.BIND_WECHAT.getDesc())`
  - **True分支**: 设置微信绑定状态为1
  - **False分支**: 设置微信绑定状态为0

#### 分支B11: 银行信息处理
```java
if (bankInfo != null) {
    vo.setBankCardNo(DataMaskingUtil.maskBankCardNo(bankInfo.getBankCardNo()));
    vo.setBankName(bankInfo.getBankName());
    vo.setBankValidateStatus(bankInfo.getValidateStatus());
}
```
- **条件**: `bankInfo != null`
- **True分支**: 设置银行相关字段（包含脱敏处理）
- **False分支**: 银行字段保持默认值(null)

## 3. 分支覆盖率评估

### 3.1 已覆盖分支

| 分支ID | 分支条件 | 已覆盖测试方法 | 覆盖状态 |
|--------|----------|----------------|----------|
| B1 | `userId == null` | `testGetUserProfileWithNullUserId` | ✅ True分支 |
| B2 | `userInfo == null` | `testGetUserProfileWithInvalidUserId` | ✅ True分支 |
| B3 | `recommenderInfo` 查询结果 | `testGetUserProfileWithNoRecommenderInfo` | ⚠️ 部分覆盖 |
| B4 | `wechatUser` 查询结果 | `testGetUserProfileWithNoWechatBinding`, `testGetUserProfileWithWechatBinding` | ⚠️ 部分覆盖 |
| B5 | `recommenderInfo != null` | `testGetUserProfileWithNoBankInfo`, `testGetUserProfileWithBankInfo` | ⚠️ 部分覆盖 |
| B6 | 异常处理 | `testGetUserProfileWithDatabaseException`, `testGetUserProfileWithMaskingException` | ❌ 未完全覆盖 |
| B7 | `recommenderInfo != null` | `testGetUserProfileWithNoRecommenderInfo` | ⚠️ 部分覆盖 |
| B8 | `StrUtil.isNotBlank(userInfo.getMobile())` | `testGetUserProfileWithEmptyMobile` | ⚠️ 部分覆盖 |
| B9 | `StrUtil.isNotBlank(userInfo.getEmail())` | `testGetUserProfileWithEmptyEmail`, `testGetUserProfileWithNotEmptyEmail` | ⚠️ 部分覆盖 |
| B10 | `wechatUser != null` 和三元运算符 | 现有测试 | ❌ 未完全覆盖 |
| B11 | `bankInfo != null` | `testGetUserProfileWithNoBankInfo`, `testGetUserProfileWithBankInfo` | ⚠️ 部分覆盖 |

### 3.2 覆盖率统计
- **完全覆盖**: 2个分支 (18%)
- **部分覆盖**: 7个分支 (64%)
- **未覆盖**: 2个分支 (18%)
- **总体覆盖率**: 约60%

## 4. 缺失的分支测试清单

### 4.1 高优先级缺失测试

#### 4.1.1 异常处理分支完整覆盖
- **缺失**: 真实的异常触发测试
- **需要**: Mock各种数据库查询异常、脱敏工具异常

#### 4.1.2 微信绑定状态三元运算符分支
- **缺失**: `Objects.equals(wechatUser.getSource(), QrChannelBindEnum.BIND_WECHAT.getDesc())` 的两个分支
- **需要**: 测试微信用户存在但source不匹配的情况

#### 4.1.3 数据脱敏分支完整覆盖
- **缺失**: 手机号和邮箱的True/False分支完整覆盖
- **需要**: 确保测试数据包含空值和非空值的情况

### 4.2 中优先级缺失测试

#### 4.2.1 数据组合场景
- **缺失**: 各种数据存在/不存在的组合测试
- **需要**: 系统性的组合场景测试

#### 4.2.2 边界条件
- **缺失**: 特殊字符、极长字符串等边界条件
- **需要**: 更全面的边界值测试

### 4.3 低优先级缺失测试

#### 4.3.1 性能测试
- **缺失**: 大量数据查询的性能测试
- **需要**: 并发访问、内存泄漏等测试

## 5. 测试数据依赖分析

### 5.1 数据库表依赖
- `user_info`: 用户基本信息
- `recommender_info`: 推荐方信息
- `social_user`: 社交用户信息
- `recommender_bank`: 银行信息

### 5.2 测试数据要求
为了达到100%分支覆盖，需要准备以下测试数据：

```sql
-- 基础用户数据
INSERT INTO user_info (id, mobile, email) VALUES
(1, '加密手机号1', '<EMAIL>'),  -- 完整信息
(2, '加密手机号2', NULL),                -- 无邮箱
(3, NULL, '<EMAIL>'),          -- 无手机号
(4, NULL, NULL),                         -- 无手机号和邮箱
(5, '加密手机号5', ''),                  -- 空邮箱
(6, '', '<EMAIL>');           -- 空手机号

-- 推荐方信息
INSERT INTO recommender_info (user_id, name, type) VALUES
(1, '推荐方1', 1),  -- 用户1有推荐方信息
(3, '推荐方3', 1);  -- 用户3有推荐方信息

-- 社交用户信息
INSERT INTO social_user (uid, source) VALUES
(1, 'WECHAT_OPEN'),     -- 用户1绑定微信
(2, 'OTHER_PLATFORM');  -- 用户2绑定其他平台

-- 银行信息
INSERT INTO recommender_bank (recommender_id, bank_card_no, bank_name) VALUES
(1, '6222****1234', '工商银行');  -- 推荐方1有银行信息
```

## 6. 下一步行动计划

### 6.1 立即执行
1. 补充异常处理分支的Mock测试
2. 完善微信绑定状态的三元运算符测试
3. 确保数据脱敏分支的完整覆盖

### 6.2 短期执行
1. 准备完整的测试数据
2. 添加数据组合场景测试
3. 完善边界条件测试

### 6.3 长期维护
1. 集成到CI/CD流水线
2. 定期检查覆盖率
3. 随业务逻辑变更更新测试

## 7. 预期结果

完成所有缺失测试后，预期达到：
- **分支覆盖率**: 100%
- **行覆盖率**: ≥95%
- **方法覆盖率**: 100%
- **异常处理覆盖**: 100%
