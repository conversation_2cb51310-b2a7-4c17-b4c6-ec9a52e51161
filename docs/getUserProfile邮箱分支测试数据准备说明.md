# getUserProfile邮箱分支测试数据准备说明

## 问题分析

您遇到的问题是测试无法覆盖到`if (StrUtil.isNotBlank(userInfo.getEmail()))`这个分支。这个问题的根本原因是**测试数据不足**，具体表现为：

1. **缺少邮箱不为空的用户数据**（无法覆盖True分支）
2. **缺少邮箱为空的用户数据**（无法覆盖False分支）

## 分支覆盖要求

要实现100%的分支覆盖，需要测试以下两种情况：

### True分支：`StrUtil.isNotBlank(userInfo.getEmail()) == true`
- **条件**：用户的email字段不为null且不为空字符串
- **执行逻辑**：`vo.setEmail(DataMaskingUtil.maskEmail(userInfo.getEmail()));`
- **预期结果**：邮箱被脱敏处理，包含"*"和"@"字符

### False分支：`StrUtil.isNotBlank(userInfo.getEmail()) == false`
- **条件**：用户的email字段为null或为空字符串
- **执行逻辑**：跳过脱敏处理
- **预期结果**：邮箱字段为null

## 测试数据准备方案

### 方案1：数据库准备测试数据（推荐）

#### 1.1 准备有邮箱的用户数据（True分支）
```sql
-- 为用户ID 1-5 设置邮箱数据
UPDATE user_info SET email = '<EMAIL>' WHERE id = 1;
UPDATE user_info SET email = '<EMAIL>' WHERE id = 2;
UPDATE user_info SET email = '<EMAIL>' WHERE id = 3;
UPDATE user_info SET email = '<EMAIL>' WHERE id = 4;
UPDATE user_info SET email = '<EMAIL>' WHERE id = 5;

-- 验证数据
SELECT id, email FROM user_info WHERE id IN (1,2,3,4,5);
```

#### 1.2 准备邮箱为空的用户数据（False分支）
```sql
-- 为用户ID 6-10 设置邮箱为空
UPDATE user_info SET email = NULL WHERE id = 6;
UPDATE user_info SET email = NULL WHERE id = 7;
UPDATE user_info SET email = '' WHERE id = 8;  -- 空字符串
UPDATE user_info SET email = '   ' WHERE id = 9;  -- 空白字符串
UPDATE user_info SET email = NULL WHERE id = 10;

-- 验证数据
SELECT id, email FROM user_info WHERE id IN (6,7,8,9,10);
```

#### 1.3 创建完整的测试用户数据
```sql
-- 如果用户不存在，先创建用户
INSERT INTO user_info (id, mobile, email, create_time, update_time) VALUES
(1, '13800138001', '<EMAIL>', NOW(), NOW()),
(2, '13800138002', '<EMAIL>', NOW(), NOW()),
(3, '13800138003', '<EMAIL>', NOW(), NOW()),
(4, '13800138004', '<EMAIL>', NOW(), NOW()),
(5, '13800138005', '<EMAIL>', NOW(), NOW()),
(6, '13800138006', NULL, NOW(), NOW()),
(7, '13800138007', NULL, NOW(), NOW()),
(8, '13800138008', '', NOW(), NOW()),
(9, '13800138009', '   ', NOW(), NOW()),
(10, '13800138010', NULL, NOW(), NOW())
ON DUPLICATE KEY UPDATE
mobile = VALUES(mobile),
email = VALUES(email),
update_time = NOW();
```

### 方案2：使用测试配置文件

#### 2.1 创建测试专用的数据源配置
```yaml
# application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  sql:
    init:
      schema-locations: classpath:schema-test.sql
      data-locations: classpath:data-test.sql
```

#### 2.2 创建测试数据脚本
```sql
-- data-test.sql
INSERT INTO user_info (id, mobile, email, create_time, update_time) VALUES
-- 有邮箱的用户（True分支）
(1, '13800138001', '<EMAIL>', NOW(), NOW()),
(2, '13800138002', '<EMAIL>', NOW(), NOW()),
(3, '13800138003', '<EMAIL>', NOW(), NOW()),
-- 无邮箱的用户（False分支）
(6, '13800138006', NULL, NOW(), NOW()),
(7, '13800138007', '', NOW(), NOW()),
(8, '13800138008', '   ', NOW(), NOW());
```

### 方案3：使用@Sql注解准备数据

```java
@Test
@Sql(statements = {
    "INSERT INTO user_info (id, mobile, email) VALUES (1, '13800138001', '<EMAIL>')",
    "INSERT INTO user_info (id, mobile, email) VALUES (6, '13800138006', NULL)"
})
public void testGetUserProfileEmailBranchCoverage() {
    // 测试True分支
    ResponseResult<UserProfileVo> result1 = userInfoService.getUserProfile(1L);
    assertThat(result1.getData().getEmail()).contains("*");
    
    // 测试False分支
    ResponseResult<UserProfileVo> result2 = userInfoService.getUserProfile(6L);
    assertThat(result2.getData().getEmail()).isNull();
}
```

## 验证测试数据

### 验证脚本
```sql
-- 检查测试数据是否准备正确
SELECT 
    id,
    email,
    CASE 
        WHEN email IS NULL THEN 'NULL (False分支)'
        WHEN email = '' THEN 'EMPTY (False分支)'
        WHEN TRIM(email) = '' THEN 'BLANK (False分支)'
        ELSE CONCAT('NOT_BLANK (True分支): ', email)
    END as branch_type
FROM user_info 
WHERE id IN (1,2,3,4,5,6,7,8,9,10)
ORDER BY id;
```

### 预期结果
```
+----+------------------+--------------------------------+
| id | email            | branch_type                    |
+----+------------------+--------------------------------+
|  1 | <EMAIL>| NOT_BLANK (True分支): <EMAIL> |
|  2 | <EMAIL>   | NOT_BLANK (True分支): <EMAIL>    |
|  3 | <EMAIL>   | NOT_BLANK (True分支): <EMAIL>    |
|  4 | <EMAIL> | NOT_BLANK (True分支): <EMAIL>  |
|  5 | <EMAIL>   | NOT_BLANK (True分支): <EMAIL>    |
|  6 | NULL             | NULL (False分支)               |
|  7 | NULL             | NULL (False分支)               |
|  8 |                  | EMPTY (False分支)              |
|  9 |                  | BLANK (False分支)              |
| 10 | NULL             | NULL (False分支)               |
+----+------------------+--------------------------------+
```

## 运行测试验证

### 1. 运行邮箱分支专项测试
```bash
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfileEmailBranchCoverageSpecific
```

### 2. 运行邮箱脱敏功能测试
```bash
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfileEmailMaskingDetailedVerification
```

### 3. 运行邮箱为空测试
```bash
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfileEmptyEmailDetailedVerification
```

### 4. 生成覆盖率报告
```bash
mvn clean test jacoco:report -Dtest=TestUserInfoServiceGetUserProfile
```

## 覆盖率验证

### 查看JaCoCo报告
1. 打开 `target/site/jacoco/index.html`
2. 导航到 `UserInfoServiceImpl` 类
3. 查看 `getUserProfile` 方法
4. 确认 `if (StrUtil.isNotBlank(userInfo.getEmail()))` 分支显示为绿色

### 预期覆盖率结果
- **分支覆盖率**: 100%
- **True分支**: 已覆盖（绿色）
- **False分支**: 已覆盖（绿色）

## 常见问题解决

### 问题1：仍然无法覆盖True分支
**原因**: 数据库中没有邮箱不为空的用户
**解决方案**: 
```sql
UPDATE user_info SET email = '<EMAIL>' WHERE id = 1;
```

### 问题2：仍然无法覆盖False分支
**原因**: 数据库中所有用户都有邮箱
**解决方案**: 
```sql
UPDATE user_info SET email = NULL WHERE id = 6;
```

### 问题3：测试数据被其他测试影响
**解决方案**: 使用事务回滚或独立的测试数据库
```java
@Test
@Transactional
@Rollback
public void testGetUserProfileEmailBranch() {
    // 测试逻辑
}
```

### 问题4：StrUtil.isNotBlank()行为不明确
**验证方法**: 
```java
// StrUtil.isNotBlank() 的行为：
StrUtil.isNotBlank(null)      // false
StrUtil.isNotBlank("")        // false  
StrUtil.isNotBlank("   ")     // false
StrUtil.isNotBlank("test")    // true
StrUtil.isNotBlank("<EMAIL>") // true
```

## 总结

要解决`if (StrUtil.isNotBlank(userInfo.getEmail()))`分支无法覆盖的问题，关键是：

1. **准备正确的测试数据**：确保有邮箱不为空和邮箱为空的用户
2. **使用专项测试方法**：针对性地测试邮箱分支覆盖
3. **验证测试结果**：通过日志和断言确认分支被正确覆盖
4. **检查覆盖率报告**：使用JaCoCo确认分支覆盖率达到100%

按照以上方案准备测试数据后，应该能够成功覆盖邮箱处理的两个分支。
