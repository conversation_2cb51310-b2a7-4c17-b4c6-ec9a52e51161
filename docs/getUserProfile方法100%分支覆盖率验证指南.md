# getUserProfile方法100%分支覆盖率验证指南

## 概述

本指南提供了验证`getUserProfile()`方法达到100%分支覆盖率的完整步骤和方法。

## 1. 补充的测试方法总览

### 1.1 新增测试方法列表

| 测试方法 | 目标分支 | 覆盖内容 |
|---------|---------|----------|
| `testGetUserProfileWechatBindStatusTernaryOperator` | B10 | 微信绑定状态三元运算符的两个分支 |
| `testGetUserProfileExceptionHandlingWithMock` | B6 | 异常处理分支（使用Mock） |
| `testGetUserProfileDataMaskingBranchesComplete` | B8, B9 | 手机号和邮箱脱敏的完整分支 |
| `testGetUserProfileBankInfoBranchesComplete` | B11 | 银行信息存在/不存在分支 |
| `testGetUserProfileRecommenderInfoBranchesComplete` | B7 | 推荐方信息存在/不存在分支 |
| `testGetUserProfileDataMaskingUtilException` | B6 | 数据脱敏工具异常分支 |
| `testGetUserProfileDataCombinationScenarios` | 综合 | 各种数据组合场景 |

### 1.2 分支覆盖映射表

| 分支ID | 分支条件 | 原有测试 | 新增测试 | 覆盖状态 |
|--------|----------|----------|----------|----------|
| B1 | `userId == null` | ✅ | - | 100% |
| B2 | `userInfo == null` | ✅ | - | 100% |
| B3 | `recommenderInfo` 查询 | ⚠️ | ✅ | 100% |
| B4 | `wechatUser` 查询 | ⚠️ | ✅ | 100% |
| B5 | `recommenderInfo != null` (银行查询) | ⚠️ | ✅ | 100% |
| B6 | `catch (Exception e)` | ❌ | ✅ | 100% |
| B7 | `recommenderInfo != null` (VO构建) | ⚠️ | ✅ | 100% |
| B8 | `StrUtil.isNotBlank(mobile)` | ⚠️ | ✅ | 100% |
| B9 | `StrUtil.isNotBlank(email)` | ⚠️ | ✅ | 100% |
| B10 | 微信绑定状态三元运算符 | ❌ | ✅ | 100% |
| B11 | `bankInfo != null` | ⚠️ | ✅ | 100% |

## 2. 运行测试验证

### 2.1 运行单个测试方法

```bash
# 运行微信绑定状态三元运算符测试
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfileWechatBindStatusTernaryOperator

# 运行异常处理测试
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfileExceptionHandlingWithMock

# 运行数据脱敏分支测试
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfileDataMaskingBranchesComplete

# 运行银行信息分支测试
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfileBankInfoBranchesComplete

# 运行数据组合场景测试
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfileDataCombinationScenarios
```

### 2.2 运行所有getUserProfile测试

```bash
# 运行整个测试类
mvn test -Dtest=TestUserInfoServiceGetUserProfile

# 运行测试并生成覆盖率报告
mvn clean test jacoco:report -Dtest=TestUserInfoServiceGetUserProfile
```

### 2.3 运行特定分支测试

```bash
# 运行所有新增的分支测试
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfile*Complete
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfile*Exception*
mvn test -Dtest=TestUserInfoServiceGetUserProfile#testGetUserProfile*Ternary*
```

## 3. 覆盖率验证步骤

### 3.1 使用JaCoCo验证

#### 步骤1：生成覆盖率报告
```bash
mvn clean test jacoco:report -Dtest=TestUserInfoServiceGetUserProfile
```

#### 步骤2：查看HTML报告
```bash
# 打开覆盖率报告
open target/site/jacoco/index.html

# 或者在Windows上
start target/site/jacoco/index.html
```

#### 步骤3：导航到目标方法
1. 点击 `tripai.recommend.system.service.impl`
2. 点击 `UserInfoServiceImpl`
3. 找到 `getUserProfile` 方法
4. 检查分支覆盖率

### 3.2 使用IDE验证

#### IntelliJ IDEA
1. 右键点击 `TestUserInfoServiceGetUserProfile` 类
2. 选择 "Run 'TestUserInfoServiceGetUserProfile' with Coverage"
3. 在Coverage窗口中查看 `UserInfoServiceImpl.getUserProfile` 方法
4. 确认分支覆盖率为100%

#### Eclipse (使用EclEmma)
1. 右键点击测试类
2. 选择 "Coverage As" > "JUnit Test"
3. 在Coverage视图中查看结果

### 3.3 命令行验证

```bash
# 检查特定方法的覆盖率
mvn jacoco:check -Drules.rule.element=METHOD \
    -Drules.rule.includes=*UserInfoServiceImpl.getUserProfile* \
    -Drules.rule.limits.limit.counter=BRANCH \
    -Drules.rule.limits.limit.minimum=1.00
```

## 4. 预期验证结果

### 4.1 JaCoCo报告预期结果

在JaCoCo HTML报告中，`getUserProfile`方法应该显示：
- **分支覆盖率**: 100% (绿色)
- **行覆盖率**: ≥95% (绿色)
- **方法覆盖率**: 100% (绿色)

### 4.2 具体分支验证清单

#### ✅ 应该显示为已覆盖（绿色）的分支：
- [ ] `if (userId == null)` - True和False分支
- [ ] `if (userInfo == null)` - True和False分支
- [ ] `if (recommenderInfo != null)` - True和False分支（银行查询）
- [ ] `if (StrUtil.isNotBlank(userInfo.getMobile()))` - True和False分支
- [ ] `if (StrUtil.isNotBlank(userInfo.getEmail()))` - True和False分支
- [ ] `if (wechatUser != null)` - True和False分支
- [ ] `Objects.equals(...) ? 1 : 0` - 三元运算符的两个分支
- [ ] `if (bankInfo != null)` - True和False分支
- [ ] `if (recommenderInfo != null)` - True和False分支（VO构建）
- [ ] `catch (Exception e)` - 异常处理分支

### 4.3 测试日志预期输出

运行测试时，应该看到以下关键日志：
```
✅ 三元运算符True分支覆盖成功
✅ 三元运算符False分支覆盖成功
✅ 推荐方信息查询异常处理验证通过
✅ 社交用户信息查询异常处理验证通过
✅ 银行信息查询异常处理验证通过
✅ 手机号不为空分支覆盖
✅ 手机号为空分支覆盖
✅ 邮箱不为空分支覆盖
✅ 邮箱为空分支覆盖
✅ 银行信息存在分支 (True) 覆盖成功
✅ 银行信息不存在分支 (False) 覆盖成功
🎉 所有数据脱敏分支都已覆盖！
```

## 5. 故障排除

### 5.1 常见问题及解决方案

#### 问题1：Mock不生效
**症状**: Mock的异常没有被触发
**解决方案**:
```java
// 确保使用了正确的Mock注解
@MockBean
private SocialUserMapper socialUserMapper;

// 确保Mock设置正确
when(socialUserMapper.selectOne(any(), eq(false)))
    .thenThrow(new RuntimeException("测试异常"));
```

#### 问题2：分支仍然未覆盖
**症状**: JaCoCo报告显示某些分支未覆盖
**解决方案**:
1. 检查测试数据是否准备正确
2. 确认测试方法实际执行了目标分支
3. 查看测试日志确认分支被触发

#### 问题3：测试数据不足
**症状**: 数据脱敏分支无法完全覆盖
**解决方案**:
```sql
-- 准备完整的测试数据
INSERT INTO user_info (id, mobile, email) VALUES
(1, '加密手机号', '<EMAIL>'),  -- 有手机号和邮箱
(2, '加密手机号', NULL),               -- 有手机号无邮箱
(3, NULL, '<EMAIL>'),         -- 无手机号有邮箱
(4, NULL, NULL);                       -- 无手机号和邮箱
```

### 5.2 调试技巧

#### 启用详细日志
```properties
# application-test.properties
logging.level.tripai.recommend.system.service.impl.UserInfoServiceImpl=DEBUG
logging.level.org.springframework.test=DEBUG
```

#### 使用断点调试
1. 在关键分支处设置断点
2. 以Debug模式运行测试
3. 逐步执行验证分支覆盖

## 6. 持续集成配置

### 6.1 Maven配置

```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.8</version>
    <executions>
        <execution>
            <id>check</id>
            <goals>
                <goal>check</goal>
            </goals>
            <configuration>
                <rules>
                    <rule>
                        <element>METHOD</element>
                        <includes>
                            <include>*UserInfoServiceImpl.getUserProfile*</include>
                        </includes>
                        <limits>
                            <limit>
                                <counter>BRANCH</counter>
                                <value>COVEREDRATIO</value>
                                <minimum>1.00</minimum>
                            </limit>
                        </limits>
                    </rule>
                </rules>
            </configuration>
        </execution>
    </executions>
</plugin>
```

### 6.2 CI/CD流水线

```yaml
# .github/workflows/test.yml
name: Test Coverage
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
      - name: Run getUserProfile tests
        run: mvn test -Dtest=TestUserInfoServiceGetUserProfile
      - name: Generate coverage report
        run: mvn jacoco:report
      - name: Check coverage threshold
        run: mvn jacoco:check
```

## 7. 总结

通过以上补充的测试方法，`getUserProfile()`方法的分支覆盖率应该能够达到100%。关键要点：

1. **完整的分支覆盖**: 所有11个主要分支都有对应的测试
2. **Mock策略**: 使用Mock来触发异常和特定条件
3. **测试数据**: 准备多样化的测试数据覆盖各种场景
4. **验证方法**: 使用JaCoCo和IDE工具验证覆盖率
5. **持续监控**: 集成到CI/CD流水线进行持续监控

按照本指南执行后，您应该能够实现并验证`getUserProfile()`方法的100%分支覆盖率。
