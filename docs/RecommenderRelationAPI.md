# 推荐方关系管理API文档

## 概述

推荐方关系管理系统提供了完整的推荐方与供应商关系的查询、管理功能，支持多种筛选条件、排序方式和分页选项。该模块基于`recommender_relation`表设计，核心功能是管理推荐方与各类供应商（酒店、导游等）的绑定关系。

## API接口

### 1. 查询推荐方酒店关系列表

**接口地址：** `GET /recommender/relation/hotel/list`

**请求头：**
- `Recommender-Token`: 推荐方用户token（必填）

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| relationStartDate | String | 否 | 建立关系开始时间（yyyy-MM-dd格式） | 2024-01-01 |
| relationEndDate | String | 否 | 建立关系结束时间（yyyy-MM-dd格式） | 2024-12-31 |
| keyword | String | 否 | 关键词搜索（供应商编码、酒店名称、供应商姓名） | 测试酒店 |
| sortType | Integer | 否 | 排序类型（见排序类型说明） | 1 |
| pageNum | Integer | 否 | 页码（默认1） | 1 |
| pageSize | Integer | 否 | 每页条数（支持50/100/150/200，默认50） | 100 |

**排序类型说明：**

| 排序代码 | 排序描述 | 说明 |
|----------|----------|------|
| 1 | 建立时间从新到旧 | 默认排序，按建立关系时间降序 |
| 2 | 上架服务数从高到低 | 按上架房型数量降序排序 |
| 3 | 成单数从高到低 | 按成单数量降序排序 |

**注意：** 当排序字段值相同时，按该条数据的订单号升序排列。

**响应示例：**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "relations": [
      {
        "relationId": 1,
        "recommenderId": 1,
        "hotelId": 1,
        "hotelName": "测试酒店",
        "hotelSupplierCode": "HOTEL001",
        "supplierMobile": "13800138000",
        "supplierNickName": "张三",
        "onlineRoomCount": 5,
        "relationCreateTime": "2024-01-01T10:00:00",
        "orderCount": 10,
        "relationStatus": 1,
        "relationStatusDesc": "正常",
        "hotelStatus": 4,
        "hotelStatusDesc": "已上线",
        "hotelAddress": "广州市天河区",
        "starRate": 4,
        "score": "4.5",
        "cityName": "广州市",
        "bizType": 1
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 50,
    "totalPages": 2,
    "hasNext": true,
    "hasPrevious": false,
    "sortType": "create_time_desc",
    "sortDescription": "建立关系时间降序",
    "statistics": {
      "totalRelationCount": 100,
      "totalHotelCount": 95,
      "totalOnlineRoomCount": 500,
      "totalOrderCount": 1000,
      "activeRelationCount": 90,
      "suspendRelationCount": 10,
      "avgRoomCountPerRelation": 5.0,
      "avgOrderCountPerRelation": 10.0
    }
  }
}
```

### 2. 查询推荐方酒店关系详情

**接口地址：** `GET /recommender/relation/hotel/detail/{hotelId}`

**请求头：**
- `Recommender-Token`: 推荐方用户token（必填）

**路径参数：**
- `hotelId`: 酒店ID（必填）

**响应示例：**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "relationId": 1,
    "recommenderId": 1,
    "hotelId": 1,
    "hotelName": "测试酒店",
    "hotelSupplierCode": "HOTEL001",
    "supplierMobile": "13800138000",
    "supplierNickName": "张三",
    "onlineRoomCount": 5,
    "relationCreateTime": "2024-01-01T10:00:00",
    "orderCount": 10,
    "relationStatus": 1,
    "relationStatusDesc": "正常",
    "hotelStatus": 4,
    "hotelStatusDesc": "已上线"
  }
}
```

### 3. 创建推荐方酒店关系

**接口地址：** `POST /recommender/relation/hotel/{hotelId}`

**请求头：**
- `Recommender-Token`: 推荐方用户token（必填）

**路径参数：**
- `hotelId`: 酒店ID（必填）

**响应示例：**

```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

### 4. 更新推荐方酒店关系状态

**接口地址：** `PUT /recommender/relation/hotel/status/{hotelId}`

**请求头：**
- `Recommender-Token`: 推荐方用户token（必填）

**路径参数：**
- `hotelId`: 酒店ID（必填）

**请求参数：**
- `relationStatus`: 关系状态（1=正常，2=管控/暂停）

**响应示例：**

```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

### 5. 删除推荐方酒店关系

**接口地址：** `DELETE /recommender/relation/hotel/{hotelId}`

**请求头：**
- `Recommender-Token`: 推荐方用户token（必填）

**路径参数：**
- `hotelId`: 酒店ID（必填）

**响应示例：**

```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

### 6. 检查推荐方酒店关系是否存在

**接口地址：** `GET /recommender/relation/hotel/exists/{hotelId}`

**请求头：**
- `Recommender-Token`: 推荐方用户token（必填）

**路径参数：**
- `hotelId`: 酒店ID（必填）

**响应示例：**

```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

### 7. 管理员接口

#### 7.1 根据推荐方ID查询酒店关系列表

**接口地址：** `GET /recommender/relation/hotel/list/{recommenderId}`

**路径参数：**
- `recommenderId`: 推荐方ID（必填）

#### 7.2 根据推荐方ID查询酒店关系详情

**接口地址：** `GET /recommender/relation/hotel/detail/{recommenderId}/{hotelId}`

**路径参数：**
- `recommenderId`: 推荐方ID（必填）
- `hotelId`: 酒店ID（必填）

#### 7.3 根据推荐方ID创建酒店关系

**接口地址：** `POST /recommender/relation/hotel/{recommenderId}/{hotelId}`

#### 7.4 根据推荐方ID更新关系状态

**接口地址：** `PUT /recommender/relation/hotel/status/{recommenderId}/{hotelId}`

#### 7.5 根据推荐方ID删除关系

**接口地址：** `DELETE /recommender/relation/hotel/{recommenderId}/{hotelId}`

## 业务规则

### 核心概念
- **推荐方关系管理**：基于`recommender_relation`表，管理推荐方与各类供应商的绑定关系
- **业务类型区分**：通过`biz_type`字段区分不同类型的供应商（1=酒店，2=导游）
- **关系状态管理**：支持正常、管控/暂停等状态的管理

### 筛选功能
- **建立关系时间筛选：** 支持开始时间和结束时间区间筛选
- **关键词搜索：** 支持供应商编码、酒店名称、供应商姓名模糊搜索

### 排序功能
- **默认排序：** 建立关系时间从新到旧（降序）
- **可选排序：** 签约酒店数量降序 / 上线房型数量降序 / 成单数量降序

### 分页选项
- **支持的分页大小：** 50条/页、100条/页、150条/页、200条/页
- **默认分页大小：** 50条/页

## 数据字段说明

### 推荐方酒店关系字段

| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| relationId | Long | 关系ID | recommender_relation.id |
| recommenderId | Long | 推荐方ID | recommender_relation.recommender_id |
| hotelId | Long | 酒店ID | recommender_relation.biz_id |
| hotelName | String | 酒店名称 | hotel_info.hotel_name |
| hotelSupplierCode | String | 酒店供应商编码 | hotel_info.hotel_supplier_code |
| supplierMobile | String | 供应商手机号 | user_info.mobile |
| supplierNickName | String | 供应商昵称 | user_info.nick_name |
| onlineRoomCount | Integer | 已上线房型数 | count(hotel_rooms) where room_status=3 |
| relationCreateTime | LocalDateTime | 建立关系时间 | recommender_relation.create_time |
| orderCount | Integer | 成单数量 | 统计已结算订单数量 |
| relationStatus | Integer | 关系状态 | recommender_relation.relation_status |
| bizType | Integer | 业务类型 | recommender_relation.biz_type |

### 统计信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalRelationCount | Long | 总关系数量 |
| totalHotelCount | Long | 总酒店数量 |
| totalOnlineRoomCount | Long | 总上线房型数量 |
| totalOrderCount | Long | 总成单数量 |
| activeRelationCount | Long | 正常关系数量 |
| suspendRelationCount | Long | 暂停关系数量 |
| avgRoomCountPerRelation | Double | 平均每个关系的房型数量 |
| avgOrderCountPerRelation | Double | 平均每个关系的成单数量 |

## 扩展性设计

### 架构设计
- **通用关系管理**：基于`recommender_relation`表的通用设计，支持多种业务类型
- **业务类型扩展**：通过`biz_type`字段支持未来添加导游、其他类型供应商
- **命名规范**：采用`RecommenderRelation`前缀，体现关系管理的核心概念

### 未来扩展
- **导游关系管理**：可复用相同的架构，添加导游相关的DTO、VO、Service
- **其他供应商类型**：通过扩展`biz_type`枚举支持新的供应商类型
- **通用关系操作**：可抽象出通用的关系管理基础类

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 查询条件不能为空 | 查询参数为null |
| 400 | 推荐方ID不能为空 | recommenderId为null |
| 400 | 用户ID不能为空 | userId为null |
| 400 | 酒店ID不能为空 | hotelId为null |
| 400 | 关系状态无效 | relationStatus无效 |
| 404 | 推荐方不存在 | 指定的推荐方不存在 |
| 404 | 推荐方酒店关系不存在 | 指定的关系不存在 |
| 409 | 推荐方酒店关系已存在 | 创建时关系已存在 |
| 401 | api.user.token.token-not-resolve-user | token解析失败 |
| 500 | 查询推荐方酒店关系列表失败 | 系统内部错误 |

## 使用示例

### 1. 查询全部酒店关系（第一页，50条）
```bash
GET /recommender/relation/hotel/list?pageNum=1&pageSize=50
```

### 2. 按建立关系时间筛选
```bash
GET /recommender/relation/hotel/list?relationStartDate=2024-01-01&relationEndDate=2024-12-31
```

### 3. 关键词搜索
```bash
GET /recommender/relation/hotel/list?keyword=测试酒店&pageSize=100
```

### 4. 按上线房型数量排序
```bash
GET /recommender/relation/hotel/list?sortType=room_count_desc&pageSize=200
```

### 5. 创建酒店关系
```bash
POST /recommender/relation/hotel/1
```

### 6. 更新关系状态为暂停
```bash
PUT /recommender/relation/hotel/status/1?relationStatus=2
```

### 7. 删除酒店关系
```bash
DELETE /recommender/relation/hotel/1
```

## 注意事项

1. **Token验证：** 所有用户接口都需要在请求头中携带有效的Recommender-Token
2. **分页限制：** 只支持指定的分页大小选项，无效值会被自动修正
3. **日期格式：** 日期参数必须使用yyyy-MM-dd格式
4. **跨库查询：** 系统会自动处理跨库查询，确保数据一致性
5. **数据权限：** 用户只能查看和管理自己名下的关系
6. **关系状态：** 1=正常，2=管控/暂停
7. **逻辑删除：** 删除操作为逻辑删除，不会物理删除数据
8. **业务类型：** 当前接口专门处理酒店关系（biz_type=1）
9. **扩展性：** 架构设计支持未来添加其他类型供应商的关系管理
