# RecommenderRelationController API文档

## 接口概述

推荐方关系管理接口，提供推荐方与酒店、导游供应商关系列表的查询功能。

**基础路径**: `/recommender/relation`

**认证方式**: 需要在请求头中携带Authorization token

---

## 1. 查询推荐方酒店关系列表

### 接口基本信息
- **接口路径**: `POST /recommender/relation/hotel/list`
- **接口描述**: 查询推荐方与酒店的关系列表，支持分页、筛选和排序
- **认证要求**: 需要Authorization token

### 请求参数

#### 请求头参数
```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // 用户认证token，必填
  "Content-Type": "application/json" // 请求内容类型
}
```

#### 请求体参数
```json
{
  "recommenderId": 123, // 推荐方ID（可选，系统会根据用户ID自动查询）
  "userId": 1, // 用户ID（系统自动设置，前端无需传递）
  "relationStartDate": "2025-01-01", // 建立关系开始时间，格式：yyyy-MM-dd
  "relationEndDate": "2025-01-30", // 建立关系结束时间，格式：yyyy-MM-dd
  "keyword": "白云宾馆", // 关键词搜索（供应商编码、酒店名称、供应商姓名）
  "sortType": 1, // 排序类型：1=建立时间从新到旧（默认），2=上架服务数从高到低，3=成单数从高到低
  "pageNum": 1, // 页码（从1开始），默认值：1
  "pageSize": 50 // 每页大小（支持50/100/150/200条/页），默认值：50
}
```

### 响应结果

#### 成功响应 (200)
```json
{
  "code": 200, // 响应状态码
  "message": "OK", // 响应消息
  "data": {
    "relations": [ // 推荐方酒店关系列表
      {
        "relationId": 1001, // 关系ID
        "recommenderId": 123, // 推荐方ID
        "hotelId": 2001, // 酒店ID（业务ID）
        "hotelName": "广州白云宾馆", // 酒店名称
        "hotelSupplierCode": "SUP001", // 酒店供应商编码
        "supplierMobile": "138****8000", // 供应商手机号（脱敏显示）
        "supplierNickName": "张经理", // 供应商昵称
        "onlineRoomCount": 15, // 已上线房型数量
        "relationCreateTime": "2025-01-15T10:00:00", // 建立关系时间
        "orderCount": 25, // 成单数量（建立关系以来佣金已结算的订单数量）
        "relationStatus": 1, // 关系状态：1=正常，2=管控/暂停
        "hotelStatus": 4, // 酒店状态：0=草稿，1=审核中，2=审核失败，3=未上线，4=已上线
        "hotelAddress": "广州市白云区机场路", // 酒店地址
        "starRate": 4, // 酒店星级
        "score": "4.5", // 酒店评分
        "cityName": "广州市", // 酒店所在城市
        "bizType": 1 // 业务类型（固定为1，表示酒店）
      }
    ],
    "total": 150, // 总记录数
    "pageNum": 1, // 当前页码
    "pageSize": 50, // 每页大小
    "totalPages": 3, // 总页数
    "hasNext": true, // 是否有下一页
    "hasPrevious": false, // 是否有上一页
    "sortType": 1, // 排序类型
    "sortDescription": "建立时间从新到旧", // 排序描述
    "statistics": { // 统计信息
      "totalRelationCount": 150, // 总关系数量
      "totalHotelCount": 145, // 总酒店数量
      "totalOnlineRoomCount": 2250, // 总上线房型数量
      "totalOrderCount": 3500, // 总成单数量
      "activeRelationCount": 140, // 正常关系数量
      "suspendRelationCount": 10, // 暂停关系数量
      "avgRoomCountPerRelation": 15.0, // 平均每个关系的房型数量
      "avgOrderCountPerRelation": 23.3 // 平均每个关系的成单数量
    }
  }
}
```

#### 错误响应
```json
{
  "code": 401, // 错误状态码
  "message": "api.user.token.token-not-resolve-user", // Token错误消息
  "data": null // 数据为空
}
```

### 错误码说明
- `400`: 业务逻辑错误（如推荐方不存在、查询条件不能为空等）
- `401`: Token解析失败或用户ID为空
- `500`: 服务器内部错误

---

## 2. 查询推荐方导游供应商列表

### 接口基本信息
- **接口路径**: `POST /recommender/relation/guide/list`
- **接口描述**: 查询推荐方与导游供应商的关系列表，支持分页、筛选和排序
- **认证要求**: 需要Authorization token

### 请求参数

#### 请求头参数
```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // 用户认证token，必填
  "Content-Type": "application/json" // 请求内容类型
}
```

#### 请求体参数
```json
{
  "recommenderId": 123, // 推荐方ID（可选，系统会根据用户ID自动查询）
  "userId": 1, // 用户ID（系统自动设置，前端无需传递）
  "productionLine": 2, // 产线筛选：null或"all"=全部，1=司机，2=导游，3=司兼导
  "relationStartDate": "2025-01-01", // 建立关系开始时间，格式：yyyy-MM-dd
  "relationEndDate": "2025-01-30", // 建立关系结束时间，格式：yyyy-MM-dd
  "keyword": "张导游", // 搜索关键字（导游姓名）
  "sortType": 1, // 排序类型：1=建立时间从新到旧（默认），2=上架服务数从高到低，3=成单数从高到低
  "pageNum": 1, // 页码（从1开始），默认值：1
  "pageSize": 50 // 每页大小（支持50/100/150/200条/页），默认值：50
}
```

### 响应结果

#### 成功响应 (200)
```json
{
  "code": 200, // 响应状态码
  "message": "OK", // 响应消息
  "data": {
    "suppliers": [ // 导游供应商列表
      {
        "relationId": 1001, // 关系ID
        "recommenderId": 123, // 推荐方ID
        "guideId": 3001, // 导游ID（业务ID）
        "guideName": "张导游", // 导游姓名（来自身份证）
        "phone": "138****8000", // 手机号（脱敏显示）
        "productionLine": 2, // 所属产线：1=司机，2=导游，3=司兼导
        "productionLineName": "导游", // 产线名称
        "serviceScore": 4.8, // 服务分
        "controlStatus": 0, // 管控状态：0=正常，1=月度管控中，2=永久管控中，3=手动管控中
        "controlStatusName": "正常", // 管控状态名称
        "serviceCount": 12, // 上架服务数
        "relationCreateTime": "2025-01-15T10:00:00", // 建立关系时间
        "orderCount": 35, // 成单数（建立关系以来已结算的订单数量）
        "relationStatus": 1, // 关系状态：1=正常，2=管控/暂停
        "userId": 1, // 用户ID
        "auditStatus": 1, // 审核状态：0=待审核，1=审核通过，2=审核未通过
        "auditStatusName": "审核通过", // 审核状态名称
        "serviceCitiesList": ["广州市", "深圳市"], // 服务城市列表
        "primaryServiceCity": "广州市", // 主要服务城市
        "languageList": ["中文", "英文"], // 服务语言列表
        "introduction": "专业导游，服务热情", // 个人简介
        "rejectionCount": 2, // 本月已拒单次数
        "rejectCountMonth": "2025-01-01", // 当前拒单计数对应月份
        "bizType": 2 // 业务类型（固定为2，表示导游）
      }
    ],
    "total": 80, // 总记录数
    "pageNum": 1, // 当前页码
    "pageSize": 50, // 每页大小
    "totalPages": 2, // 总页数
    "hasNext": true, // 是否有下一页
    "hasPrevious": false // 是否有上一页
  }
}
```

#### 错误响应
```json
{
  "code": 401, // 错误状态码
  "message": "api.user.token.token-not-resolve-user", // Token错误消息
  "data": null // 数据为空
}
```

### 错误码说明
- `400`: 业务逻辑错误（如推荐方不存在、查询条件不能为空等）
- `401`: Token解析失败或用户ID为空
- `500`: 服务器内部错误

---

## 调用示例

### 1. 查询酒店关系列表（默认参数）

```bash
curl -X POST "http://localhost:8080/recommender/relation/hotel/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. 按排序类型查询酒店关系

```bash
curl -X POST "http://localhost:8080/recommender/relation/hotel/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "sortType": 2,
    "pageNum": 1,
    "pageSize": 100
  }'
```

### 3. 关键词搜索酒店关系

```bash
curl -X POST "http://localhost:8080/recommender/relation/hotel/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "白云宾馆",
    "pageNum": 1,
    "pageSize": 50
  }'
```

### 4. 查询导游供应商列表（按产线筛选）

```bash
curl -X POST "http://localhost:8080/recommender/relation/guide/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "productionLine": 2,
    "sortType": 3,
    "pageNum": 1,
    "pageSize": 50
  }'
```

### 5. 按时间范围查询导游关系

```bash
curl -X POST "http://localhost:8080/recommender/relation/guide/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "relationStartDate": "2025-01-01",
    "relationEndDate": "2025-01-30",
    "keyword": "张导游",
    "pageNum": 1,
    "pageSize": 50
  }'
```

---

## 参数说明

### 酒店关系查询参数详解

#### sortType（排序类型）
- `1`：建立时间从新到旧（默认）
- `2`：上架服务数从高到低
- `3`：成单数从高到低

#### 分页参数
- `pageSize`：支持50/100/150/200条/页，默认50

### 导游关系查询参数详解

#### productionLine（产线筛选）
- `null` 或不传：查询全部产线
- `1`：仅查询司机
- `2`：仅查询导游
- `3`：仅查询司兼导

#### 时间范围查询
- 如果开始时间晚于结束时间，系统会自动交换

---

## 注意事项

1. **认证要求**: 所有接口都需要在请求头中携带有效的Authorization token
2. **权限控制**: 用户只能查询自己作为推荐方的关系数据
3. **数据脱敏**: 手机号等敏感信息已进行脱敏处理
4. **分页限制**: 建议合理设置每页大小，以保证查询性能
5. **时间格式**: 日期参数使用 `yyyy-MM-dd` 格式
6. **关键词搜索**: 支持模糊匹配，不区分大小写

## 枚举值说明

### 排序类型 (sortType)
- `1`: 建立时间从新到旧
- `2`: 上架服务数从高到低
- `3`: 成单数从高到低

### 产线类型 (productionLine)
- `1`: 司机
- `2`: 导游
- `3`: 司兼导

### 关系状态 (relationStatus)
- `1`: 正常
- `2`: 管控/暂停

### 酒店状态 (hotelStatus)
- `0`: 草稿
- `1`: 审核中
- `2`: 审核失败
- `3`: 未上线
- `4`: 已上线

### 管控状态 (controlStatus)
- `0`: 正常
- `1`: 月度管控中
- `2`: 永久管控中
- `3`: 手动管控中

### 审核状态 (auditStatus)
- `0`: 待审核
- `1`: 审核通过
- `2`: 审核未通过
