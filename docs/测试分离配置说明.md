# 测试分离配置说明

## 目录结构

```
src/test/java/tripai/recommend/system/
├── unit/                           # 单元测试目录
│   ├── README.md                   # 单元测试说明
│   └── UnitRecommenderOrderServiceTest.java  # 单元测试类
├── integration/                    # 集成测试目录
│   ├── README.md                   # 集成测试说明
│   └── IntegrationRecommenderOrderServiceTest.java  # 集成测试类
└── TestRecommenderOrderService.java  # 原有测试类（可保留或删除）
```

## 测试类型说明

### 1. 单元测试 (Unit Tests)
- **位置**: `src/test/java/tripai/recommend/system/unit/`
- **特点**: 使用Mock数据，专注于分支覆盖率
- **命名**: `Unit[ServiceName]Test.java`
- **用途**: 
  - 代码分支覆盖率测试
  - 快速反馈
  - CI/CD流水线中的快速验证

### 2. 集成测试 (Integration Tests)
- **位置**: `src/test/java/tripai/recommend/system/integration/`
- **特点**: 使用真实数据库，测试完整业务流程
- **命名**: `Integration[ServiceName]Test.java`
- **用途**:
  - 端到端业务流程验证
  - 数据一致性验证
  - 性能测试

## 运行方式

### 1. 运行单元测试（分支覆盖率测试）

```bash
# 运行所有单元测试
mvn test -Dtest="tripai.recommend.system.unit.**"

# 运行特定服务的单元测试
mvn test -Dtest="tripai.recommend.system.unit.UnitRecommenderOrderServiceTest"

# 生成覆盖率报告
mvn clean test jacoco:report -Dtest="tripai.recommend.system.unit.**"

# 查看覆盖率报告
open target/site/jacoco/index.html
```

### 2. 运行集成测试（真实数据库测试）

```bash
# 运行所有集成测试
mvn test -Dtest="tripai.recommend.system.integration.**" -Dspring.profiles.active=test

# 运行特定服务的集成测试
mvn test -Dtest="tripai.recommend.system.integration.IntegrationRecommenderOrderServiceTest" -Dspring.profiles.active=test

# 运行集成测试并生成报告
mvn clean test -Dtest="tripai.recommend.system.integration.**" -Dspring.profiles.active=test
```

### 3. 运行所有测试

```bash
# 先运行单元测试，再运行集成测试
mvn clean test -Dtest="tripai.recommend.system.unit.**"
mvn test -Dtest="tripai.recommend.system.integration.**" -Dspring.profiles.active=test

# 或者运行所有测试（不推荐，因为会混合Mock和真实数据）
mvn test
```

## Maven配置建议

在`pom.xml`中添加测试配置：

```xml
<build>
    <plugins>
        <!-- Surefire插件配置 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>3.0.0-M7</version>
            <configuration>
                <!-- 默认只运行单元测试 -->
                <includes>
                    <include>**/unit/**/*Test.java</include>
                </includes>
            </configuration>
            <executions>
                <!-- 单元测试执行配置 -->
                <execution>
                    <id>unit-tests</id>
                    <phase>test</phase>
                    <goals>
                        <goal>test</goal>
                    </goals>
                    <configuration>
                        <includes>
                            <include>**/unit/**/*Test.java</include>
                        </includes>
                        <excludes>
                            <exclude>**/integration/**/*Test.java</exclude>
                        </excludes>
                    </configuration>
                </execution>
                <!-- 集成测试执行配置 -->
                <execution>
                    <id>integration-tests</id>
                    <phase>integration-test</phase>
                    <goals>
                        <goal>test</goal>
                    </goals>
                    <configuration>
                        <includes>
                            <include>**/integration/**/*Test.java</include>
                        </includes>
                        <systemPropertyVariables>
                            <spring.profiles.active>test</spring.profiles.active>
                        </systemPropertyVariables>
                    </configuration>
                </execution>
            </executions>
        </plugin>

        <!-- JaCoCo覆盖率插件 -->
        <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>0.8.7</version>
            <executions>
                <execution>
                    <goals>
                        <goal>prepare-agent</goal>
                    </goals>
                </execution>
                <execution>
                    <id>report</id>
                    <phase>test</phase>
                    <goals>
                        <goal>report</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>
    </plugins>
</build>

<!-- 测试环境Profile -->
<profiles>
    <profile>
        <id>unit-test</id>
        <activation>
            <activeByDefault>true</activeByDefault>
        </activation>
        <build>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <configuration>
                        <includes>
                            <include>**/unit/**/*Test.java</include>
                        </includes>
                    </configuration>
                </plugin>
            </plugins>
        </build>
    </profile>
    
    <profile>
        <id>integration-test</id>
        <build>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <configuration>
                        <includes>
                            <include>**/integration/**/*Test.java</include>
                        </includes>
                        <systemPropertyVariables>
                            <spring.profiles.active>test</spring.profiles.active>
                        </systemPropertyVariables>
                    </configuration>
                </plugin>
            </plugins>
        </build>
    </profile>
</profiles>
```

## IDE配置

### IntelliJ IDEA配置

1. **创建运行配置**：
   - 单元测试配置：Test kind = "Pattern", Pattern = "tripai.recommend.system.unit.**"
   - 集成测试配置：Test kind = "Pattern", Pattern = "tripai.recommend.system.integration.**"

2. **设置测试环境**：
   - 集成测试运行配置中添加VM options: `-Dspring.profiles.active=test`

### Eclipse配置

1. **创建JUnit运行配置**
2. **设置测试包路径**
3. **配置环境变量**

## 最佳实践

### 1. 开发阶段
- 主要运行单元测试，快速验证代码逻辑
- 使用覆盖率报告确保分支覆盖完整

### 2. 提交前验证
- 运行所有单元测试
- 运行相关的集成测试

### 3. CI/CD流水线
- 第一阶段：运行单元测试，生成覆盖率报告
- 第二阶段：运行集成测试，验证业务流程
- 第三阶段：生成测试报告

### 4. 测试数据管理
- 单元测试：使用Mock数据，不依赖外部环境
- 集成测试：使用独立的测试数据库，测试后清理数据

## 注意事项

1. **数据库配置**：集成测试需要配置独立的测试数据库
2. **测试隔离**：确保测试之间不相互影响
3. **性能考虑**：单元测试应该快速执行，集成测试可以较慢
4. **维护成本**：平衡测试覆盖率和维护成本

## 迁移建议

1. **保留原有测试**：暂时保留`TestRecommenderOrderService.java`作为参考
2. **逐步迁移**：将现有测试方法分别迁移到单元测试和集成测试中
3. **验证功能**：确保迁移后的测试能够正常运行
4. **清理代码**：迁移完成后删除原有测试类

通过这种分离方式，您可以：
- 快速运行单元测试进行开发验证
- 独立运行集成测试进行业务验证
- 生成准确的代码覆盖率报告
- 提高测试的可维护性和可读性
