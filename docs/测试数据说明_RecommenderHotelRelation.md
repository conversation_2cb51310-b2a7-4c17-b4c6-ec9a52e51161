# RecommenderHotelRelation 测试数据说明

## 概述

本文档说明了为 `testGetRecommenderHotelRelationListSuccess` 测试方法生成的测试数据结构和关系。

## 测试方法执行流程

1. **测试方法调用**：`queryDto.setUserId(1L)`
2. **Service层处理**：通过 `userId=1` 查询 `recommender_info` 表获取 `recommenderId=1`
3. **数据库查询**：执行复杂的多表联查获取酒店关系列表

## 涉及的数据库表

### 核心业务表
- `recommender_user_info` - 用户信息表
- `recommender_info` - 推荐方信息表  
- `recommender_relation` - 推荐方关系表
- `hotel_info` - 酒店信息表

### 统计相关表
- `hotel_rooms` - 房型表（统计上线房型数）
- `manager_order` - 管理订单表（统计成单数）
- `pay_order` - 支付订单表（订单状态验证）
- `supplier_operation_order_log` - 供应商操作日志表（订单确认状态）

## 测试数据结构

### 用户和推荐方数据
```
userId=1 → recommenderId=1 (张三，个人推荐方)
userId=2 → recommenderId=2 (测试企业有限公司，企业推荐方)
```

### 酒店供应商数据
```
SUP001 (酒店供应商1) → 管理酒店: 1,2,5
SUP002 (酒店供应商2) → 管理酒店: 3,4
```

### 推荐方关系数据 (recommenderId=1)

| 酒店ID | 酒店名称 | 上线房型数 | 成单数 | 关系状态 | 建立时间 |
|--------|----------|------------|--------|----------|----------|
| 1 | 北京王府井大酒店 | 6 | 5 | 正常 | 2025-01-25 |
| 2 | 上海外滩精品酒店 | 4 | 3 | 正常 | 2025-01-26 |
| 3 | 广州珠江新城商务酒店 | 3 | 2 | 正常 | 2025-01-27 |
| 4 | 深圳南山科技园酒店 | 2 | 1 | 管控 | 2025-01-28 |
| 5 | 杭州西湖度假村 | 5 | 4 | 正常 | 2025-01-29 |

### 统计信息汇总
- **总关系数**：5个
- **总酒店数**：5个
- **总上线房型数**：20个
- **总成单数**：15个
- **正常关系数**：4个
- **管控关系数**：1个

## 排序功能验证

### 1. 按建立时间排序（sortType=1，默认）
预期顺序：杭州西湖度假村 → 深圳南山科技园酒店 → 广州珠江新城商务酒店 → 上海外滩精品酒店 → 北京王府井大酒店

### 2. 按上线房型数排序（sortType=2）
预期顺序：北京王府井大酒店(6) → 杭州西湖度假村(5) → 上海外滩精品酒店(4) → 广州珠江新城商务酒店(3) → 深圳南山科技园酒店(2)

### 3. 按成单数排序（sortType=3）
预期顺序：北京王府井大酒店(5) → 杭州西湖度假村(4) → 上海外滩精品酒店(3) → 广州珠江新城商务酒店(2) → 深圳南山科技园酒店(1)

## 分页功能验证

- **默认分页**：pageNum=1, pageSize=50
- **总记录数**：5条
- **总页数**：1页
- **是否有下一页**：false
- **是否有上一页**：false

## 关键词搜索验证

测试数据支持以下关键词搜索：
- **酒店名称**：如"王府井"、"外滩"、"珠江"等
- **供应商编码**：如"SUP001"、"SUP002"
- **供应商昵称**：如"酒店供应商1"、"酒店供应商2"

## 数据执行步骤

1. **执行SQL文件**：
   ```bash
   mysql -u username -p database_name < sql/test_data_for_recommender_hotel_relation.sql
   ```

2. **验证数据完整性**：
   ```sql
   -- 验证用户和推荐方关系
   SELECT ri.id as recommender_id, ri.name, ui.nick_name 
   FROM recommender_info ri 
   JOIN recommender_user_info ui ON ri.user_id = ui.id 
   WHERE ui.id = 1;
   
   -- 验证关系数据
   SELECT COUNT(*) as relation_count 
   FROM recommender_relation 
   WHERE recommender_id = 1 AND biz_type = 1 AND is_del = 0;
   ```

3. **运行测试**：
   ```bash
   mvn test -Dtest=TestRecommenderRelationService#testGetRecommenderHotelRelationListSuccess
   ```

## 预期测试结果

测试方法执行后应该返回：
- **响应状态**：200 (成功)
- **数据条数**：5条酒店关系记录
- **统计信息**：包含正确的汇总数据
- **排序结果**：按建立时间降序排列
- **分页信息**：正确的分页计算结果

## 注意事项

1. **数据依赖**：确保所有相关表都已创建且结构正确
2. **时间格式**：注意数据库时间字段的格式要求
3. **外键关系**：虽然不使用数据库外键约束，但要保证数据的引用完整性
4. **测试隔离**：建议在测试环境中执行，避免影响生产数据
5. **数据清理**：测试完成后可选择清理测试数据

## 扩展测试场景

基于这些测试数据，还可以验证：
- 不同排序类型的功能
- 关键词搜索的准确性
- 日期筛选功能
- 分页边界条件
- 异常参数处理
- 性能测试等
