# getUserProfile方法100%分支覆盖率实现指南

## 1. 补充的测试方法总览

为了达到`getUserProfile()`方法的100%分支覆盖率，我已经补充了以下测试方法：

### 1.1 核心分支覆盖测试
1. **testGetUserProfileWithNoRecommenderInfo** - 用户存在但推荐方信息不存在
2. **testGetUserProfileWithNoWechatBinding** - 用户未绑定微信
3. **testGetUserProfileWithWechatBinding** - 用户已绑定微信
4. **testGetUserProfileWithNoBankInfo** - 推荐方存在但无银行信息
5. **testGetUserProfileWithBankInfo** - 推荐方存在且有银行信息
6. **testGetUserProfileWithEmptyMobile** - 用户手机号为空
7. **testGetUserProfileWithEmptyEmail** - 用户邮箱为空

### 1.2 边界条件和异常测试
8. **testGetUserProfileBoundaryValues** - 边界值测试
9. **testGetUserProfileWithDatabaseException** - 数据库查询异常模拟
10. **testGetUserProfileWithMaskingException** - 数据脱敏异常模拟

### 1.3 质量保证测试
11. **testGetUserProfileDataIntegrity** - 数据完整性验证
12. **testGetUserProfileCombinationScenarios** - 组合场景测试
13. **testGetUserProfileConcurrentAccess** - 并发访问测试
14. **testGetUserProfileMemoryLeak** - 内存泄漏检测

## 2. 分支覆盖率映射表

| 分支编号 | 分支条件 | 测试方法 | 覆盖状态 |
|---------|---------|---------|---------|
| 1 | `userId == null` | testGetUserProfileWithNullUserId | ✅ |
| 2 | `userInfo == null` | testGetUserProfileWithInvalidUserId | ✅ |
| 3 | `recommenderInfo != null` | testGetUserProfileWithNoRecommenderInfo | ✅ |
| 4 | `wechatUser != null` | testGetUserProfileWithNoWechatBinding<br>testGetUserProfileWithWechatBinding | ✅ |
| 5 | `recommenderInfo != null` (银行查询条件) | testGetUserProfileWithNoBankInfo | ✅ |
| 6 | `bankInfo != null` | testGetUserProfileWithBankInfo | ✅ |
| 7 | `StrUtil.isNotBlank(userInfo.getMobile())` | testGetUserProfileWithEmptyMobile | ✅ |
| 8 | `StrUtil.isNotBlank(userInfo.getEmail())` | testGetUserProfileWithEmptyEmail | ✅ |
| 9 | `wechatUser != null ? 1 : 0` | testGetUserProfileWithNoWechatBinding<br>testGetUserProfileWithWechatBinding | ✅ |
| 10 | `catch (Exception e)` | testGetUserProfileWithDatabaseException | ✅ |

## 3. 测试数据准备策略

### 3.1 数据库测试数据
为了确保测试的有效性，需要在测试数据库中准备以下数据：

```sql
-- 用户基本信息
INSERT INTO user_info (id, mobile, email, ...) VALUES
(1, '加密的手机号', '邮箱@example.com', ...),     -- 完整信息用户
(993, '加密的手机号', NULL, ...),                -- 邮箱为空的用户
(994, NULL, '邮箱@example.com', ...),            -- 手机号为空的用户
(995, '加密的手机号', '邮箱@example.com', ...),   -- 有银行信息的用户
(996, '加密的手机号', '邮箱@example.com', ...),   -- 无银行信息的用户
(997, '加密的手机号', '邮箱@example.com', ...),   -- 已绑定微信的用户
(998, '加密的手机号', '邮箱@example.com', ...),   -- 未绑定微信的用户
(999, '加密的手机号', '邮箱@example.com', ...);   -- 无推荐方信息的用户

-- 推荐方信息
INSERT INTO recommender_info (user_id, name, type, ...) VALUES
(1, '张三', 1, ...),      -- 用户1的推荐方信息
(995, '李四', 1, ...),    -- 用户995的推荐方信息
(996, '王五', 1, ...),    -- 用户996的推荐方信息
(997, '赵六', 1, ...),    -- 用户997的推荐方信息
(998, '钱七', 1, ...);    -- 用户998的推荐方信息

-- 银行信息
INSERT INTO recommender_bank (recommender_id, bank_card_no, bank_name, ...) VALUES
(1, '6222****1234', '工商银行', ...),    -- 用户1的银行信息
(2, '6222****5678', '建设银行', ...);    -- 用户995的银行信息

-- 社交用户信息（微信绑定）
INSERT INTO social_user (uid, source, ...) VALUES
(997, 'WECHAT_OPEN', ...);    -- 用户997绑定了微信
```

### 3.2 Mock数据策略
对于异常测试，可以使用以下Mock策略：

```java
// 1. Mock数据库异常
@MockBean
private UserInfoMapper userInfoMapper;

@Test
public void testGetUserProfileWithDatabaseException() {
    when(userInfoMapper.selectById(any())).thenThrow(new RuntimeException("Database connection failed"));
    // 执行测试...
}

// 2. Mock数据脱敏异常
@MockBean
private DataMaskingUtil dataMaskingUtil;

@Test
public void testGetUserProfileWithMaskingException() {
    when(DataMaskingUtil.maskMobile(any())).thenThrow(new RuntimeException("Masking failed"));
    // 执行测试...
}
```

## 4. 覆盖率验证步骤

### 4.1 使用JaCoCo生成覆盖率报告

#### 步骤1：添加JaCoCo插件
```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.8</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

#### 步骤2：运行测试并生成报告
```bash
# 运行所有getUserProfile相关测试
mvn clean test -Dtest=TestUserInfoService#testGetUserProfile* jacoco:report

# 或者运行整个测试类
mvn clean test -Dtest=TestUserInfoService jacoco:report
```

#### 步骤3：查看覆盖率报告
```bash
# 打开HTML报告
open target/site/jacoco/index.html

# 导航到UserInfoServiceImpl类
# 查看getUserProfile方法的分支覆盖率
```

### 4.2 IDE中查看覆盖率

#### IntelliJ IDEA
1. 右键点击测试类或方法
2. 选择"Run 'TestUserInfoService' with Coverage"
3. 在Coverage窗口中查看分支覆盖率
4. 点击方法名查看详细的分支覆盖情况

#### Eclipse
1. 安装EclEmma插件
2. 右键点击测试类
3. 选择"Coverage As" > "JUnit Test"
4. 在Coverage视图中查看结果

### 4.3 命令行验证
```bash
# 生成覆盖率报告并检查特定方法
mvn clean test jacoco:report
grep -A 10 "getUserProfile" target/site/jacoco/tripai.recommend.system.service.impl/UserInfoServiceImpl.html
```

## 5. 预期覆盖率结果

### 5.1 目标指标
- **分支覆盖率**: 100%
- **行覆盖率**: ≥95%
- **方法覆盖率**: 100%

### 5.2 关键分支验证清单
- [ ] userId为null的分支
- [ ] userInfo为null的分支
- [ ] recommenderInfo为null和非null的分支
- [ ] wechatUser为null和非null的分支
- [ ] bankInfo为null和非null的分支
- [ ] 手机号为空和非空的分支
- [ ] 邮箱为空和非空的分支
- [ ] 异常处理分支
- [ ] 三元运算符的两个分支

## 6. 常见问题和解决方案

### 6.1 覆盖率不达100%的常见原因

#### 问题1：测试数据不足
**症状**: 某些分支显示未覆盖
**解决方案**: 检查测试数据，确保覆盖所有数据组合

#### 问题2：异常分支未覆盖
**症状**: catch块显示未覆盖
**解决方案**: 使用Mock模拟异常情况

#### 问题3：三元运算符分支未完全覆盖
**症状**: 条件表达式的某个分支未覆盖
**解决方案**: 确保测试用例覆盖true和false两种情况

### 6.2 Mock使用建议

#### 推荐的Mock框架
```xml
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    <scope>test</scope>
</dependency>
```

#### Mock最佳实践
```java
// 1. 使用@MockBean注解Mock Spring Bean
@MockBean
private UserInfoMapper userInfoMapper;

// 2. 在测试方法中设置Mock行为
@Test
public void testException() {
    when(userInfoMapper.selectById(any())).thenThrow(new RuntimeException());
    // 执行测试
}

// 3. 验证Mock调用
verify(userInfoMapper, times(1)).selectById(any());
```

## 7. 持续集成配置

### 7.1 CI/CD流水线配置
```yaml
# .github/workflows/test.yml
name: Test Coverage
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
      - name: Run tests with coverage
        run: mvn clean test jacoco:report
      - name: Check coverage
        run: |
          # 检查getUserProfile方法的覆盖率
          # 如果低于100%则失败
```

### 7.2 覆盖率阈值配置
```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <executions>
        <execution>
            <id>check</id>
            <goals>
                <goal>check</goal>
            </goals>
            <configuration>
                <rules>
                    <rule>
                        <element>METHOD</element>
                        <includes>
                            <include>*UserInfoServiceImpl.getUserProfile*</include>
                        </includes>
                        <limits>
                            <limit>
                                <counter>BRANCH</counter>
                                <value>COVEREDRATIO</value>
                                <minimum>1.00</minimum>
                            </limit>
                        </limits>
                    </rule>
                </rules>
            </configuration>
        </execution>
    </executions>
</plugin>
```

## 8. 总结

通过以上补充的14个测试方法，`getUserProfile()`方法的分支覆盖率应该能够达到100%。这些测试方法覆盖了：

1. **所有条件分支**: if/else、三元运算符
2. **异常处理分支**: try/catch块
3. **边界条件**: null值、空值、边界值
4. **业务逻辑分支**: 不同数据组合的处理路径
5. **质量保证**: 并发、内存、数据完整性

建议在实际项目中：
1. 先运行现有测试，查看当前覆盖率
2. 逐步添加缺失的测试方法
3. 使用JaCoCo验证覆盖率提升
4. 将覆盖率检查集成到CI/CD流水线
5. 定期审查和维护测试用例
