# BankCardVerifyController 频率限制升级说明

## 修改概述

对`BankCardVerifyController.java`中的`verifyBankCard`方法进行了全面升级，添加了用户身份验证和双重频率限制机制，与`IdentityVerificationController`保持一致的实现风格。

## 修改详情

### 1. 导入依赖添加

#### 新增import语句
```java
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;
```

### 2. 常量定义添加

#### 频率限制相关常量
```java
/**
 * 银行卡验证redis键前缀 - 分钟级限制
 */
private final String BANK_VERIFY_MINUTE_REDISKEY = "bank_verify:minute:";

/**
 * 银行卡验证redis键前缀 - 天级限制
 */
private final String BANK_VERIFY_DAY_REDISKEY = "bank_verify:day:";

/**
 * 同一用户1分钟内最多请求15次
 */
private final int MAX_MINUTE_LIMIT = 15;

/**
 * 同一用户1天内最多请求30次
 */
private final int MAX_DAY_LIMIT = 30;

/**
 * 分钟级redis过期时间（秒）
 */
private final int MINUTE_EXPIRE_TIME = 60;

/**
 * 天级redis过期时间（秒）
 */
private final int DAY_EXPIRE_TIME = 24 * 60 * 60;
```

### 3. 依赖注入添加

#### 新增依赖
```java
@Resource
private StringRedisTemplate stringRedisTemplate;

@Resource
private HttpServletRequest request;
```

### 4. 方法签名修改

#### 修改前
```java
@PostMapping("/verify")
public ResponseResult<BankCardVerifyResultVo> verifyBankCard(@RequestBody @Valid BankCardVerifyDto verifyDto)
```

#### 修改后
```java
@PostMapping("/verify")
public ResponseResult<BankCardVerifyResultVo> verifyBankCard(@RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token,
                                                           @RequestBody @Valid BankCardVerifyDto verifyDto)
```

### 5. Redis键设计

#### 分钟级限制键格式
```
bank_verify:minute:{userId}
```
- 示例：`bank_verify:minute:12345`
- 过期时间：60秒

#### 天级限制键格式
```
bank_verify:day:{userId}:{date}
```
- 示例：`bank_verify:day:12345:2025-01-30`
- 过期时间：24小时（86400秒）

### 6. 核心业务逻辑流程

#### 完整的验证流程
```java
1. Token验证 → 获取用户ID
2. 分钟级频率检查 → Redis计数器验证
3. 天级频率检查 → Redis计数器验证
4. 频率限制通过 → 执行银行卡验证
5. 返回验证结果
```

#### 用户身份验证
```java
// 从token中获取用户信息
UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
if (userInfoVo == null) {
    return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
}
Long userId = userInfoVo.getId();
if (userId == null) {
    return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
}
```

#### 双重频率限制检查
```java
// 1. 分钟级限制检查
minuteCount = stringRedisTemplate.opsForValue().increment(minuteRedisKey, 1);

// 2. 天级限制检查
dayCount = stringRedisTemplate.opsForValue().increment(dayRedisKey, 1);

// 3. 限制验证
if (minuteCount > MAX_MINUTE_LIMIT) {
    return ResponseResult.fail("分钟级限制超限");
}
if (dayCount > MAX_DAY_LIMIT) {
    return ResponseResult.fail("天级限制超限");
}
```

## 技术特性

### 1. 与IdentityVerificationController保持一致

| 特性 | IdentityVerificationController | BankCardVerifyController | 一致性 |
|------|-------------------------------|-------------------------|--------|
| 频率限制规则 | 15次/分钟 + 30次/天 | 15次/分钟 + 30次/天 | ✅ 完全一致 |
| Redis键设计 | 分层命名规范 | 分层命名规范 | ✅ 完全一致 |
| 异常处理 | Redis异常时允许继续 | Redis异常时允许继续 | ✅ 完全一致 |
| 错误提示 | 明确区分限制类型 | 明确区分限制类型 | ✅ 完全一致 |
| Token验证 | JWT解析获取用户ID | JWT解析获取用户ID | ✅ 完全一致 |

### 2. 错误提示优化

#### 分钟级超限提示
```
请求过于频繁，1分钟内最多请求15次，请XX秒后再试
```

#### 天级超限提示
```
今日请求次数已达上限（30次），请X小时X分钟后再试
```

#### Token验证失败提示
```
401: api.user.token.token-not-resolve-user
```

### 3. 异常处理机制

#### Redis操作异常处理
```java
try {
    // Redis操作
} catch (Exception e) {
    // Redis操作异常，记录日志但允许请求继续
    log.info("Redis操作异常，无法进行防刷限制，用户ID: {}", userId);
    minuteCount = 1L;
    dayCount = 1L;
}
```

#### 银行卡验证异常处理
```java
try {
    BankCardVerifyResultVo result = tencentBankCardVerifyService.verifyBankCard(verifyDto);
    return ResponseResult.ok(result);
} catch (Exception e) {
    log.error("银行卡验证失败，用户ID: {}", userId, e);
    return ResponseResult.fail("银行卡验证失败: " + e.getMessage());
}
```

## 业务影响

### 1. 安全性提升

| 安全维度 | 升级前 | 升级后 | 提升效果 |
|----------|--------|--------|----------|
| 身份验证 | 无 | JWT Token验证 | 防止未授权访问 |
| 频率控制 | 无 | 双重限制机制 | 防止API滥用 |
| 用户追踪 | 无 | 基于用户ID限制 | 精确控制单用户行为 |

### 2. 成本控制

- **API调用成本**：通过天级限制控制整体调用量
- **系统资源**：防止恶意请求消耗系统资源
- **服务稳定性**：避免单用户过度使用影响其他用户

### 3. 用户体验

- **合理限制**：15次/分钟满足正常使用需求
- **清晰提示**：明确告知限制原因和等待时间
- **降级处理**：Redis异常时不影响正常业务

## API接口变更

### 1. 请求头要求

#### 新增必填请求头
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. 错误响应新增

#### 401 Token验证失败
```json
{
  "code": 401,
  "message": "api.user.token.token-not-resolve-user",
  "data": null
}
```

#### 400 频率限制超限
```json
{
  "code": 400,
  "message": "请求过于频繁，1分钟内最多请求15次，请XX秒后再试",
  "data": null
}
```

## 测试建议

### 1. 功能测试

#### Token验证测试
```bash
# 测试无Token请求
curl -X POST "http://localhost:8080/api/bank-card/verify" \
  -H "Content-Type: application/json" \
  -d '{"name":"张三","idCard":"123456","bankCard":"6222021234567890123"}'

# 测试无效Token请求
curl -X POST "http://localhost:8080/api/bank-card/verify" \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json" \
  -d '{"name":"张三","idCard":"123456","bankCard":"6222021234567890123"}'
```

#### 频率限制测试
```bash
# 测试分钟级限制（16次请求）
for i in {1..16}; do
  curl -X POST "http://localhost:8080/api/bank-card/verify" \
    -H "Authorization: Bearer {valid_token}" \
    -H "Content-Type: application/json" \
    -d '{"name":"张三","idCard":"123456","bankCard":"6222021234567890123"}'
  echo "Request $i completed"
done
```

### 2. Redis键验证

#### 查看Redis中的键
```bash
# 查看分钟级限制键
redis-cli keys "bank_verify:minute:*"

# 查看天级限制键
redis-cli keys "bank_verify:day:*"

# 查看键的过期时间
redis-cli TTL "bank_verify:minute:12345"
redis-cli TTL "bank_verify:day:12345:2025-01-30"
```

### 3. 异常场景测试

- Redis服务不可用时的降级处理
- Token解析异常的处理
- 银行卡验证服务异常的处理
- 并发请求的计数器准确性

## 监控建议

### 1. 关键指标

- 分钟级限制触发次数
- 天级限制触发次数
- Token验证失败次数
- Redis操作异常次数
- 银行卡验证成功率

### 2. 告警设置

- Redis连接异常告警
- 频率限制触发率过高告警
- Token验证失败率过高告警
- 银行卡验证失败率异常告警

## 部署注意事项

### 1. 前端适配

前端需要在请求头中添加Authorization token：
```javascript
// 前端请求示例
fetch('/api/bank-card/verify', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + userToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(verifyData)
});
```

### 2. 向后兼容性

⚠️ **重要提醒**：此次修改不向后兼容，所有调用此接口的客户端都需要更新以支持Token验证。

### 3. 配置验证

- 确保Redis服务正常运行
- 验证JWT工具类正常工作
- 测试HttpHeadConstant.RECOMMENDER_TOKEN常量正确

## 总结

本次升级成功实现了：

1. **完整的身份验证**：基于JWT Token的用户身份验证
2. **双重频率限制**：分钟级和天级的双重保护机制
3. **一致的实现风格**：与IdentityVerificationController保持完全一致
4. **健壮的异常处理**：完善的降级和容错机制
5. **友好的用户体验**：清晰的错误提示和等待时间显示

通过这次升级，银行卡验证接口具备了与身份证OCR接口相同的安全性和稳定性保障。
