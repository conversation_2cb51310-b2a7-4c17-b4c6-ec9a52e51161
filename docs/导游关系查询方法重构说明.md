# 导游关系查询方法重构说明

## 重构概述

本次重构将`getTourGuideSupplierList`方法重构为`getRecommenderGuideRelationList`方法，使其与`getRecommenderHotelRelationList`方法保持完全一致的结构、命名风格和处理逻辑，提高代码的一致性和可维护性。

## 重构前的问题

### 1. 命名不一致
- 方法名：`getTourGuideSupplierList` vs `getRecommenderHotelRelationList`
- 参数验证方法：命名风格不统一
- 日志信息：描述不一致

### 2. 结构混乱
- 参数验证逻辑不够统一
- 错误处理方式略有差异
- 步骤编号和注释不一致

### 3. 代码重复
- 相似的逻辑有微小差异
- 维护成本高，容易出现不一致

## 重构内容

### 1. 方法名称统一

#### 1.1 Service接口
**重构前：**
```java
ResponseResult<RecommenderGuideRelationListVo> getTourGuideSupplierList(RecommenderGuideRelationQueryDto queryDto);
```

**重构后：**
```java
ResponseResult<RecommenderGuideRelationListVo> getRecommenderGuideRelationList(RecommenderGuideRelationQueryDto queryDto);
```

#### 1.2 Service实现类
**重构前：**
```java
@Override
public ResponseResult<RecommenderGuideRelationListVo> getTourGuideSupplierList(RecommenderGuideRelationQueryDto queryDto) {
    // 实现逻辑
}
```

**重构后：**
```java
@Override
public ResponseResult<RecommenderGuideRelationListVo> getRecommenderGuideRelationList(RecommenderGuideRelationQueryDto queryDto) {
    // 实现逻辑
}
```

### 2. 参数验证逻辑统一

#### 2.1 参数验证步骤
**重构前：**
```java
// 1. 参数验证
if (queryDto == null) {
    log.error("查询推荐方导游关系列表失败，查询条件为空");
    return ResponseResult.fail("查询条件不能为空");
}
if (queryDto.getUserId() == null) {
    log.error("查询推荐方酒店关系列表失败，用户ID为空");  // 错误的日志信息
    return ResponseResult.fail("用户ID不能为空");
}
```

**重构后：**
```java
// 1. 参数验证
if (queryDto == null) {
    log.error("查询推荐方导游关系列表失败，查询条件为空");
    return ResponseResult.fail("查询条件不能为空");
}

if (queryDto.getUserId() == null) {
    log.error("查询推荐方导游关系列表失败，用户ID为空");
    return ResponseResult.fail("用户ID不能为空");
}
```

#### 2.2 推荐方验证逻辑
**重构前：**
```java
// 2. 验证推荐方是否存在
LambdaQueryWrapper<RecommenderInfo> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(RecommenderInfo::getUserId, queryDto.getUserId())
        .eq(RecommenderInfo::getIsDel, 0);
RecommenderInfo recommenderInfo = recommenderMapper.selectOne(queryWrapper, false);

if (recommenderInfo == null) {
    log.error("查询推荐方导游关系列表失败，推荐方不存在，推荐方ID：{}", queryDto.getRecommenderId());
    return ResponseResult.fail("推荐方不存在");
}

if (recommenderInfo.getId() == null) {
    log.error("查询推荐方导游关系列表失败，推荐方ID为空");
    return ResponseResult.fail("推荐方ID不能为空");
}
queryDto.setRecommenderId(recommenderInfo.getId());
```

**重构后：**
```java
// 2. 验证推荐方是否存在
LambdaQueryWrapper<RecommenderInfo> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(RecommenderInfo::getUserId, queryDto.getUserId())
        .eq(RecommenderInfo::getIsDel, 0);
RecommenderInfo recommenderInfo = recommenderMapper.selectOne(queryWrapper, false);
if (recommenderInfo == null) {
    log.error("查询推荐方导游关系列表失败，推荐方不存在，用户ID：{}", queryDto.getUserId());
    return ResponseResult.fail("推荐方不存在");
}
queryDto.setRecommenderId(recommenderInfo.getId());
```

### 3. 数据处理流程统一

#### 3.1 处理步骤编号
**重构前：**
```java
// 3. 参数校验和处理
// 4. 查询关系列表
// 5. 处理数据脱敏和状态名称设置
// 6. 查询总数
// 7. 查询统计信息
// 8. 构建响应结果
```

**重构后：**
```java
// 3. 参数校验和处理
// 4. 查询关系列表
// 5. 查询总数
// 6. 查询统计信息
// 7. 构建响应结果
```

#### 3.2 数据处理逻辑
**重构前：**
```java
// 4. 查询关系列表
List<RecommenderGuideRelationVo> relationList = recommenderRelationMapper.selectRecommenderGuideRelationList(queryDto);

// 5. 处理数据脱敏和状态名称设置
processGuideRelationList(relationList);
```

**重构后：**
```java
// 4. 查询关系列表
List<RecommenderGuideRelationVo> relationList = recommenderRelationMapper.selectRecommenderGuideRelationList(queryDto);
if (CollUtil.isNotEmpty(relationList)) {
    processGuideRelationList(relationList);
}
```

### 4. 测试方法名称统一

#### 4.1 测试方法重命名
| 重构前 | 重构后 |
|--------|--------|
| `testGetTourGuideSupplierListSuccess` | `testGetRecommenderGuideRelationListSuccess` |
| `testGetGuideSupplierListWithProductionLineFilter` | `testGetRecommenderGuideRelationListWithProductionLineFilter` |
| `testGetGuideSupplierListWithControlStatusFilter` | `testGetRecommenderGuideRelationListWithControlStatusFilter` |
| `testGetGuideSupplierListWithKeyword` | `testGetRecommenderGuideRelationListWithKeyword` |
| `testGetGuideSupplierListWithDateFilter` | `testGetRecommenderGuideRelationListWithDateFilter` |
| `testGetGuideSupplierListWithDifferentPageSizes` | `testGetRecommenderGuideRelationListWithDifferentPageSizes` |

#### 4.2 测试方法调用更新
**重构前：**
```java
ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
```

**重构后：**
```java
ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getRecommenderGuideRelationList(queryDto);
```

### 5. Controller调用更新

**重构前：**
```java
return recommenderRelationService.getTourGuideSupplierList(queryDto);
```

**重构后：**
```java
return recommenderRelationService.getRecommenderGuideRelationList(queryDto);
```

### 6. 导入依赖完善

**新增导入：**
```java
import cn.hutool.core.collection.CollUtil;
```

## 重构效果对比

### 1. 代码一致性
**重构前：** 两个查询方法的结构和命名风格不一致，维护困难
**重构后：** 完全一致的结构和命名风格，易于理解和维护

### 2. 错误处理
**重构前：** 日志信息有错误，参数验证逻辑略有差异
**重构后：** 统一的错误处理和日志信息，更加准确

### 3. 代码质量
**重构前：** 存在代码重复和不一致的问题
**重构后：** 消除了不一致性，提高了代码质量

### 4. 可维护性
**重构前：** 需要分别维护两套相似但不同的逻辑
**重构后：** 统一的结构使维护更加简单

## 涉及的文件

### 1. 核心业务文件
- `src/main/java/tripai/recommend/system/service/RecommenderRelationService.java`
- `src/main/java/tripai/recommend/system/service/impl/RecommenderRelationServiceImpl.java`
- `src/main/java/tripai/recommend/system/controller/RecommenderRelationController.java`

### 2. 测试文件
- `src/test/java/tripai/recommend/system/TestRecommenderRelationService.java`

### 3. 文档文件
- 本重构说明文档

## 验证步骤

### 1. 编译验证
```bash
mvn clean compile
```

### 2. 单元测试验证
```bash
mvn test -Dtest=TestRecommenderRelationService#testGetRecommenderGuideRelationListSuccess
```

### 3. 集成测试验证
```bash
# 启动应用，调用API接口验证功能正常
curl -X POST "http://localhost:8080/api/recommender/guide/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "sortType": 1,
    "pageNum": 1,
    "pageSize": 50
  }'
```

## 总结

本次重构成功地将导游关系查询方法与酒店关系查询方法统一，实现了：

1. **命名一致性**：方法名、参数名、变量名都保持一致的风格
2. **结构一致性**：处理流程、步骤编号、注释格式完全一致
3. **逻辑一致性**：参数验证、错误处理、数据处理逻辑统一
4. **测试一致性**：测试方法名称和测试逻辑保持一致

重构后的代码更加清晰、易于理解和维护，为后续的功能扩展和优化奠定了良好的基础。
