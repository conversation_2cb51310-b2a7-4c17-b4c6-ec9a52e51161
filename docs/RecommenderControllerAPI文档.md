# RecommenderController API文档

## 接口概述

推荐方认证资料管理接口，提供推荐方认证资料的保存/更新和审核详情查询功能。

**基础路径**: `/recommender`

**认证方式**: 需要在请求头中携带Authorization token

---

## 1. 保存/更新认证资料

### 接口基本信息
- **接口路径**: `POST /recommender/profile`
- **接口描述**: 保存或更新推荐方的认证资料信息
- **认证要求**: 需要Authorization token

### 请求参数

#### 请求头参数
```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // 用户认证token，必填
  "Content-Type": "application/json" // 请求内容类型
}
```

#### 请求体参数
```json
{
  "userId": 1, // 用户ID（系统自动设置，前端无需传递）
  "isDraft": false, // 是否为草稿：true=保存草稿，false=提交审核
  "type": 1, // 推荐方类型：1=个人 2=企业
  "name": "张三", // 个人姓名/企业全称
  "certificateType": 1, // 证件类型：1=身份证 2=护照 3=港澳通行证 4=台胞证 5=外国人永久居留证 6=其他（个人类型使用，企业类型为NULL）
  "identifier": "******************", // 身份证号/统一社会信用代码
  "avatarUrl": { // 头像URL（可选）
    "bucket": "rms", // 存储桶名称
    "name": "avatar.jpg", // 文件名
    "url": "http://example.com/rms/avatar.jpg", // 文件URL
    "type": 1 // 文件类型：1=图片 2=视频
  },
  "idCardFrontUrl": { // 身份证正面图片URL（个人类型必填）
    "bucket": "rms", // 存储桶名称
    "name": "id_card_front.jpg", // 文件名
    "url": "http://example.com/rms/id_card_front.jpg", // 文件URL
    "type": 1 // 文件类型：1=图片 2=视频
  },
  "idCardBackUrl": { // 身份证反面图片URL（个人类型必填）
    "bucket": "rms", // 存储桶名称
    "name": "id_card_back.jpg", // 文件名
    "url": "http://example.com/rms/id_card_back.jpg", // 文件URL
    "type": 1 // 文件类型：1=图片 2=视频
  },
  "businessLicenseUrl": { // 营业执照图片URL（企业类型必填）
    "bucket": "rms", // 存储桶名称
    "name": "business_license.jpg", // 文件名
    "url": "http://example.com/rms/business_license.jpg", // 文件URL
    "type": 1 // 文件类型：1=图片 2=视频
  },
  "bankInfo": { // 银行卡信息
    "id": 1, // 银行卡ID（修改时需要）
    "accountName": "张三", // 开户名（个人=姓名；企业=企业全称）
    "accountCertificateType": 1, // 开户证件类型：1=身份证 2=护照 3=港澳通行证 4=台胞证 5=外国人永久居留证 6=其他
    "accountIdentifier": "******************", // 开户证件号码/纳税人识别号
    "idCardNo": "******************", // 开户证件号(个人类型使用)
    "accountPhone": "***********", // 开户手机号
    "bankCardNo": "6222021234567890123", // 银行卡号/对公账号
    "bankName": "中国工商银行", // 开户行
    "bankCode": "102", // 银行编号
    "branchCode": "************", // 支行编号
    "branchName": "中国工商银行广州分行", // 支行名称
    "province": "广东省", // 所在省份
    "city": "广州市", // 所在城市
    "bankAddress": "广州市天河区珠江新城" // 银行地址
  }
}
```

### 响应结果

#### 成功响应 (200)
```json
{
  "code": 200, // 响应状态码
  "message": "OK", // 响应消息
  "data": {
    "recommenderId": 123 // 推荐方ID
  }
}
```

#### 错误响应
```json
{
  "code": 400, // 错误状态码
  "message": "参数验证失败", // 参数验证错误消息
  "data": null // 数据为空
}
```

```json
{
  "code": 401, // 错误状态码
  "message": "api.user.token.token-not-resolve-user", // Token错误消息
  "data": null // 数据为空
}
```

### 错误码说明
- `400`: 参数验证失败
- `401`: Token解析失败或用户ID为空
- `500`: 服务器内部错误

---

## 2. 查询审核详情

### 接口基本信息
- **接口路径**: `GET /recommender/audit-detail/{recommenderId}`
- **接口描述**: 根据推荐方ID查询审核详情信息
- **认证要求**: 需要Authorization token

### 请求参数

#### 请求头参数
```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." // 用户认证token，必填
}
```

#### 路径参数
- `recommenderId`: 推荐方ID，必填，Long类型

#### 请求示例
```
GET /recommender/audit-detail/123
```

### 响应结果

#### 成功响应 (200)
```json
{
  "code": 200, // 响应状态码
  "message": "OK", // 响应消息
  "data": {
    "recommenderId": 123, // 推荐方ID
    "type": 1, // 推荐方类型：1=个人 2=企业
    "auditStatus": 2, // 审核状态：0=待审核 1=审核中 2=已审核
    "auditResult": 1, // 审核结果：1=通过 2=不通过
    "rejectReason": null, // 驳回原因（仅当审核结果为不通过时填写）
    "auditTime": "2025-01-30T15:00:00", // 审核时间
    "submitTime": "2025-01-30T10:00:00", // 提交审核时间
    
    // ==================== 身份信息字段（通用） ====================
    "name": "张三", // 推荐方名称（个人=姓名，企业=企业全称）
    "identifier": "******************", // 证件号码（个人=身份证号/护照号等，企业=统一社会信用代码）
    "certificateType": 1, // 证件类型：1=身份证 2=护照 3=港澳通行证 4=台胞证 5=外国人永久居留证 6=其他（仅个人类型使用，企业类型为null）
    
    "idCardFrontUrl": { // 身份证人像面照片（仅个人类型使用）
      "bucket": "rms", // 存储桶名称
      "name": "id_card_front.jpg", // 文件名
      "url": "http://example.com/rms/id_card_front.jpg", // 文件URL
      "type": 1 // 文件类型：1=图片 2=视频
    },
    "idCardBackUrl": { // 身份证国徽面照片（仅个人类型使用）
      "bucket": "rms", // 存储桶名称
      "name": "id_card_back.jpg", // 文件名
      "url": "http://example.com/rms/id_card_back.jpg", // 文件URL
      "type": 1 // 文件类型：1=图片 2=视频
    },
    "businessLicenseUrl": { // 营业执照扫描件（仅企业类型使用）
      "bucket": "rms", // 存储桶名称
      "name": "business_license.jpg", // 文件名
      "url": "http://example.com/rms/business_license.jpg", // 文件URL
      "type": 1 // 文件类型：1=图片 2=视频
    },
    
    // ==================== 收款信息字段（通用） ====================
    "accountName": "张三", // 开户名（个人=姓名，企业=企业全称）
    "accountCertificateType": 1, // 开户证件类型：1=身份证 2=护照 3=港澳通行证 4=台胞证 5=外国人永久居留证 6=其他
    "accountIdentifier": "******************", // 开户证件号（个人=身份证号等，企业=统一社会信用代码）
    "accountPhone": "***********", // 开户手机号
    "bankCardNo": "6222021234567890123", // 银行卡号/对公账号
    "bankName": "中国工商银行", // 开户银行
    "branchName": "中国工商银行广州分行", // 支行名称
    "province": "广东省", // 所在省份
    "city": "广州市", // 所在城市
    "bankAddress": "广州市天河区珠江新城" // 银行地址
  }
}
```

#### 错误响应
```json
{
  "code": 401, // 错误状态码
  "message": "api.user.token.token-not-resolve-user", // 错误消息
  "data": null // 数据为空
}
```

```json
{
  "code": 404, // 错误状态码
  "message": "推荐方不存在", // 错误消息
  "data": null // 数据为空
}
```

### 错误码说明
- `401`: Token解析失败或用户ID为空
- `404`: 推荐方不存在或无权限查看
- `500`: 服务器内部错误

---

## 调用示例

### 1. 保存个人认证资料示例

```bash
curl -X POST "http://localhost:8080/recommender/profile" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "isDraft": false,
    "type": 1,
    "name": "张三",
    "certificateType": 1,
    "identifier": "******************",
    "idCardFrontUrl": {
      "bucket": "rms",
      "name": "id_card_front.jpg",
      "url": "http://example.com/rms/id_card_front.jpg",
      "type": 1
    },
    "idCardBackUrl": {
      "bucket": "rms",
      "name": "id_card_back.jpg",
      "url": "http://example.com/rms/id_card_back.jpg",
      "type": 1
    },
    "bankInfo": {
      "accountName": "张三",
      "accountPhone": "***********",
      "bankCardNo": "6222021234567890123",
      "bankName": "中国工商银行",
      "branchName": "中国工商银行广州分行",
      "province": "广东省",
      "city": "广州市"
    }
  }'
```

### 2. 保存企业认证资料示例

```bash
curl -X POST "http://localhost:8080/recommender/profile" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "isDraft": false,
    "type": 2,
    "name": "测试企业有限公司",
    "identifier": "91110000123456789X",
    "businessLicenseUrl": {
      "bucket": "rms",
      "name": "business_license.jpg",
      "url": "http://example.com/rms/business_license.jpg",
      "type": 1
    },
    "bankInfo": {
      "accountName": "测试企业有限公司",
      "accountPhone": "***********",
      "bankCardNo": "1234567890123456789",
      "bankName": "中国工商银行",
      "branchName": "中国工商银行北京分行",
      "province": "北京市",
      "city": "北京市"
    }
  }'
```

### 3. 查询审核详情示例

```bash
curl -X GET "http://localhost:8080/recommender/audit-detail/123" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

---

## 注意事项

1. **认证要求**: 所有接口都需要在请求头中携带有效的Authorization token
2. **文件格式**: 图片字段需要使用FileInfo对象格式，包含bucket、name、url、type字段
3. **个人vs企业**: 
   - 个人类型需要提供身份证正反面照片
   - 企业类型需要提供营业执照照片
4. **草稿功能**: isDraft=true时保存为草稿，false时提交审核
5. **权限控制**: 用户只能操作自己的认证资料

## 枚举值说明

### 推荐方类型 (type)
- `1`: 个人
- `2`: 企业

### 证件类型 (certificateType)
- `1`: 身份证
- `2`: 护照
- `3`: 港澳通行证
- `4`: 台胞证
- `5`: 外国人永久居留证
- `6`: 其他

### 审核状态 (auditStatus)
- `0`: 待审核
- `1`: 审核中
- `2`: 已审核

### 审核结果 (auditResult)
- `1`: 通过
- `2`: 不通过

### 文件类型 (FileInfo.type)
- `1`: 图片
- `2`: 视频
