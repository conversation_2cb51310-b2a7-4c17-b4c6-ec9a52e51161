# 导游关系排序类型修改说明

## 修改概述

本次修改将导游关系查询中的 `sortType` 字段从 `String` 类型修改为 `Integer` 类型，与酒店关系查询保持一致，提供更好的类型安全性和API一致性。

## 修改详情

### 1. 排序类型定义

**修改前：**
- 类型：`String`
- 默认值：`"1"`
- 排序规则：基于字符串标识

**修改后：**
- 类型：`Integer`
- 默认值：`1`
- 排序规则：
  - `1`: 建立时间从新到旧（默认）
  - `2`: 服务分从高到低
  - `3`: 上架服务数从高到低
  - `4`: 成单数从高到低

### 2. 涉及的文件修改

#### 2.1 枚举类
**GuideRelationSortTypeEnum.java** (重新创建)
- 修改所有枚举值的code类型从`String`改为`Integer`
- 更新构造函数和相关方法的参数类型
- 修改`getByCode()`、`isValidCode()`等方法的参数类型
- 更新`getSqlOrderBy()`方法的参数类型

#### 2.2 DTO类
**RecommenderGuideRelationQueryDto.java**
- 修改`sortType`字段类型为`Integer`
- 更新字段注释说明
- 添加`GuideRelationSortTypeEnum`导入
- 添加`getSqlOrderBy()`方法

#### 2.3 VO类
**RecommenderGuideRelationListVo.java**
- 修改`sortType`字段类型为`Integer`
- 新增`sortDescription`字段用于显示排序描述

#### 2.4 Service实现类
**RecommenderRelationServiceImpl.java**
- 更新`validateAndProcessGuideQueryParams()`方法中的排序类型验证
- 修改`buildGuideRelationListVo()`方法，设置排序描述
- 确保数据转换和VO构建正确

#### 2.5 XML映射文件
**RecommenderRelationMapper.xml**
- 更新导游查询的排序逻辑，使用Integer类型比较
- 修改排序SQL，统一使用`rr.id ASC`作为次要排序条件
- 完善各种排序类型的SQL实现

#### 2.6 测试类
**TestRecommenderRelationService.java**
- 更新`buildGuideQueryDto()`方法中的sortType设置
- 修改所有测试方法中的排序类型使用

#### 2.7 文档更新
- **RecommenderRelationController完整API文档.md**
- **TestRecommenderRelationService使用说明.md**

### 3. 排序逻辑实现

#### 3.1 XML中的排序逻辑
```xml
<choose>
    <when test="query.sortType == 2">
        ORDER BY tgs.service_score DESC, rr.create_time DESC, rr.id ASC
    </when>
    <when test="query.sortType == 3">
        ORDER BY service_count DESC, rr.create_time DESC, rr.id ASC
    </when>
    <when test="query.sortType == 4">
        ORDER BY order_count DESC, rr.create_time DESC, rr.id ASC
    </when>
    <otherwise>
        ORDER BY rr.create_time DESC, rr.id ASC
    </otherwise>
</choose>
```

#### 3.2 排序规则说明

**排序类型1（默认）：建立时间从新到旧**
- 主要排序：`rr.create_time DESC` - 按创建时间降序
- 次要排序：`rr.id ASC` - 相同创建时间时按ID升序

**排序类型2：服务分从高到低**
- 主要排序：`tgs.service_score DESC` - 按服务分降序
- 次要排序：`rr.create_time DESC` - 相同服务分时按创建时间降序
- 第三排序：`rr.id ASC` - 最终按ID升序

**排序类型3：上架服务数从高到低**
- 主要排序：`service_count DESC` - 按服务数降序
- 次要排序：`rr.create_time DESC` - 相同服务数时按创建时间降序
- 第三排序：`rr.id ASC` - 最终按ID升序

**排序类型4：成单数从高到低**
- 主要排序：`order_count DESC` - 按成单数降序
- 次要排序：`rr.create_time DESC` - 相同成单数时按创建时间降序
- 第三排序：`rr.id ASC` - 最终按ID升序

### 4. API变更影响

#### 4.1 请求参数变更
**修改前：**
```json
{
  "sortType": "service_score_desc",
  "pageNum": 1,
  "pageSize": 50
}
```

**修改后：**
```json
{
  "sortType": 2,
  "pageNum": 1,
  "pageSize": 50
}
```

#### 4.2 响应数据变更
**新增字段：**
```json
{
  "sortType": 2,
  "sortDescription": "服务分从高到低",
  "suppliers": [...]
}
```

### 5. 向后兼容性

**注意：** 本次修改涉及API接口参数类型变更，属于**破坏性变更**。

#### 5.1 影响范围
- 前端调用需要修改排序类型参数从字符串改为数字
- 现有的API调用需要更新请求参数格式
- 数据库查询逻辑发生变化

#### 5.2 迁移建议
1. **前端修改：** 将排序类型参数从字符串改为数字
2. **API调用：** 更新所有相关的API调用代码
3. **测试验证：** 全面测试排序功能的正确性
4. **文档更新：** 确保所有相关文档都已更新

### 6. 验证步骤

#### 6.1 单元测试
```bash
# 运行所有相关测试
mvn test -Dtest=TestRecommenderRelationService

# 运行特定的导游排序测试
mvn test -Dtest=TestRecommenderRelationService#testGetTourGuideSupplierListSuccess
```

#### 6.2 集成测试
1. 验证API接口的正确响应
2. 确认排序逻辑的正确性
3. 测试无效参数的处理

#### 6.3 性能测试
1. 验证排序性能没有显著下降
2. 确认数据库查询效率
3. 测试大数据量下的排序表现

### 7. 技术优势

#### 7.1 类型安全
- 使用Integer类型提供更好的类型安全性
- 避免字符串比较可能出现的问题
- 编译时类型检查

#### 7.2 API一致性
- 与酒店关系查询的sortType类型保持一致
- 统一的API设计风格
- 更好的开发体验

#### 7.3 性能优化
- Integer比较比字符串比较更高效
- 减少字符串处理开销
- 更好的数据库查询性能

#### 7.4 可维护性
- 枚举类提供了更好的代码组织
- 统一的状态管理
- 易于扩展新的排序类型

### 8. 部署注意事项

1. **数据库兼容性：** 确保相关表结构存在
2. **前端同步更新：** 前端代码需要同步修改排序参数类型
3. **API文档发布：** 及时更新和发布新的API文档
4. **回滚准备：** 准备回滚方案以防出现问题

## 总结

本次修改成功将导游关系排序类型从字符串改为整数类型，提供了更清晰的排序规则定义和更好的类型安全性。通过引入枚举类管理和完善的参数验证，提高了代码的可维护性和健壮性。同时，统一的排序次序规则（按ID升序）确保了数据排序的一致性和可预测性。

修改后的系统具有更好的：
- **类型安全性**：Integer类型避免了字符串处理的潜在问题
- **API一致性**：与酒店关系查询保持统一的设计风格
- **性能表现**：Integer比较比字符串比较更高效
- **可维护性**：枚举类提供了更好的代码组织和管理
