# RecommenderBankServiceImpl银行卡验证集成说明

## 修改概述

在`RecommenderBankServiceImpl.java`的`saveBankAccount`方法中集成银行卡三要素验证功能，实现银行账户保存后自动进行验证并更新验证状态。

## 修改目标

1. **自动化验证**：银行账户保存成功后自动进行三要素验证
2. **状态管理**：根据验证结果自动更新数据库中的验证状态
3. **统计功能**：验证调用被纳入频率限制统计
4. **异常容错**：验证失败不影响银行账户信息的保存

## 修改详情

### 1. 新增依赖和导入

#### 新增import语句
```java
import tripai.recommend.system.domain.dto.BankCardVerifyDto;
import tripai.recommend.system.domain.vo.BankCardVerifyResultVo;
import tripai.recommend.system.service.TencentBankCardVerifyService;
```

#### 新增依赖注入
```java
@Resource
private TencentBankCardVerifyService tencentBankCardVerifyService;
```

### 2. 修改saveBankAccount方法

#### 修改前的流程
```
1. 参数验证
2. 查询推荐方信息
3. 构建银行账户信息
4. 保存到数据库
5. 返回结果
```

#### 修改后的流程
```
1. 参数验证
2. 查询推荐方信息
3. 构建银行账户信息
4. 保存到数据库
5. 执行银行卡三要素验证
6. 根据验证结果更新状态
7. 返回结果
```

### 3. 新增核心方法

#### performBankCardVerification方法
```java
private void performBankCardVerification(Long userId, RecommenderBank bankInfo, RecommenderBankAccountDto dto) {
    // 构建银行卡验证DTO
    BankCardVerifyDto verifyDto = new BankCardVerifyDto();
    verifyDto.setBankCardNumber(dto.getBankCardNo());
    verifyDto.setCardholderName(dto.getAccountName());
    verifyDto.setIdNumber(dto.getAccountIdentifier());

    // 调用带频率限制的银行卡验证服务
    BankCardVerifyResultVo verifyResult = tencentBankCardVerifyService.verifyBankCardWithRateLimit(userId, verifyDto);
    
    // 根据验证结果更新验证状态
    updateBankVerificationStatus(bankInfo, verifyResult);
}
```

**功能**：
- 将`RecommenderBankAccountDto`转换为`BankCardVerifyDto`
- 调用带频率限制的银行卡验证服务
- 记录详细的验证过程日志

#### updateBankVerificationStatus方法
```java
private void updateBankVerificationStatus(RecommenderBank bankInfo, BankCardVerifyResultVo verifyResult) {
    if (verifyResult.getSuccess()) {
        if (verifyResult.getResult() == 0) {
            // 0=一致，设置为待人工审核
            bankInfo.setValidateStatus(2);
            bankInfo.setUsageStatus(0);
            bankInfo.setRejectReason("银行卡三要素验证通过，等待人工审核");
        } else {
            // 1=不一致 或 2=无记录，设置为验证失败
            bankInfo.setValidateStatus(1);
            bankInfo.setUsageStatus(0);
            bankInfo.setRejectReason("银行卡三要素验证不一致: " + verifyResult.getDescription());
        }
    } else {
        // 验证失败
        bankInfo.setValidateStatus(1);
        bankInfo.setUsageStatus(0);
        bankInfo.setRejectReason("银行卡三要素验证失败: " + verifyResult.getErrorMessage());
    }
    
    // 更新数据库
    recommenderBankMapper.updateById(bankInfo);
}
```

**功能**：
- 根据验证结果设置相应的验证状态和使用状态
- 记录详细的验证失败原因
- 更新数据库中的状态信息

#### maskBankCardNumber方法
```java
private String maskBankCardNumber(String bankCardNumber) {
    if (StrUtil.isBlank(bankCardNumber) || bankCardNumber.length() < 8) {
        return bankCardNumber;
    }
    return bankCardNumber.substring(0, 4) + "****" + bankCardNumber.substring(bankCardNumber.length() - 4);
}
```

**功能**：
- 对银行卡号进行脱敏处理
- 用于日志记录时保护敏感信息

## 状态管理规则

### 数据库字段说明
根据`recommender_bank`表结构：

#### validate_status（验证状态）
- `0`: 未验证（初始状态）
- `1`: 三要素失败（验证不通过）
- `2`: 待人工审核（三要素验证通过）
- `3`: 审核通过（人工审核通过）
- `4`: 审核驳回（人工审核不通过）

#### usage_status（使用状态）
- `0`: 未使用（默认状态）
- `1`: 当前使用
- `2`: 历史使用
- `3`: 已废弃

### 状态设置规则

#### 三要素验证失败时
```java
bankInfo.setValidateStatus(1); // 三要素失败
bankInfo.setUsageStatus(0);    // 未使用
bankInfo.setRejectReason("具体的验证失败原因");
```

#### 三要素验证通过时
```java
bankInfo.setValidateStatus(2); // 待人工审核
bankInfo.setUsageStatus(0);    // 未使用
bankInfo.setRejectReason("银行卡三要素验证通过，等待人工审核");
```

#### 验证异常时
```java
bankInfo.setValidateStatus(1); // 三要素失败
bankInfo.setUsageStatus(0);    // 未使用
bankInfo.setRejectReason("银行卡三要素验证失败: " + 异常信息);
```

## 数据映射关系

### RecommenderBankAccountDto → BankCardVerifyDto
| RecommenderBankAccountDto | BankCardVerifyDto | 说明 |
|---------------------------|-------------------|------|
| bankCardNo | bankCardNumber | 银行卡号 |
| accountName | cardholderName | 开户人姓名 |
| accountIdentifier | idNumber | 开户证件号码 |

### BankCardVerifyResultVo → 数据库状态
| 验证结果 | validate_status | usage_status | reject_reason |
|----------|----------------|--------------|---------------|
| success=true, result=0 | 2 | 0 | 银行卡三要素验证通过，等待人工审核 |
| success=true, result=1 | 1 | 0 | 银行卡三要素验证不一致: 描述信息 |
| success=true, result=2 | 1 | 0 | 银行卡三要素验证不一致: 描述信息 |
| success=false | 1 | 0 | 银行卡三要素验证失败: 错误信息 |

## 异常处理机制

### 1. 验证异常处理
```java
try {
    BankCardVerifyResultVo verifyResult = tencentBankCardVerifyService.verifyBankCardWithRateLimit(userId, verifyDto);
    updateBankVerificationStatus(bankInfo, verifyResult);
} catch (Exception e) {
    log.error("银行卡三要素验证失败，用户ID: {}, 银行账户ID: {}, 错误: {}", userId, bankInfo.getId(), e.getMessage());
    // 设置验证失败状态
    bankInfo.setValidateStatus(1);
    bankInfo.setUsageStatus(0);
    bankInfo.setRejectReason("银行卡三要素验证失败: " + e.getMessage());
    recommenderBankMapper.updateById(bankInfo);
}
```

**特点**：
- 验证异常不影响银行账户信息的保存
- 异常时自动设置为验证失败状态
- 记录详细的异常信息

### 2. 数据一致性保证
- 先保存银行账户信息，再进行验证
- 验证失败时更新验证状态，不回滚银行账户信息
- 确保用户数据不丢失

## 业务流程图

```mermaid
graph TD
    A[开始保存银行账户] --> B[参数验证]
    B --> C[查询推荐方信息]
    C --> D[构建银行账户信息]
    D --> E[保存到数据库]
    E --> F{保存是否成功?}
    F -->|失败| G[返回保存失败]
    F -->|成功| H[执行银行卡三要素验证]
    H --> I{验证是否成功?}
    I -->|成功| J{验证结果是否一致?}
    I -->|失败| K[设置状态为三要素失败]
    J -->|一致| L[设置状态为待人工审核]
    J -->|不一致| K
    K --> M[更新验证状态]
    L --> M
    M --> N[返回成功]
    G --> O[结束]
    N --> O
```

## 日志记录

### 1. 验证开始日志
```java
log.info("开始银行卡三要素验证（带频率限制统计），用户ID: {}, 银行账户ID: {}, 卡号: {}, 姓名: {}", 
        userId, bankInfo.getId(), maskBankCardNumber(dto.getBankCardNo()), dto.getAccountName());
```

### 2. 验证完成日志
```java
log.info("银行卡三要素验证完成，用户ID: {}, 银行账户ID: {}, 结果: {}, 描述: {}", 
        userId, bankInfo.getId(), verifyResult.getSuccess(), verifyResult.getDescription());
```

### 3. 状态更新日志
```java
log.info("银行验证状态更新成功，银行账户ID: {}, 验证状态: {}, 使用状态: {}, 原因: {}", 
        bankInfo.getId(), bankInfo.getValidateStatus(), bankInfo.getUsageStatus(), bankInfo.getRejectReason());
```

### 4. 异常日志
```java
log.error("银行卡三要素验证失败，用户ID: {}, 银行账户ID: {}, 错误: {}", userId, bankInfo.getId(), e.getMessage());
```

## 频率限制统计

### 1. 统计维度
- **分钟级统计**：同一用户1分钟内最多15次
- **天级统计**：同一用户1天内最多30次
- **Redis键格式**：`bank_verify:minute:{userId}` 和 `bank_verify:day:{userId}:{date}`

### 2. 超限处理
如果用户在银行账户保存过程中触发频率限制：
- **返回错误**：验证方法会抛出频率限制异常
- **状态设置**：银行账户状态会被设置为验证失败
- **错误记录**：详细的错误信息会被记录到`reject_reason`字段

### 3. 业务影响
- **正常用户**：不会受到影响，因为正常操作不会频繁保存银行账户
- **异常用户**：如果短时间内多次保存银行账户，可能会触发限制
- **系统保护**：防止恶意或异常的大量API调用

## 测试建议

### 1. 单元测试
```java
@Test
public void testSaveBankAccountWithVerification() {
    // 测试银行账户保存和验证流程
}

@Test
public void testBankCardVerificationSuccess() {
    // 测试银行卡验证成功场景
}

@Test
public void testBankCardVerificationFailure() {
    // 测试银行卡验证失败场景
}

@Test
public void testVerificationException() {
    // 测试验证异常处理
}
```

### 2. 集成测试
```java
@Test
public void testSaveBankAccountEndToEnd() {
    // 测试完整的银行账户保存流程，包括银行卡验证
}
```

### 3. 边界测试
- 测试验证服务异常情况
- 测试数据库更新失败情况
- 测试频率限制触发情况

## 监控建议

### 1. 业务指标
- 银行账户保存成功率
- 银行卡验证成功率
- 验证失败原因分布
- 频率限制触发次数

### 2. 技术指标
- 验证服务调用次数
- 验证异常次数
- 数据库更新成功率
- 验证响应时间

## 注意事项

### 1. 数据安全
- 银行卡号在日志中进行脱敏处理
- 验证失败原因不暴露敏感信息

### 2. 业务逻辑
- 验证失败不影响银行账户信息保存，保证用户体验
- 状态设置严格按照数据库表结构规范

### 3. 扩展性
- 验证逻辑独立封装，便于后续扩展
- 支持不同类型的银行卡验证

## 总结

通过这次修改，实现了：

1. **自动化验证**：银行账户保存后自动进行三要素验证
2. **状态管理**：根据验证结果自动更新验证状态和使用状态
3. **异常容错**：验证失败不影响银行账户信息保存
4. **日志完善**：详细记录验证过程和结果
5. **数据安全**：敏感信息脱敏处理
6. **统计功能**：验证调用被纳入频率限制统计

这个集成方案既保证了数据验证的及时性，又确保了系统的稳定性和用户体验，同时为API使用情况的统计和监控提供了支持。
