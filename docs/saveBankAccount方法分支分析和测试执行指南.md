# saveBankAccount方法分支分析和测试执行指南

## 1. 方法分支分析

### 1.1 方法概述
- **方法位置**: `RecommenderBankService.saveBankAccount(RecommenderBankAccountDto dto)`
- **主要功能**: 保存或更新推荐方银行账户信息
- **事务特性**: `@Transactional(rollbackFor = Exception.class)`

### 1.2 完整分支结构

```java
@Override
@Transactional(rollbackFor = Exception.class)
public ResponseResult<Boolean> saveBankAccount(RecommenderBankAccountDto dto) {
    try {
        // 分支B1: userId null检查
        if (userId == null) {
            return ResponseResult.fail("用户userId为空");
        }

        // 分支B2: 推荐方信息存在性检查
        if (recommenderInfo == null) {
            return ResponseResult.fail("推荐方信息不存在");
        }

        // 分支B3: 银行账户ID检查（更新 vs 创建）
        if (dto.getId() != null) {
            // 分支B3a: 更新现有银行账户
            // 分支B4: 银行账户存在性和归属检查
            if (bankInfo == null || !bankInfo.getRecommenderId().equals(recommenderInfo.getId())) {
                return ResponseResult.fail("银行账户信息不存在");
            }
        } else {
            // 分支B3b: 创建新的银行账户
        }

        // 分支B5: 数据库操作
        if (dto.getId() != null) {
            // 分支B5a: 更新操作
            saveResult = recommenderBankMapper.updateById(bankInfo) > 0;
        } else {
            // 分支B5b: 插入操作
            saveResult = recommenderBankMapper.insert(bankInfo) > 0;
        }

        // 分支B6: 保存结果检查
        if (saveResult) {
            return ResponseResult.ok(true);
        } else {
            return ResponseResult.fail("保存银行账户信息失败");
        }

    } catch (Exception e) {
        // 分支B7: 异常处理
        return ResponseResult.fail("保存银行账户信息失败");
    }
}
```

### 1.3 分支详细分析

| 分支ID | 分支类型 | 分支条件 | 返回结果 | 测试方法 |
|--------|----------|----------|----------|----------|
| B1 | if/else | `userId == null` | 失败："用户userId为空" | `test02_BranchB1_UserIdNull` |
| B2 | if/else | `recommenderInfo == null` | 失败："推荐方信息不存在" | `test03_BranchB2_RecommenderInfoNotExists` |
| B3a | if分支 | `dto.getId() != null` | 进入更新流程 | `test04-07_BranchB3a_*` |
| B3b | else分支 | `dto.getId() == null` | 进入创建流程 | `test08-09_BranchB3b_*` |
| B4 | if/else | `bankInfo == null \|\| !bankInfo.getRecommenderId().equals(recommenderInfo.getId())` | 失败："银行账户信息不存在" | `test04-05_BranchB3a_B4_*` |
| B5a | if分支 | `dto.getId() != null` | 执行更新操作 | `test06-07_BranchB3a_B5a_*` |
| B5b | else分支 | `dto.getId() == null` | 执行插入操作 | `test08-09_BranchB3b_B5b_*` |
| B6 | if/else | `saveResult` | 成功/失败 | 所有成功/失败测试 |
| B7 | try/catch | `catch (Exception e)` | 失败："保存银行账户信息失败" | `test10_BranchB7_ExceptionHandling` |

## 2. 测试方法执行顺序

### 2.1 测试执行顺序说明

测试方法按照以下顺序设计和执行：

1. **数据完整性验证**（最优先）
2. **基础验证分支**（B1, B2）
3. **更新流程分支**（B3a + B4, B3a + B5a + B6）
4. **创建流程分支**（B3b + B5b + B6）
5. **异常处理分支**（B7）
6. **综合场景测试**

### 2.2 具体测试方法列表

| 执行顺序 | 测试方法 | 目标分支 | 测试场景 |
|---------|----------|----------|----------|
| 01 | `test01_DataIntegrityVerification` | 数据完整性 | 验证测试环境和依赖 |
| 02 | `test02_BranchB1_UserIdNull` | B1 | userId为null |
| 03 | `test03_BranchB2_RecommenderInfoNotExists` | B2 | 推荐方信息不存在 |
| 04 | `test04_BranchB3a_B4_UpdateBankAccountNotExists` | B3a + B4 | 更新不存在的银行账户 |
| 05 | `test05_BranchB3a_B4_UpdateBankAccountNotBelongToUser` | B3a + B4 | 更新不属于当前用户的银行账户 |
| 06 | `test06_BranchB3a_B5a_B6_UpdateBankAccountSuccess` | B3a + B5a + B6 | 更新银行账户成功 |
| 07 | `test07_BranchB3a_B5a_B6_UpdateBankAccountFailed` | B3a + B5a + B6 | 更新银行账户失败 |
| 08 | `test08_BranchB3b_B5b_B6_CreateBankAccountSuccess` | B3b + B5b + B6 | 创建银行账户成功 |
| 09 | `test09_BranchB3b_B5b_B6_CreateBankAccountFailed` | B3b + B5b + B6 | 创建银行账户失败 |
| 10 | `test10_BranchB7_ExceptionHandling` | B7 | 异常处理 |
| 11 | `test11_ComprehensiveScenarioTest` | 综合 | 综合场景验证 |

## 3. 运行测试验证

### 3.1 运行单个测试方法

```bash
# 按顺序运行单个测试方法
mvn test -Dtest=TestRecommenderBankServiceSaveBankAccount#test01_DataIntegrityVerification
mvn test -Dtest=TestRecommenderBankServiceSaveBankAccount#test02_BranchB1_UserIdNull
mvn test -Dtest=TestRecommenderBankServiceSaveBankAccount#test03_BranchB2_RecommenderInfoNotExists
# ... 依此类推
```

### 3.2 运行所有测试

```bash
# 运行整个测试类
mvn test -Dtest=TestRecommenderBankServiceSaveBankAccount

# 运行测试并生成覆盖率报告
mvn clean test jacoco:report -Dtest=TestRecommenderBankServiceSaveBankAccount
```

### 3.3 运行特定分支测试

```bash
# 运行更新相关测试
mvn test -Dtest=TestRecommenderBankServiceSaveBankAccount#test*Update*

# 运行创建相关测试
mvn test -Dtest=TestRecommenderBankServiceSaveBankAccount#test*Create*

# 运行异常处理测试
mvn test -Dtest=TestRecommenderBankServiceSaveBankAccount#test*Exception*
```

## 4. 预期测试结果

### 4.1 成功测试的日志输出

每个测试方法都会输出详细的日志信息：

```
=== 测试saveBankAccount方法 - 分支B1：userId为null的情况 ===
测试数据: {"userId":null,"bankCardNo":"****************",...}
执行时间: 15ms
测试结果: {"code":400,"message":"用户userId为空","data":null}
✅ 分支B1测试通过：userId为null时正确返回失败结果
```

### 4.2 分支覆盖率验证

使用JaCoCo验证分支覆盖率：

1. 生成覆盖率报告：`mvn clean test jacoco:report`
2. 打开报告：`target/site/jacoco/index.html`
3. 导航到`RecommenderBankService.saveBankAccount`方法
4. 确认所有分支显示为绿色（已覆盖）

### 4.3 预期覆盖率结果

- **分支覆盖率**: 100%
- **行覆盖率**: ≥95%
- **方法覆盖率**: 100%

## 5. Mock策略说明

### 5.1 Mock Bean配置

```java
@MockBean
private RecommenderInfoMapper recommenderInfoMapper;

@MockBean
private RecommenderBankMapper recommenderBankMapper;
```

### 5.2 Mock使用场景

| Mock对象 | Mock方法 | 使用场景 |
|---------|----------|----------|
| `recommenderInfoMapper` | `selectOne()` | 模拟推荐方信息查询 |
| `recommenderBankMapper` | `selectById()` | 模拟银行账户查询 |
| `recommenderBankMapper` | `updateById()` | 模拟更新操作 |
| `recommenderBankMapper` | `insert()` | 模拟插入操作 |

### 5.3 Mock验证

每个测试方法都包含Mock调用验证：

```java
// 验证Mock调用次数和参数
verify(recommenderInfoMapper, times(1)).selectOne(any(), eq(false));
verify(recommenderBankMapper, times(1)).updateById(any());
verify(recommenderBankMapper, never()).insert(any());
```

## 6. 测试数据管理

### 6.1 测试数据构建

```java
private RecommenderBankAccountDto buildValidBankAccountDto() {
    RecommenderBankAccountDto dto = new RecommenderBankAccountDto();
    dto.setUserId(1L);
    dto.setBankCardNo("****************");
    dto.setBankName("工商银行");
    dto.setAccountName("张三");
    dto.setAccountType(1);
    return dto;
}
```

### 6.2 Mock数据构建

```java
private RecommenderInfo buildMockRecommenderInfo() {
    RecommenderInfo recommenderInfo = new RecommenderInfo();
    recommenderInfo.setId(1L);
    recommenderInfo.setUserId(1L);
    // ... 设置其他字段
    return recommenderInfo;
}
```

## 7. 故障排除

### 7.1 常见问题

#### 问题1：Mock不生效
**解决方案**：
- 确保使用了`@MockBean`注解
- 检查Mock方法签名是否正确
- 验证`when().thenReturn()`设置

#### 问题2：测试顺序问题
**解决方案**：
- 每个测试方法都是独立的
- 使用`reset()`重置Mock状态
- 避免测试间的数据依赖

#### 问题3：分支未覆盖
**解决方案**：
- 检查测试数据是否正确
- 确认Mock设置是否触发了目标分支
- 查看测试日志确认执行路径

### 7.2 调试技巧

1. **启用详细日志**：查看方法执行路径
2. **使用断点调试**：逐步验证分支执行
3. **检查Mock调用**：确认Mock被正确调用

## 8. 总结

### 8.1 测试特点

- **完整的分支覆盖**：覆盖所有9个主要分支
- **有序的测试执行**：按逻辑顺序设计测试方法
- **详细的分支标注**：每个测试方法都明确标注目标分支
- **专业的Mock策略**：使用Mock模拟各种场景
- **完整的验证机制**：包含断言和Mock调用验证

### 8.2 预期效果

通过这套测试方法，`saveBankAccount`方法应该能够达到：
- ✅ 100%分支覆盖率
- ✅ 完整的异常处理测试
- ✅ 全面的业务逻辑验证
- ✅ 可靠的回归测试保障

按照本指南执行测试，您应该能够验证`saveBankAccount`方法的所有分支都被正确覆盖。
