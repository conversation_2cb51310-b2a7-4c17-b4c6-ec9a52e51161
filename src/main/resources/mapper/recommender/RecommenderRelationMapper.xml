<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tripai.recommend.system.mapper.recommender.RecommenderRelationMapper">

    <!-- ================================ 数据库表名统一管理 ================================ -->

    <!-- 推荐系统数据库表 -->
    <sql id="TABLE_RECOMMENDER_RELATION">somytrip_recommend_system.recommender_relation</sql>

    <!-- 酒店供应商数据库表 -->
    <sql id="TABLE_HOTEL_INFO">somytrip_hotel_supplier.hotel_info</sql>
    <sql id="TABLE_HOTEL_USER_INFO">somytrip_hotel_supplier.user_info</sql>
    <sql id="TABLE_HOTEL_ROOMS">somytrip_hotel_supplier.hotel_rooms</sql>
    <sql id="TABLE_MANAGER_ORDER">somytrip_hotel_supplier.manager_order</sql>

    <!-- 导游系统数据库表 -->
    <sql id="TABLE_TOUR_GUIDE_SETTLEMENT">somytrip_hotel_supplier.tour_guide_settlement</sql>
    <sql id="TABLE_TOUR_GUIDE_CERTIFICATES">somytrip_hotel_supplier.tour_guide_certificates</sql>
    <sql id="TABLE_TOUR_GUIDE_SERVICE_INFO">somytrip_hotel_supplier.tour_guide_service_info</sql>
    <sql id="TABLE_TOUR_GUIDE_ORDER">somytrip_hotel_supplier.tour_guide_order</sql>

    <!-- 支付系统数据库表 -->
    <sql id="TABLE_PAY_ORDER">somytrip_order.pay_order</sql>

    <!-- 供应商操作日志表 -->
    <sql id="TABLE_SUPPLIER_OPERATION_ORDER_LOG">`oct_iri_db_tourism-ai`.supplier_operation_order_log</sql>

    <!-- ================================ 结果映射 ================================ -->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="tripai.recommend.system.domain.entity.RecommenderRelation">
        <id column="id" property="id" />
        <result column="recommender_id" property="recommenderId" />
        <result column="biz_type" property="bizType" />
        <result column="biz_id" property="bizId" />
        <result column="user_id" property="userId" />
        <result column="relation_status" property="relationStatus" />
        <result column="create_time" property="createTime" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 推荐方酒店关系VO结果映射 -->
    <resultMap id="RecommenderHotelRelationVoMap" type="tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationVo">
        <result column="relation_id" property="relationId"/>
        <result column="recommender_id" property="recommenderId"/>
        <result column="hotel_id" property="hotelId"/>
        <result column="hotel_name" property="hotelName"/>
        <result column="hotel_supplier_code" property="hotelSupplierCode"/>
        <result column="supplier_mobile" property="supplierMobile"/>
        <result column="supplier_nick_name" property="supplierNickName"/>
        <result column="online_room_count" property="onlineRoomCount"/>
        <result column="relation_create_time" property="relationCreateTime"/>
        <result column="order_count" property="orderCount"/>
        <result column="relation_status" property="relationStatus"/>
        <result column="hotel_status" property="hotelStatus"/>
        <result column="hotel_address" property="hotelAddress"/>
        <result column="star_rate" property="starRate"/>
        <result column="score" property="score"/>
        <result column="city_name" property="cityName"/>
        <result column="biz_type" property="bizType"/>
    </resultMap>

    <!-- 统计信息结果映射 -->
    <resultMap id="StatisticsVoMap" type="tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationListVo$RecommenderHotelRelationStatisticsVo">
        <result column="total_relation_count" property="totalRelationCount"/>
        <result column="total_hotel_count" property="totalHotelCount"/>
        <result column="total_online_room_count" property="totalOnlineRoomCount"/>
        <result column="total_order_count" property="totalOrderCount"/>
        <result column="active_relation_count" property="activeRelationCount"/>
        <result column="suspend_relation_count" property="suspendRelationCount"/>
        <result column="avg_room_count_per_relation" property="avgRoomCountPerRelation"/>
        <result column="avg_order_count_per_relation" property="avgOrderCountPerRelation"/>
    </resultMap>

    <!-- 通用查询条件 -->
    <sql id="commonWhereCondition">
        <where>
            rr.recommender_id = #{query.recommenderId}
            AND rr.biz_type = 1
            AND rr.is_del = 0
            <if test="query.relationStartDate != null">
                AND DATE(rr.create_time) &gt;= #{query.relationStartDate}
            </if>
            <if test="query.relationEndDate != null">
                AND DATE(rr.create_time) &lt;= #{query.relationEndDate}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                hi.hotel_supplier_code LIKE CONCAT('%', #{query.keyword}, '%')
                OR hi.hotel_name LIKE CONCAT('%', #{query.keyword}, '%')
                OR ui.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
                )
            </if>
        </where>
    </sql>

    <!-- 查询推荐方酒店关系列表 -->
    <select id="selectRecommenderHotelRelationList" resultMap="RecommenderHotelRelationVoMap">
        SELECT
        rr.id as relation_id,
        rr.recommender_id,
        hi.id as hotel_id,
        hi.hotel_name,
        hi.hotel_supplier_code,
        ui.mobile as supplier_mobile,
        ui.nick_name as supplier_nick_name,
        COALESCE(room_stats.online_room_count, 0) as online_room_count,
        rr.create_time as relation_create_time,
        COALESCE(order_stats.order_count, 0) as order_count,
        rr.relation_status,
        hi.hotel_status,
        hi.address as hotel_address,
        hi.star_rate,
        hi.score,
        hi.city_name,
        rr.biz_type
        FROM <include refid="TABLE_RECOMMENDER_RELATION"/> rr
        INNER JOIN <include refid="TABLE_HOTEL_INFO"/> hi ON rr.biz_id = hi.id
        LEFT JOIN <include refid="TABLE_HOTEL_USER_INFO"/> ui ON hi.hotel_supplier_code COLLATE utf8mb4_0900_ai_ci = ui.supplier_code COLLATE utf8mb4_0900_ai_ci
        LEFT JOIN (
        SELECT
        hotel_id,
        COUNT(*) as online_room_count
        FROM <include refid="TABLE_HOTEL_ROOMS"/>
        WHERE room_status = 3 AND del_flag = 0
        GROUP BY hotel_id
        ) room_stats ON hi.id = room_stats.hotel_id
        LEFT JOIN (
        SELECT
        mo.hotel_id,
        COUNT(*) as order_count
        FROM <include refid="TABLE_MANAGER_ORDER"/> mo
        INNER JOIN <include refid="TABLE_PAY_ORDER"/> po ON mo.order_no = po.order_no
        INNER JOIN <include refid="TABLE_SUPPLIER_OPERATION_ORDER_LOG"/> sol ON mo.id = sol.order_id
        WHERE mo.order_state = 'COMPLETED'
        AND sol.action_type = 1
        GROUP BY mo.hotel_id
        ) order_stats ON hi.id = order_stats.hotel_id
        <include refid="commonWhereCondition"/>
        <choose>
            <when test="query.sortType == 2">
                ORDER BY online_room_count DESC, rr.id ASC
            </when>
            <when test="query.sortType == 3">
                ORDER BY order_count DESC, rr.id ASC
            </when>
            <otherwise>
                ORDER BY rr.create_time DESC, rr.id ASC
            </otherwise>
        </choose>
        <if test="query.limit != null and query.offset != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询推荐方酒店关系总数 -->
    <select id="selectRecommenderHotelRelationCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM recommender_relation rr
        INNER JOIN somytrip_hotel_supplier.hotel_info hi ON rr.biz_id = hi.id
        LEFT JOIN <include refid="TABLE_HOTEL_USER_INFO"/> ui ON hi.hotel_supplier_code COLLATE utf8mb4_0900_ai_ci = ui.supplier_code COLLATE utf8mb4_0900_ai_ci
        <include refid="commonWhereCondition"/>
    </select>

    <!-- 查询推荐方酒店关系统计信息 -->
    <select id="selectRecommenderHotelRelationStatistics" resultMap="StatisticsVoMap">
        SELECT
        COUNT(*) as total_relation_count,
        COUNT(DISTINCT hi.id) as total_hotel_count,
        COALESCE(SUM(room_stats.online_room_count), 0) as total_online_room_count,
        COALESCE(SUM(order_stats.order_count), 0) as total_order_count,
        SUM(CASE WHEN rr.relation_status = 1 THEN 1 ELSE 0 END) as active_relation_count,
        SUM(CASE WHEN rr.relation_status = 2 THEN 1 ELSE 0 END) as suspend_relation_count,
        CASE WHEN COUNT(*) > 0 THEN COALESCE(SUM(room_stats.online_room_count), 0) / COUNT(*) ELSE 0 END as avg_room_count_per_relation,
        CASE WHEN COUNT(*) > 0 THEN COALESCE(SUM(order_stats.order_count), 0) / COUNT(*) ELSE 0 END as avg_order_count_per_relation
        FROM recommender_relation rr
        INNER JOIN somytrip_hotel_supplier.hotel_info hi ON rr.biz_id = hi.id
        LEFT JOIN <include refid="TABLE_HOTEL_USER_INFO"/> ui ON hi.hotel_supplier_code COLLATE utf8mb4_0900_ai_ci = ui.supplier_code COLLATE utf8mb4_0900_ai_ci
        LEFT JOIN (
        SELECT
        hotel_id,
        COUNT(*) as online_room_count
        FROM somytrip_hotel_supplier.hotel_rooms
        WHERE room_status = 3 AND del_flag = 0
        GROUP BY hotel_id
        ) room_stats ON hi.id = room_stats.hotel_id
        LEFT JOIN (
        SELECT
        mo.hotel_id,
        COUNT(*) as order_count
        FROM <include refid="TABLE_MANAGER_ORDER"/> mo
        INNER JOIN <include refid="TABLE_PAY_ORDER"/> po ON mo.order_no = po.order_no
        INNER JOIN <include refid="TABLE_SUPPLIER_OPERATION_ORDER_LOG"/>  sol ON mo.id = sol.order_id
        WHERE mo.order_state = 'COMPLETED'
        AND sol.action_type = 1
        GROUP BY mo.hotel_id
        ) order_stats ON hi.id = order_stats.hotel_id
        <include refid="commonWhereCondition"/>
    </select>



    <!-- 导游关系VO映射结果 -->
    <resultMap id="GuideRelationVoResultMap" type="tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationVo">
        <id column="relation_id" property="relationId" />
        <result column="recommender_id" property="recommenderId" />
        <result column="guide_id" property="guideId" />
        <result column="guide_name" property="guideName" />
        <result column="phone" property="phone" />
        <result column="production_line" property="productionLine" />
        <result column="service_score" property="serviceScore" />
        <result column="control_status" property="controlStatus" />
        <result column="service_count" property="serviceCount" />
        <result column="relation_create_time" property="relationCreateTime" />
        <result column="order_count" property="orderCount" />
        <result column="relation_status" property="relationStatus" />
        <result column="user_id" property="userId" />
        <result column="audit_status" property="auditStatus" />
        <result column="service_cities" property="serviceCities" />
        <result column="primary_service_city" property="primaryServiceCity" />
        <result column="language" property="language" />
        <result column="introduction" property="introduction" />
        <result column="rejection_count" property="rejectionCount" />
        <result column="reject_count_month" property="rejectCountMonth" />
    </resultMap>

    <!-- 统计信息VO映射结果 -->
    <resultMap id="StatisticsVoResultMap" type="tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationStatisticsVo">
        <result column="total_guide_count" property="totalGuideCount" />
        <result column="normal_guide_count" property="normalGuideCount" />
        <result column="controlled_guide_count" property="controlledGuideCount" />
        <result column="driver_count" property="driverCount" />
        <result column="guide_count" property="guideCount" />
        <result column="driver_guide_count" property="driverGuideCount" />
        <result column="total_service_count" property="totalServiceCount" />
        <result column="total_order_count" property="totalOrderCount" />
        <result column="average_service_score" property="averageServiceScore" />
        <result column="max_service_score" property="maxServiceScore" />
        <result column="min_service_score" property="minServiceScore" />
        <result column="monthly_new_guide_count" property="monthlyNewGuideCount" />
        <result column="weekly_new_guide_count" property="weeklyNewGuideCount" />
        <result column="daily_new_guide_count" property="dailyNewGuideCount" />
    </resultMap>

    <!-- 基础查询SQL片段 -->
    <sql id="BaseQuery">
        SELECT
        rr.id as relation_id,
        rr.recommender_id,
        rr.biz_id as guide_id,
        rr.user_id,
        rr.relation_status,
        rr.create_time as relation_create_time,
        tgs.nick_name as guide_name,
        ui.mobile as phone,
        tgs.production_line,
        tgs.service_score,
        tgs.control_status,
        tgs.audit_status,
        tgs.service_cities,
        tgs.primary_service_city,
        tgs.language,
        tgs.introduction,
        tgs.rejection_count,
        tgs.reject_count_month,
        tgc.name as guide_name_from_cert
        FROM <include refid="TABLE_RECOMMENDER_RELATION"/> rr
        INNER JOIN <include refid="TABLE_TOUR_GUIDE_SETTLEMENT"/> tgs ON rr.biz_id = tgs.id
        INNER JOIN <include refid="TABLE_HOTEL_USER_INFO"/> ui ON tgs.user_id = ui.id
        LEFT JOIN <include refid="TABLE_TOUR_GUIDE_CERTIFICATES"/> tgc ON tgs.user_id = tgc.user_id AND tgc.certificate_type = 1 AND tgc.is_del = 0
        WHERE rr.biz_type = 2
        AND rr.is_del = 0
        AND tgs.is_del = 0
    </sql>

    <!-- 查询条件SQL片段 -->
    <sql id="QueryConditions">
        <if test="query.recommenderId != null">
            AND rr.recommender_id = #{query.recommenderId}
        </if>
        <if test="query.hasProductionLineFilter()">
            AND tgs.production_line = #{query.productionLine}
        </if>
        <if test="query.isNormalStatusFilter()">
            AND tgs.control_status = 0
        </if>
        <if test="query.isControlledStatusFilter()">
            AND tgs.control_status IN (1, 2, 3)
        </if>
        <if test="query.hasDateFilter()">
            <if test="query.relationStartDate != null">
                AND DATE(rr.create_time) &gt;= #{query.relationStartDate}
            </if>
            <if test="query.relationEndDate != null">
                AND DATE(rr.create_time) &lt;= #{query.relationEndDate}
            </if>
        </if>
        <if test="query.hasKeyword()">
            AND (
            tgs.id = #{query.processedKeyword}
            OR tgc.name LIKE CONCAT('%', #{query.processedKeyword}, '%')
            OR tgs.nick_name LIKE CONCAT('%', #{query.processedKeyword}, '%')
            )
        </if>
    </sql>

    <!-- 查询推荐方导游关系列表 -->
    <select id="selectRecommenderGuideRelationList" resultMap="GuideRelationVoResultMap">
        <include refid="BaseQuery"/>
        <include refid="QueryConditions"/>
        <choose>
            <when test="query.sortType == 1">
                ORDER BY rr.create_time DESC
            </when>
            <when test="query.sortType == 2">
                ORDER BY gi.guide_name ASC
            </when>
            <otherwise>
                ORDER BY rr.create_time DESC
            </otherwise>
        </choose>
        <if test="query.offset != null and query.limit != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询推荐方导游关系总数 -->
    <select id="selectRecommenderGuideRelationCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT rr.id)
        FROM recommender_relation rr
        INNER JOIN <include refid="TABLE_TOUR_GUIDE_SETTLEMENT"/> tgs ON rr.biz_id = tgs.id
        INNER JOIN <include refid="TABLE_HOTEL_USER_INFO"/> ui ON tgs.user_id = ui.id
        LEFT JOIN <include refid="TABLE_TOUR_GUIDE_CERTIFICATES"/> tgc ON tgs.user_id = tgc.user_id AND tgc.certificate_type = 1 AND tgc.is_del = 0
        WHERE rr.biz_type = 2
        AND rr.is_del = 0
        AND tgs.is_del = 0
        <include refid="QueryConditions"/>
    </select>


    <!-- 查询推荐方导游关系统计信息 -->
    <select id="selectRecommenderGuideRelationStatistics" resultMap="StatisticsVoResultMap">
        SELECT
        COUNT(DISTINCT rr.id) as total_guide_count,
        COUNT(DISTINCT CASE WHEN tgs.control_status = 0 THEN rr.id END) as normal_guide_count,
        COUNT(DISTINCT CASE WHEN tgs.control_status IN (1, 2, 3) THEN rr.id END) as controlled_guide_count,
        COUNT(DISTINCT CASE WHEN tgs.production_line = 1 THEN rr.id END) as driver_count,
        COUNT(DISTINCT CASE WHEN tgs.production_line = 2 THEN rr.id END) as guide_count,
        COUNT(DISTINCT CASE WHEN tgs.production_line = 3 THEN rr.id END) as driver_guide_count,
        COALESCE(SUM(service_counts.service_count), 0) as total_service_count,
        COALESCE(SUM(order_counts.order_count), 0) as total_order_count,
        COALESCE(AVG(tgs.service_score), 0) as average_service_score,
        COALESCE(MAX(tgs.service_score), 0) as max_service_score,
        COALESCE(MIN(tgs.service_score), 0) as min_service_score,
        COUNT(DISTINCT CASE WHEN DATE(rr.create_time) >= DATE_SUB(CURDATE(), INTERVAL DAY(CURDATE())-1 DAY) THEN rr.id END) as monthly_new_guide_count,
        COUNT(DISTINCT CASE WHEN DATE(rr.create_time) >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) THEN rr.id END) as weekly_new_guide_count,
        COUNT(DISTINCT CASE WHEN DATE(rr.create_time) = CURDATE() THEN rr.id END) as daily_new_guide_count
        FROM recommender_relation rr
        INNER JOIN <include refid="TABLE_TOUR_GUIDE_SETTLEMENT"/> tgs ON rr.biz_id = tgs.id
        LEFT JOIN (
        SELECT tour_guide_id, COUNT(*) as service_count
        FROM <include refid="TABLE_TOUR_GUIDE_SERVICE_INFO"/>
        WHERE service_status = 1 AND is_del = 0
        GROUP BY tour_guide_id
        ) service_counts ON tgs.id = service_counts.tour_guide_id
        LEFT JOIN (
        SELECT tour_guide_id, COUNT(*) as order_count
        FROM <include refid="TABLE_TOUR_GUIDE_ORDER"/>
        WHERE settlement_status = 1 AND is_del = 0
        GROUP BY tour_guide_id
        ) order_counts ON tgs.id = order_counts.tour_guide_id
        WHERE rr.biz_type = 2
        AND rr.is_del = 0
        AND tgs.is_del = 0
        <include refid="QueryConditions"/>
    </select>


    <!-- 查询导游的上架服务数 -->
    <select id="selectServiceCountByGuideId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="TABLE_TOUR_GUIDE_SERVICE_INFO"/> tgsi
                 INNER JOIN <include refid="TABLE_TOUR_GUIDE_SETTLEMENT"/> tgs ON tgsi.tour_guide_id = tgs.id
        WHERE tgs.id = #{guideId}
          AND tgsi.service_status = 1
          AND tgsi.is_del = 0
          AND tgs.is_del = 0
    </select>


    <!-- 查询导游的成单数 -->
    <select id="selectOrderCountByGuideId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="TABLE_TOUR_GUIDE_ORDER"/> tgo
        INNER JOIN <include refid="TABLE_TOUR_GUIDE_SETTLEMENT"/> tgs ON tgo.tour_guide_id = tgs.id
        WHERE tgs.id = #{guideId}
        AND tgo.settlement_status = 1
        AND tgo.is_del = 0
        AND tgs.is_del = 0
        <if test="relationCreateTime != null">
            AND tgo.create_time >= #{relationCreateTime}
        </if>
    </select>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, recommender_id, biz_type, biz_id, user_id, relation_status, create_time, is_del
    </sql>

</mapper>
