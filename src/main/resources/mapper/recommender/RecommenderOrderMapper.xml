<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tripai.recommend.system.mapper.recommender.RecommenderOrderMapper">

    <!-- ================================ 数据库表名统一管理 ================================ -->

    <!-- 推荐系统数据库表 -->
    <sql id="TABLE_RECOMMENDER_RELATION">somytrip_recommend_system.recommender_relation</sql>

    <!-- 酒店供应商数据库表 -->
    <sql id="TABLE_HOTEL_INFO">somytrip_hotel_supplier.hotel_info</sql>
    <sql id="TABLE_HOTEL_USER_INFO">somytrip_hotel_supplier.user_info</sql>
    <sql id="TABLE_HOTEL_ROOMS">somytrip_hotel_supplier.hotel_rooms</sql>
    <sql id="TABLE_MANAGER_ORDER">somytrip_hotel_supplier.manager_order</sql>

    <!-- 导游系统数据库表 -->
    <sql id="TABLE_TOUR_GUIDE_SETTLEMENT">somytrip_hotel_supplier.tour_guide_settlement</sql>
    <sql id="TABLE_TOUR_GUIDE_CERTIFICATES">somytrip_hotel_supplier.tour_guide_certificates</sql>
    <sql id="TABLE_TOUR_GUIDE_SERVICE_INFO">somytrip_hotel_supplier.tour_guide_service_info</sql>
    <sql id="TABLE_TOUR_GUIDE_ORDER">somytrip_hotel_supplier.tour_guide_order</sql>

    <!-- 支付系统数据库表 -->
    <sql id="TABLE_PAY_ORDER">somytrip_order.pay_order</sql>

    <!-- 供应商操作日志表 -->
    <sql id="TABLE_SUPPLIER_OPERATION_ORDER_LOG">`oct_iri_db_tourism-ai`.supplier_operation_order_log</sql>

    <!-- 通用查询结果映射 -->
    <resultMap id="RecommenderOrderVoMap" type="tripai.recommend.system.domain.vo.recommender.RecommenderOrderVo">
        <result column="order_no" property="orderNo"/>
        <result column="order_type" property="orderType"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="commission_amount" property="commissionAmount"/>
        <result column="settlement_time" property="settlementTime"/>
        <result column="biz_id" property="bizId"/>
        <result column="recommender_id" property="recommenderId"/>
        <result column="relation_id" property="relationId"/>
    </resultMap>

    <!-- 通用查询条件 -->
    <sql id="commonWhereCondition">
        <where>
            rr.recommender_id = #{query.recommenderId}
            AND rr.relation_status = 1
            AND rr.is_del = 0
            <if test="query.settlementStartDate != null">
                AND DATE(sol.create_time) &gt;= #{query.settlementStartDate}
            </if>
            <if test="query.settlementEndDate != null">
                AND DATE(sol.create_time) &lt;= #{query.settlementEndDate}
            </if>
        </where>
    </sql>

    <!-- 酒店订单查询 -->
    <select id="selectHotelOrderList" resultMap="RecommenderOrderVoMap">
        SELECT
        mo.order_no,
        rr.biz_type as order_type,
        hi.hotel_name as supplier_name,
        po.amount as order_amount,
        sol.amount as commission_amount,
        sol.create_time as settlement_time,
        rr.biz_id,
        rr.recommender_id,
        rr.id as relation_id
        FROM recommender_relation rr
        INNER JOIN manager_order mo ON rr.biz_id = mo.hotel_id
        INNER JOIN pay_order po ON mo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON mo.id = sol.order_id
        INNER JOIN hotel_info hi ON mo.hotel_id = hi.id
        <include refid="commonWhereCondition"/>
        AND rr.biz_type = 1
        AND mo.order_state = 'COMPLETED'
        AND sol.action_type = 1
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            mo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR hi.hotel_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        ORDER BY sol.create_time DESC, mo.order_no ASC
        <if test="query.limit != null and query.offset != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 导游订单查询 -->
    <select id="selectTourGuideOrderList" resultMap="RecommenderOrderVoMap">
        SELECT
        tgo.order_no,
        rr.biz_type as order_type,
        tgs.nick_name as supplier_name,
        po.amount as order_amount,
        sol.amount as commission_amount,
        sol.create_time as settlement_time,
        rr.biz_id,
        rr.recommender_id,
        rr.id as relation_id
        FROM recommender_relation rr
        INNER JOIN tour_guide_order tgo ON rr.biz_id = tgo.tour_guide_id
        INNER JOIN pay_order po ON tgo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON tgo.id = sol.order_id
        INNER JOIN tour_guide_settlement tgs ON tgo.tour_guide_id = tgs.id
        <include refid="commonWhereCondition"/>
        AND rr.biz_type = 2
        AND tgo.order_status = 4
        AND tgo.settlement_status = 1
        AND sol.action_type = 1
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            tgo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR tgs.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        ORDER BY sol.create_time DESC, tgo.order_no ASC
        <if test="query.limit != null and query.offset != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询全部订单列表 -->
    <select id="selectRecommenderOrderList" resultMap="RecommenderOrderVoMap">
        (
        SELECT
        mo.order_no,
        rr.biz_type as order_type,
        hi.hotel_name as supplier_name,
        po.amount as order_amount,
        sol.amount as commission_amount,
        sol.create_time as settlement_time,
        rr.biz_id,
        rr.recommender_id,
        rr.id as relation_id
        FROM recommender_relation rr
        INNER JOIN manager_order mo ON rr.biz_id = mo.hotel_id
        INNER JOIN pay_order po ON mo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON mo.id = sol.order_id
        INNER JOIN hotel_info hi ON mo.hotel_id = hi.id
        WHERE rr.recommender_id = #{query.recommenderId}
        AND rr.relation_status = 1
        AND rr.is_del = 0
        AND rr.biz_type = 1
        AND mo.order_state = 'COMPLETED'
        AND sol.action_type = 1
        <if test="query.settlementStartDate != null">
            AND DATE(sol.create_time) &gt;= #{query.settlementStartDate}
        </if>
        <if test="query.settlementEndDate != null">
            AND DATE(sol.create_time) &lt;= #{query.settlementEndDate}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            mo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR hi.hotel_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        )
        UNION ALL
        (
        SELECT
        tgo.order_no,
        rr.biz_type as order_type,
        tgs.nick_name as supplier_name,
        po.amount as order_amount,
        sol.amount as commission_amount,
        sol.create_time as settlement_time,
        rr.biz_id,
        rr.recommender_id,
        rr.id as relation_id
        FROM recommender_relation rr
        INNER JOIN tour_guide_order tgo ON rr.biz_id = tgo.tour_guide_id
        INNER JOIN pay_order po ON tgo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON tgo.id = sol.order_id
        INNER JOIN tour_guide_settlement tgs ON tgo.tour_guide_id = tgs.id
        WHERE rr.recommender_id = #{query.recommenderId}
        AND rr.relation_status = 1
        AND rr.is_del = 0
        AND rr.biz_type = 2
        AND tgo.order_status = 4
        AND tgo.settlement_status = 1
        AND sol.action_type = 1
        <if test="query.settlementStartDate != null">
            AND DATE(sol.create_time) &gt;= #{query.settlementStartDate}
        </if>
        <if test="query.settlementEndDate != null">
            AND DATE(sol.create_time) &lt;= #{query.settlementEndDate}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            tgo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR tgs.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        )
        ORDER BY settlement_time DESC, order_no ASC
        <if test="query.limit != null and query.offset != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询酒店订单总数 -->
    <select id="selectHotelOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM recommender_relation rr
        INNER JOIN manager_order mo ON rr.biz_id = mo.hotel_id
        INNER JOIN pay_order po ON mo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON mo.id = sol.order_id
        INNER JOIN hotel_info hi ON mo.hotel_id = hi.id
        <include refid="commonWhereCondition"/>
        AND rr.biz_type = 1
        AND mo.order_state = 'COMPLETED'
        AND sol.action_type = 1
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            mo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR hi.hotel_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
    </select>

    <!-- 查询导游订单总数 -->
    <select id="selectTourGuideOrderCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM recommender_relation rr
        INNER JOIN tour_guide_order tgo ON rr.biz_id = tgo.tour_guide_id
        INNER JOIN pay_order po ON tgo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON tgo.id = sol.order_id
        INNER JOIN tour_guide_settlement tgs ON tgo.tour_guide_id = tgs.id
        <include refid="commonWhereCondition"/>
        AND rr.biz_type = 2
        AND tgo.order_status = 4
        AND tgo.settlement_status = 1
        AND sol.action_type = 1
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            tgo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR tgs.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
    </select>

    <!-- 查询全部订单总数 -->
    <select id="selectRecommenderOrderCount" resultType="java.lang.Long">
        SELECT
        (
        SELECT COUNT(*)
        FROM recommender_relation rr
        INNER JOIN manager_order mo ON rr.biz_id = mo.hotel_id
        INNER JOIN pay_order po ON mo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON mo.id = sol.order_id
        INNER JOIN hotel_info hi ON mo.hotel_id = hi.id
        WHERE rr.recommender_id = #{query.recommenderId}
        AND rr.relation_status = 1
        AND rr.is_del = 0
        AND rr.biz_type = 1
        AND mo.order_state = 'COMPLETED'
        AND sol.action_type = 1
        <if test="query.settlementStartDate != null">
            AND DATE(sol.create_time) &gt;= #{query.settlementStartDate}
        </if>
        <if test="query.settlementEndDate != null">
            AND DATE(sol.create_time) &lt;= #{query.settlementEndDate}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            mo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR hi.hotel_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        ) +
        (
        SELECT COUNT(*)
        FROM recommender_relation rr
        INNER JOIN tour_guide_order tgo ON rr.biz_id = tgo.tour_guide_id
        INNER JOIN pay_order po ON tgo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON tgo.id = sol.order_id
        INNER JOIN tour_guide_settlement tgs ON tgo.tour_guide_id = tgs.id
        WHERE rr.recommender_id = #{query.recommenderId}
        AND rr.relation_status = 1
        AND rr.is_del = 0
        AND rr.biz_type = 2
        AND tgo.order_status = 4
        AND tgo.settlement_status = 1
        AND sol.action_type = 1
        <if test="query.settlementStartDate != null">
            AND DATE(sol.create_time) &gt;= #{query.settlementStartDate}
        </if>
        <if test="query.settlementEndDate != null">
            AND DATE(sol.create_time) &lt;= #{query.settlementEndDate}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            tgo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR tgs.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        ) as total_count
    </select>

    <!-- 查询酒店订单分佣金额小计 -->
    <select id="selectHotelOrderCommissionSum" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(sol.amount), 0)
        FROM recommender_relation rr
        INNER JOIN manager_order mo ON rr.biz_id = mo.hotel_id
        INNER JOIN pay_order po ON mo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON mo.id = sol.order_id
        INNER JOIN hotel_info hi ON mo.hotel_id = hi.id
        <include refid="commonWhereCondition"/>
        AND rr.biz_type = 1
        AND mo.order_state = 'COMPLETED'
        AND sol.action_type = 1
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            mo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR hi.hotel_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
    </select>

    <!-- 查询导游订单分佣金额小计 -->
    <select id="selectTourGuideOrderCommissionSum" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(sol.amount), 0)
        FROM recommender_relation rr
        INNER JOIN tour_guide_order tgo ON rr.biz_id = tgo.tour_guide_id
        INNER JOIN pay_order po ON tgo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON tgo.id = sol.order_id
        INNER JOIN tour_guide_settlement tgs ON tgo.tour_guide_id = tgs.id
        <include refid="commonWhereCondition"/>
        AND rr.biz_type = 2
        AND tgo.order_status = 4
        AND tgo.settlement_status = 1
        AND sol.action_type = 1
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            tgo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR tgs.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
    </select>

    <!-- 查询全部订单分佣金额小计 -->
    <select id="selectRecommenderOrderCommissionSum" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(
        (
        SELECT SUM(sol.amount)
        FROM recommender_relation rr
        INNER JOIN manager_order mo ON rr.biz_id = mo.hotel_id
        INNER JOIN pay_order po ON mo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON mo.id = sol.order_id
        INNER JOIN hotel_info hi ON mo.hotel_id = hi.id
        WHERE rr.recommender_id = #{query.recommenderId}
        AND rr.relation_status = 1
        AND rr.is_del = 0
        AND rr.biz_type = 1
        AND mo.order_state = 'COMPLETED'
        AND sol.action_type = 1
        <if test="query.settlementStartDate != null">
            AND DATE(sol.create_time) &gt;= #{query.settlementStartDate}
        </if>
        <if test="query.settlementEndDate != null">
            AND DATE(sol.create_time) &lt;= #{query.settlementEndDate}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            mo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR hi.hotel_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        ), 0
        ) +
        COALESCE(
        (
        SELECT SUM(sol.amount)
        FROM recommender_relation rr
        INNER JOIN tour_guide_order tgo ON rr.biz_id = tgo.tour_guide_id
        INNER JOIN pay_order po ON tgo.order_no = po.order_no
        INNER JOIN supplier_operation_order_log sol ON tgo.id = sol.order_id
        INNER JOIN tour_guide_settlement tgs ON tgo.tour_guide_id = tgs.id
        WHERE rr.recommender_id = #{query.recommenderId}
        AND rr.relation_status = 1
        AND rr.is_del = 0
        AND rr.biz_type = 2
        AND tgo.order_status = 4
        AND tgo.settlement_status = 1
        AND sol.action_type = 1
        <if test="query.settlementStartDate != null">
            AND DATE(sol.create_time) &gt;= #{query.settlementStartDate}
        </if>
        <if test="query.settlementEndDate != null">
            AND DATE(sol.create_time) &lt;= #{query.settlementEndDate}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
            tgo.order_no LIKE CONCAT('%', #{query.keyword}, '%')
            OR tgs.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        ), 0
        ) as total_commission_amount
    </select>

</mapper>
