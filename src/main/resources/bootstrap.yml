server:
  port: 8080
  servlet:
    context-path: /
spring:
  application:
    name: recommend-system
  profiles:
    active: &profileActive '@profiles.active@'
  cloud:
    nacos:
      config:
        server-addr: '@nacos.server.addr@'
        file-extension: yaml
        username: dev
        password: '@Dev@2023715'
        namespace: *profileActive
  servlet:
    multipart:
      # 默认最大上传文件大小为1M，单个文件大小
      max-file-size: 3MB
      # 默认最大请求大小为10M，总上传的数据大小
      max-request-size: 50MB

# 腾讯云配置
tencent:
  cloud:
    ocr:
      secret-id: AKIDFI6QaUAcqocFdmfHdV7UfaEckcDXbOvC
      secret-key: 3qNjocATVGkxta3GDuKUGp9CyThfNnwp
      region: ap-guangzhou
      endpoint: ocr.tencentcloudapi.com
    # 新增银行卡核验配置
    bank:
      secret-id: AKIDY1TsCUbVJwb7TflldO1VnslW5RbYHm5B
      secret-key: vfCYuOvMelgcicIl4D9rPlx0uc7PUM0R
      region: ap-guangzhou
      endpoint: faceid.tencentcloudapi.com