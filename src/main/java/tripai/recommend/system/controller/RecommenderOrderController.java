package tripai.recommend.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderOrderQueryDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderListVo;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.service.RecommenderOrderService;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.controller
 * @className: RecommenderOrderController
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/24 18:16
 * @version: 1.0
 */

@RestController
@RequestMapping("/recommender/order")
@Slf4j
public class RecommenderOrderController {

    @Resource
    private RecommenderOrderService recommenderOrderService;

    /**
     * 查询推荐方订单列表
     *
     * @param queryDto 查询条件
     * @param token    用户token
     * @return 订单列表
     */
    @PostMapping("/list")
    public ResponseResult<RecommenderOrderListVo> getOrderList(
            RecommenderOrderQueryDto queryDto,
            @RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token) {

        log.info("查询推荐方订单列表，查询条件: {}", JSON.toJSONString(queryDto));

        // 从token中获取用户信息
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        if (userInfoVo == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        Long userId = userInfoVo.getId();
        if (userId == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        // 根据用户ID查询订单列表
        return recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
    }
}
