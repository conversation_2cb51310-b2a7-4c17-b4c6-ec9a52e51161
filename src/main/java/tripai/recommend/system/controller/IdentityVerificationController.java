package tripai.recommend.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.vo.CertificateVo;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.service.CertificateOcrService;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.controller
 * @className: IdentityVerificationController
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 11:47
 * @version: 1.0
 */

@Slf4j
@RestController
@RequestMapping("/api/certificate")
public class IdentityVerificationController {

    @Resource
    private CertificateOcrService certificateOcrService;


    /**
     * OCR证件信息
     *
     * @param token 用户token
     * @param file  证件图片文件
     * @param type  证件类型：1-身份证，2-护照，4-驾驶证
     * @return 证件信息
     */
    @PostMapping("/ocr/recognize")
    public ResponseResult<CertificateVo> recognizeCertificate(@RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token,
                                                              @RequestParam("file") MultipartFile file,
                                                              @RequestParam("type") Integer type) {
        try {
            // 从token中获取用户信息
            UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
            if (userInfoVo == null) {
                return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
            }
            Long userId = userInfoVo.getId();
            if (userId == null) {
                return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
            }
            // 调用Service层的带频率限制的方法
            CertificateVo result = certificateOcrService.recognizeCertificateWithRateLimit(userId, file, type);
            return ResponseResult.ok(result);
        } catch (Exception e) {
            // 处理认证异常
            log.error("证件识别失败，类型: {}", type, e);
            ;
            return ResponseResult.fail("证件识别失败: " + e.getMessage());
        }
    }
}

