package tripai.recommend.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.vo.CertificateVo;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.service.CertificateOcrService;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.controller
 * @className: IdentityVerificationController
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/7/30 11:47
 * @version: 1.0
 */

@Slf4j
@RestController
@RequestMapping("/api/certificate")
public class IdentityVerificationController {

    /**
     * OCR证件信息redis键前缀 - 分钟级限制
     */
    private final String CERTIFICATE_MINUTE_REDISKEY = "certificate:minute:";

    /**
     * OCR证件信息redis键前缀 - 天级限制
     */
    private final String CERTIFICATE_DAY_REDISKEY = "certificate:day:";

    /**
     * 同一用户1分钟内最多请求15次
     */
    private final int MAX_MINUTE_LIMIT = 15;

    /**
     * 同一用户1天内最多请求30次
     */
    private final int MAX_DAY_LIMIT = 30;

    /**
     * 分钟级redis过期时间（秒）
     */
    private final int MINUTE_EXPIRE_TIME = 60;

    /**
     * 天级redis过期时间（秒）
     */
    private final int DAY_EXPIRE_TIME = 24 * 60 * 60;

    @Resource
    private CertificateOcrService certificateOcrService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private HttpServletRequest request;


    /**
     * OCR证件信息
     *
     * @param token 用户token
     * @param file  证件图片文件
     * @param type  证件类型：1-身份证，2-护照，4-驾驶证
     * @return 证件信息
     */
    @PostMapping("/ocr/recognize")
    public ResponseResult<?> recognizeCertificate(@RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token,
                                                  @RequestParam("file") MultipartFile file,
                                                  @RequestParam("type") Integer type) {
        // 从token中获取用户信息
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        if (userInfoVo == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        Long userId = userInfoVo.getId();
        if (userId == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        // 根据证件类型确定Redis键
        String minuteRedisKey = CERTIFICATE_MINUTE_REDISKEY + type + ":" + userId;
        String dayRedisKey = CERTIFICATE_DAY_REDISKEY + type + ":" + userId + ":" +
                            java.time.LocalDate.now().toString();

        // 防刷限制：双重限制检查
        Long minuteCount = null;
        Long dayCount = null;

        try {
            // 1. 检查分钟级限制
            minuteCount = stringRedisTemplate.opsForValue().increment(minuteRedisKey, 1);
            // 如果minuteCount为null，设置默认值为1
            if (minuteCount == null) {
                minuteCount = 1L;
                stringRedisTemplate.opsForValue().set(minuteRedisKey, String.valueOf(minuteCount),
                                                    MINUTE_EXPIRE_TIME, TimeUnit.SECONDS);
                log.warn("Redis increment返回null（分钟级），已设置默认值为1，用户ID: {}", userId);
            } else if (minuteCount == 1) {
                // 设置过期时间
                stringRedisTemplate.expire(minuteRedisKey, MINUTE_EXPIRE_TIME, TimeUnit.SECONDS);
            }

            // 2. 检查天级限制
            dayCount = stringRedisTemplate.opsForValue().increment(dayRedisKey, 1);
            // 如果dayCount为null，设置默认值为1
            if (dayCount == null) {
                dayCount = 1L;
                stringRedisTemplate.opsForValue().set(dayRedisKey, String.valueOf(dayCount),
                                                    DAY_EXPIRE_TIME, TimeUnit.SECONDS);
                log.warn("Redis increment返回null（天级），已设置默认值为1，用户ID: {}", userId);
            } else if (dayCount == 1) {
                // 设置过期时间
                stringRedisTemplate.expire(dayRedisKey, DAY_EXPIRE_TIME, TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            // Redis操作异常，记录日志但允许请求继续
            log.info("Redis操作异常，无法进行防刷限制，用户ID: {}", userId);
            minuteCount = 1L;
            dayCount = 1L;
        }

        // 检查分钟级限制
        if (minuteCount > MAX_MINUTE_LIMIT) {
            // 获取分钟级Redis键的剩余过期时间（秒）
            Long expireTime = stringRedisTemplate.getExpire(minuteRedisKey, TimeUnit.SECONDS);
            // 如果无法获取过期时间或已过期，默认为60秒
            if (expireTime == null || expireTime <= 0) {
                expireTime = (long) MINUTE_EXPIRE_TIME;
            }
            return ResponseResult.fail(String.format("请求过于频繁，1分钟内最多请求%d次，请%d秒后再试",
                                                    MAX_MINUTE_LIMIT, expireTime));
        }

        // 检查天级限制
        if (dayCount > MAX_DAY_LIMIT) {
            // 获取天级Redis键的剩余过期时间（秒）
            Long expireTime = stringRedisTemplate.getExpire(dayRedisKey, TimeUnit.SECONDS);
            // 如果无法获取过期时间或已过期，默认为24小时
            if (expireTime == null || expireTime <= 0) {
                expireTime = (long) DAY_EXPIRE_TIME;
            }
            // 将秒转换为小时显示
            long expireHours = expireTime / 3600;
            long expireMinutes = (expireTime % 3600) / 60;
            String timeMessage = expireHours > 0 ?
                String.format("%d小时%d分钟", expireHours, expireMinutes) :
                String.format("%d分钟", expireMinutes);
            return ResponseResult.fail(String.format("今日请求次数已达上限（%d次），请%s后再试",
                                                    MAX_DAY_LIMIT, timeMessage));
        }

        try {
            CertificateVo certificateVo = certificateOcrService.recognizeCertificate(type, file);
            if (certificateVo != null) {
                return ResponseResult.ok(certificateVo);
            } else {
                return ResponseResult.fail("证件识别失败");
            }
        } catch (Exception e) {
            log.error("证件识别失败，类型: {}", type, e);
            return ResponseResult.fail("证件识别失败: " + e.getMessage());
        }
    }
}

