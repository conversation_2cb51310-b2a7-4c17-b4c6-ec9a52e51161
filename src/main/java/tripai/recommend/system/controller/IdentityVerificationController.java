package tripai.recommend.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.vo.CertificateVo;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.service.CertificateOcrService;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.controller
 * @className: IdentityVerificationController
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/7/30 11:47
 * @version: 1.0
 */

@Slf4j
@RestController
@RequestMapping("/api/certificate")
public class IdentityVerificationController {

    /**
     * OCR证件信息redis键前缀
     */
    private final String CERTIFICATE_REDISKEY = "certificate:";

    /**
     * 同一用户1分钟内最多请求3次
     */
    private final int MAX_LIMIT = 3;

    /**
     * redis过期时间（秒）
     */
    private final int EXPIRE_TIME = 60;

    @Resource
    private CertificateOcrService certificateOcrService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private HttpServletRequest request;


    /**
     * OCR证件信息
     *
     * @param token 用户token
     * @param file  证件图片文件
     * @param type  证件类型：1-身份证，2-护照，4-驾驶证
     * @return 证件信息
     */
    @PostMapping("/ocr/recognize")
    public ResponseResult<?> recognizeCertificate(@RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token,
                                                  @RequestParam("file") MultipartFile file,
                                                  @RequestParam("type") Integer type) {
        // 从token中获取用户信息
        UserInfoVo userInfoVo = com.alibaba.fastjson2.JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        if (userInfoVo == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        Long userId = userInfoVo.getId();
        if (userId == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        // 根据证件类型确定Redis键
        String redisKey = CERTIFICATE_REDISKEY + type + ":" + userId;

        // 防刷限制：同一用户1分钟内最多请求3次
        Long count = null;
        try {
            count = stringRedisTemplate.opsForValue().increment(redisKey, 1);
            // 如果count为null，设置默认值为1
            if (count == null) {
                count = 1L;
                stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(count), EXPIRE_TIME, TimeUnit.SECONDS);
                log.warn("Redis increment返回null，已设置默认值为1，用户ID: {}", userId);
            } else if (count == 1) {
                // 设置过期时间
                stringRedisTemplate.expire(redisKey, EXPIRE_TIME, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            // Redis操作异常，记录日志但允许请求继续
            log.info("Redis操作异常，无法进行防刷限制，用户ID: {}", userId);
            count = 1L;
        }

        if (count > MAX_LIMIT) {
            // 获取Redis键的剩余过期时间（秒）
            Long expireTime = stringRedisTemplate.getExpire(redisKey, TimeUnit.SECONDS);
            // 如果无法获取过期时间或已过期，默认为60秒
            if (expireTime == null || expireTime <= 0) {
                expireTime = (long) EXPIRE_TIME;
            }
            return ResponseResult.fail(String.format("请求过于频繁，请%d秒后再试", expireTime));
        }

        try {
            CertificateVo certificateVo = certificateOcrService.recognizeCertificate(type, file);
            if (certificateVo != null) {
                return ResponseResult.ok(certificateVo);
            } else {
                return ResponseResult.fail("证件识别失败");
            }
        } catch (Exception e) {
            log.error("证件识别失败，类型: {}", type, e);
            return ResponseResult.fail("证件识别失败: " + e.getMessage());
        }
    }
}

