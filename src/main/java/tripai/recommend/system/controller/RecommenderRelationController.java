package tripai.recommend.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderGuideRelationQueryDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderHotelRelationQueryDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationListVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationListVo;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.service.RecommenderRelationService;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.controller
 * @className: RecommenderRelationController
 * @author: lijunqi
 * @description:
 * @date: 2025/7/25 14:38
 * @version: 1.0
 */

@RestController
@RequestMapping("/recommender/relation")
@Slf4j
public class RecommenderRelationController {

    @Resource
    private RecommenderRelationService recommenderRelationService;

    /**
     * 查询推荐方酒店关系列表
     *
     * @param queryDto 查询条件
     * @param token    用户token
     * @return 推荐方酒店关系列表
     */
    @PostMapping("/hotel/list")
    public ResponseResult<RecommenderHotelRelationListVo> getRecommenderHotelRelationList(@RequestBody RecommenderHotelRelationQueryDto queryDto,
                                                                                          @RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token) {

        log.info("查询推荐方酒店关系列表，查询条件: {}", JSON.toJSONString(queryDto));

        // 从token中获取用户信息
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        if (userInfoVo == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        Long userId = userInfoVo.getId();
        if (userId == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        queryDto.setUserId(userId);
        return recommenderRelationService.getRecommenderHotelRelationList(queryDto);
    }

    /**
     * 查询推荐方导游供应商列表
     *
     * @param queryDto 查询条件
     * @param token    用户token
     * @return 推荐方导游供应商列表
     */
    @PostMapping("/guide/list")
    public ResponseResult<RecommenderGuideRelationListVo> getRecommenderGuideSupplierList(@RequestBody RecommenderGuideRelationQueryDto queryDto,
                                                                                          @RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token) {

        log.info("查询推荐方导游供应商列表，查询条件: {}", JSON.toJSONString(queryDto));

        // 从token中获取用户信息
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        if (userInfoVo == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        Long userId = userInfoVo.getId();
        if (userId == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        queryDto.setUserId(userId);
        return recommenderRelationService.getRecommenderGuideRelationList(queryDto);
    }
}

