package tripai.recommend.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.BankCardVerifyDto;
import tripai.recommend.system.domain.vo.BankCardVerifyResultVo;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.service.TencentBankCardVerifyService;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.controller
 * @className: BankCardVerifyController
 * @author: lijunqi
 * @description: 银行卡核验控制器
 * @date: 2025/7/30 11:36
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/bank-card")
@Slf4j
public class BankCardVerifyController {

    @Resource
    private TencentBankCardVerifyService tencentBankCardVerifyService;

    /**
     * 银行卡三要素核验
     *
     * @param token     用户token
     * @param verifyDto 核验请求参数
     * @return 核验结果
     */
    @PostMapping("/verify")
    public ResponseResult<BankCardVerifyResultVo> verifyBankCard(@RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token,
                                                                 @RequestBody @Valid BankCardVerifyDto verifyDto) {
        try {
            // 从token中获取用户信息
            UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
            if (userInfoVo == null) {
                return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
            }
            Long userId = userInfoVo.getId();
            if (userId == null) {
                return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
            }
            // 调用Service层的带频率限制的方法
            BankCardVerifyResultVo result = tencentBankCardVerifyService.verifyBankCardWithRateLimit(userId, verifyDto);
            return ResponseResult.ok(result);
        } catch (Exception e) {
            log.error("银行卡验证失败", e);
            return ResponseResult.fail("银行卡验证失败: " + e.getMessage());
        }
    }
}