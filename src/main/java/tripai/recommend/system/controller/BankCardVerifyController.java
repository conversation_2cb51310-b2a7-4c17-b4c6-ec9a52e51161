package tripai.recommend.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.BankCardVerifyDto;
import tripai.recommend.system.domain.vo.BankCardVerifyResultVo;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.service.TencentBankCardVerifyService;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.controller
 * @className: BankCardVerifyController
 * @author: lijun<PERSON>
 * @description: 银行卡核验控制器
 * @date: 2025/7/30 11:36
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/bank-card")
@Slf4j
public class BankCardVerifyController {

    @Resource
    private TencentBankCardVerifyService tencentBankCardVerifyService;

    /**
     * 银行卡三要素核验
     *
     * @param verifyDto 核验请求参数
     * @return 核验结果
     */
    @PostMapping("/verify")
    public ResponseResult<BankCardVerifyResultVo> verifyBankCard(@RequestBody @Valid BankCardVerifyDto verifyDto) {
        BankCardVerifyResultVo result = tencentBankCardVerifyService.verifyBankCard(verifyDto);
        return ResponseResult.ok(result);
    }
}