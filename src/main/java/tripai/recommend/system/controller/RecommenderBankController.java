package tripai.recommend.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankAccountDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderBankAccountVo;
import tripai.recommend.system.domain.vo.user.UserInfoVo;
import tripai.recommend.system.service.RecommenderBankService;
import tripai.recommend.system.util.JwtUtils;

import java.nio.charset.StandardCharsets;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.controller
 * @className: RecommenderBankController
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/24 10:29
 * @version: 1.0
 */

@RestController
@RequestMapping("/recommender/bank")
@Slf4j
public class RecommenderBankController {

    @Resource
    private RecommenderBankService recommenderBankService;

    /**
     * 获取推荐方银行账户信息
     *
     * @param token 用户token
     * @return 银行账户信息
     */
    @GetMapping
    public ResponseResult<RecommenderBankAccountVo> getBankAccount(@RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token) {
        log.info("获取推荐方银行账户信息，token: {}", token);
        // 从token中获取用户信息
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        if (userInfoVo == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        Long userId = userInfoVo.getId();
        if (userId == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        return recommenderBankService.getBankAccount(userId);
    }

    /**
     * 保存或更新银行账户信息
     *
     * @param dto   银行账户信息
     * @param token 用户token
     * @return 保存结果
     */
    @PostMapping
    public ResponseResult<Long> saveBankAccount(@Valid @RequestBody RecommenderBankAccountDto dto,
                                                   @RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token) {
        log.info("保存银行账户信息，token: {}", token);
        // 从token中获取用户信息
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        if (userInfoVo == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        Long userId = userInfoVo.getId();
        if (userId == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        dto.setUserId(userId);
        return recommenderBankService.saveBankAccount(dto);
    }

    /**
     * 删除银行账户信息
     *
     * @param bankAccountId 银行账户ID
     * @param token         用户token
     * @return 删除结果
     */
    @GetMapping("/{bankAccountId}")
    public ResponseResult<Boolean> deleteBankAccount(@PathVariable(value = "bankAccountId") Long bankAccountId,
                                                     @RequestHeader(value = HttpHeadConstant.RECOMMENDER_TOKEN) String token) {
        log.info("删除银行账户信息，bankAccountId: {}, token: {}", bankAccountId, token);
        // 从token中获取用户信息
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        if (userInfoVo == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        Long userId = userInfoVo.getId();
        if (userId == null) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }
        return recommenderBankService.deleteBankAccount(bankAccountId, userId);
    }


}

