package tripai.recommend.system.service;

import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderOrderQueryDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderListVo;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service
 * @className: RecommenderOrderService
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/7/24 18:17
 * @version: 1.0
 */
public interface RecommenderOrderService {

    
    /**
     * 根据用户ID查询推荐方订单列表
     *
     * @param userId   用户ID
     * @param queryDto 查询条件
     * @return 订单列表响应
     */
    ResponseResult<RecommenderOrderListVo> getRecommenderOrderListByUserId(Long userId, RecommenderOrderQueryDto queryDto);
}
