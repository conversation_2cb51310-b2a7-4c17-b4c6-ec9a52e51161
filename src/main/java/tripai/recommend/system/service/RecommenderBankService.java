package tripai.recommend.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankAccountDto;
import tripai.recommend.system.domain.entity.RecommenderBank;
import tripai.recommend.system.domain.vo.recommender.RecommenderBankAccountVo;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service
 * @className: RecommenderBankService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/24 10:41
 * @version: 1.0
 */
public interface RecommenderBankService extends IService<RecommenderBank> {

    /**
     * 获取推荐方银行账户信息
     *
     * @param userId 用户id
     * @return 银行账户信息
     */
    ResponseResult<RecommenderBankAccountVo> getBankAccount(Long userId);

    /**
     * 保存或更新银行账户信息
     *
     * @param dto   银行账户信息
     * @return 保存结果
     */
    ResponseResult<Long> saveBankAccount(RecommenderBankAccountDto dto);

    /**
     * 删除银行账户信息
     *
     * @param bankAccountId 银行账户ID
     * @param userId         用户userId
     * @return 删除结果
     */
    ResponseResult<Boolean> deleteBankAccount(Long bankAccountId, Long userId);


}
