package tripai.recommend.system.service;

import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderGuideRelationQueryDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderHotelRelationQueryDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationListVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationListVo;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service
 * @className: RecommenderRelationService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/25 16:58
 * @version: 1.0
 */
public interface RecommenderRelationService {

    /**
     * 查询推荐方酒店关系列表
     *
     * @param queryDto 查询条件
     * @return 推荐方酒店关系列表响应
     */
    ResponseResult<RecommenderHotelRelationListVo> getRecommenderHotelRelationList(RecommenderHotelRelationQueryDto queryDto);

    /**
     * 查询推荐方导游关系列表
     *
     * @param queryDto 查询条件
     * @return 推荐方导游关系列表响应
     */
    ResponseResult<RecommenderGuideRelationListVo> getRecommenderGuideRelationList(RecommenderGuideRelationQueryDto queryDto);

}

