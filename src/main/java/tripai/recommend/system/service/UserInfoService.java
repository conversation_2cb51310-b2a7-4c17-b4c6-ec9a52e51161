package tripai.recommend.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.zhyd.oauth.model.AuthUser;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.entity.UserInfo;
import tripai.recommend.system.domain.vo.user.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service
 * @className: UserInfoService
 * @author: lijun<PERSON>
 * @description: 用户信息service
 * @date: 2025/7/22 15:08
 * @version: 1.0
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 发送绑定邮箱验证码
     *
     * @param email to email address
     * @return true false
     */
    ResponseResult<Boolean> sendBoundEmailCode(String email);

    /**
     * 修改绑定邮箱
     *
     * @param vo    参数
     * @param token 用户token
     * @return true false
     */
    ResponseResult<Boolean> editEmail(EditUserEmailVo vo, String token);

    /**
     * 发送登陆注册短信
     *
     * @param vo@return json
     */
    ResponseResult<Boolean> sendLoginSms(CaptchaCodeVo vo);

    /**
     * 发送重置密码短信
     *
     * @param vo 参数
     * @return true false
     */
    ResponseResult<Boolean> sendResetPasswordSms(CaptchaCodeVo vo);

    /**
     * 渠道登陆
     *
     * @param authResponse 渠道登陆参数
     * @return json
     */
    ResponseResult<LoginResultVo> login(AuthUser authResponse);

    /**
     * 用户绑定渠道
     *
     * @param authUser 用户绑定渠道参数
     * @param token    用户Token
     * @return true false
     */
    ResponseResult<Boolean> boundUser(AuthUser authUser, String token);

    /**
     * 手机号登陆
     *
     * @param vo 登陆参数
     * @return json
     */
    ResponseResult<LoginResultVo> login(MobileLoginVo vo);

    /**
     * 扫码注册
     *
     * @param vo 扫码注册参数
     * @return json
     */
    ResponseResult<LoginResultVo> register(ScanQrRegisterVo vo);

    /**
     * 用户名密码登陆
     *
     * @param vo 登陆参数
     * @return json
     */
    ResponseResult<LoginResultVo> login(UsernameLoginVo vo);

    /**
     * 手机号注册
     *
     * @param vo 注册参数
     * @return json
     */
    ResponseResult<LoginResultVo> register(MobileRegisterVo vo);

    /**
     * 重设密码
     *
     * @param vo 重设密码参数
     * @return true false
     */
    ResponseResult<Boolean> resetPassword(MobileRegisterVo vo);

    /**
     * 修改昵称
     *
     * @param name  新昵称
     * @param token token
     * @return true false
     */
    ResponseResult<Boolean> editNickname(String name, String token);

    /**
     * 修改手机号
     *
     * @param vo    修改手机号参数
     * @param token 用户参数
     * @return true false
     */
    ResponseResult<Boolean> editMobile(MobileLoginVo vo, String token);

    /**
     * 修改头像
     *
     * @param vo    参数
     * @param token 用户参数
     * @return url
     */
    ResponseResult<String> editHeadImage(EditUserHeadImageVo vo, String token);

    /**
     * 解绑
     *
     * @param token 用户参数
     * @param op    0 邮箱 1 微信渠道
     * @return true false
     */
    ResponseResult<Boolean> unbindEmailOrQrChannel(String token, int op);

    /**
     * 手机号AES加密
     *
     * @param mobileInfo 手机号信息
     * @return
     */
    String mobileAesEncryptPublic(MobileInfo mobileInfo);

    /**
     * 手机号AES解密
     *
     * @param phone 手机号信息
     * @return
     */
    MobileInfo mobileAesDecryptPublic(String phone);

    /**
     * 获取用户个人中心信息
     *
     * @param userId 用户token
     * @return 个人中心信息
     */
    ResponseResult<UserProfileVo> getUserProfile(Long userId);


}