package tripai.recommend.system.service.impl;

import cn.hutool.core.util.CreditCodeUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankInfoDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderReqDto;
import tripai.recommend.system.domain.entity.RecommenderAuditRecord;
import tripai.recommend.system.domain.entity.RecommenderBank;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.enums.CertificateTypeEnum;
import tripai.recommend.system.domain.vo.recommender.RecommenderAuditDetailVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderProfileVo;
import tripai.recommend.system.exception.BusinessException;
import tripai.recommend.system.mapper.recommender.RecommenderAuditRecordMapper;
import tripai.recommend.system.mapper.recommender.RecommenderBankMapper;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.RecommenderService;

import java.time.LocalDateTime;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: RecommenderServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/7/23 14:19
 * @version: 1.0
 */

@Slf4j
@Service
public class RecommenderServiceImpl extends ServiceImpl<RecommenderMapper, RecommenderInfo> implements RecommenderService {


    @Resource
    private RecommenderMapper recommenderInfoMapper;
    @Resource
    private RecommenderBankMapper recommenderBankMapper;

    @Resource
    private RecommenderAuditRecordMapper recommenderAuditRecordMapper;

    /**
     * 保存/更新 推荐方认证资料
     *
     * @param dto 参数
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<RecommenderProfileVo> saveProfile(RecommenderReqDto dto) {
        log.info("保存推荐方认证资料开始，参数：{}", JSON.toJSONString(dto));

        try {
            if (dto == null) {
                log.error("保存推荐方认证资料失败，参数为空");
                return ResponseResult.fail("参数为空");
            }
            // 1. 验证请求参数
            if (!dto.getIsDraft()) {
                validateRequest(dto);
            }

            // 2. 查询推荐方信息
            LambdaQueryWrapper<RecommenderInfo> queryWrapper = new LambdaQueryWrapper<>();

            queryWrapper.eq(RecommenderInfo::getUserId, dto.getUserId());
            RecommenderInfo recommenderInfo = recommenderInfoMapper.selectOne(queryWrapper, false);

            // 3. 如果recommenderInfo不存在，则创建
            if (recommenderInfo == null) {
                // 检查身份证号/统一社会信用代码是否已存在
                LambdaQueryWrapper<RecommenderInfo> identifierWrapper = new LambdaQueryWrapper<>();
                identifierWrapper.eq(RecommenderInfo::getIdentifier, dto.getIdentifier());
                RecommenderInfo existingInfo = recommenderInfoMapper.selectOne(identifierWrapper, false);
                if (existingInfo != null) {
                    log.error("保存推荐方认证资料失败，身份证号/统一社会信用代码已存在");
                    return ResponseResult.fail("该身份证号/统一社会信用代码已被使用");
                }

                recommenderInfo = new RecommenderInfo();
                recommenderInfo.setUserId(dto.getUserId());
                // 待审核
                recommenderInfo.setStatus(0);
            }

            // 4. 保存推荐方基本信息
            Boolean saveRecommenderInfo = saveRecommenderInfo(recommenderInfo, dto);
            if (!saveRecommenderInfo) {
                log.error("保存推荐方认证资料失败，保存基本信息失败");
                return ResponseResult.fail("保存推荐方认证资料失败");
            }

            // 4. 保存银行信息
            Boolean saveBankInfo = saveBankInfo(recommenderInfo.getId(), dto.getBankInfo(), dto.getIsDraft());
            if (!saveBankInfo) {
                log.error("保存推荐方认证资料失败，保存银行信息失败");
                return ResponseResult.fail("保存推荐方银行信息资料失败");
            }

            RecommenderProfileVo vo = new RecommenderProfileVo();
            vo.setRecommenderId(recommenderInfo.getId());

            log.info("保存推荐方认证资料成功，推荐方ID：{}", recommenderInfo.getId());

            return ResponseResult.ok(vo);

        } catch (Exception e) {
            log.error("保存推荐方认证资料失败，参数：{}", JSON.toJSONString(dto), e);
            return ResponseResult.fail("保存推荐方认证资料失败");

        }
    }

    /**
     * 根据推荐方ID查询审核详情
     *
     * @param recommenderId 推荐方ID
     * @return 审核详情
     */
    @Override
    public ResponseResult<RecommenderAuditDetailVo> getAuditDetail(Long recommenderId) {
        log.info("查询推荐方审核详情开始，推荐方ID：{}", recommenderId);

        try {
            if (recommenderId == null) {
                log.error("查询推荐方审核详情失败，推荐方ID为空");
                return ResponseResult.fail("推荐方ID不能为空");
            }

            // 1. 查询推荐方基本信息
            RecommenderInfo recommenderInfo = recommenderInfoMapper.selectById(recommenderId);
            if (recommenderInfo == null) {
                log.error("查询推荐方审核详情失败，推荐方不存在，ID：{}", recommenderId);
                return ResponseResult.fail("推荐方不存在");
            }

            // 2. 查询审核记录
            LambdaQueryWrapper<RecommenderAuditRecord> auditWrapper = new LambdaQueryWrapper<>();
            auditWrapper.eq(RecommenderAuditRecord::getRecommenderId, recommenderId)
                    .eq(RecommenderAuditRecord::getIsDel, 0)
                    .orderByDesc(RecommenderAuditRecord::getCreateTime)
                    .last("LIMIT 1");
            RecommenderAuditRecord auditRecord = recommenderAuditRecordMapper.selectOne(auditWrapper, false);

            // 3. 查询银行信息
            LambdaQueryWrapper<RecommenderBank> bankWrapper = new LambdaQueryWrapper<>();
            bankWrapper.eq(RecommenderBank::getRecommenderId, recommenderId);
            RecommenderBank bankInfo = recommenderBankMapper.selectOne(bankWrapper, false);

            // 4. 构建返回结果
            RecommenderAuditDetailVo result = buildAuditDetailVo(recommenderInfo, auditRecord, bankInfo);

            log.info("查询推荐方审核详情成功，推荐方ID：{}", recommenderId);
            return ResponseResult.ok(result);

        } catch (Exception e) {
            log.error("查询推荐方审核详情失败，推荐方ID：{}", recommenderId, e);
            return ResponseResult.fail("查询推荐方审核详情失败");
        }
    }

    /**
     * 构建审核详情VO
     *
     * @param recommenderInfo 推荐方信息
     * @param auditRecord     审核记录
     * @param bankInfo        银行信息
     * @return 审核详情VO
     */
    private RecommenderAuditDetailVo buildAuditDetailVo(RecommenderInfo recommenderInfo,
                                                        RecommenderAuditRecord auditRecord,
                                                        RecommenderBank bankInfo) {
        RecommenderAuditDetailVo result = new RecommenderAuditDetailVo();

        // 设置基本信息
        result.setRecommenderId(recommenderInfo.getId());
        result.setType(recommenderInfo.getType());

        // 设置审核信息
        if (auditRecord != null) {
            result.setAuditStatus(auditRecord.getAuditStatus());
            result.setAuditResult(auditRecord.getAuditResult());
            result.setRejectReason(auditRecord.getRejectReason());
            result.setAuditTime(auditRecord.getAuditTime());
            result.setSubmitTime(auditRecord.getSubmitTime());
        } else {
            // 如果没有审核记录，设置默认状态
            result.setAuditStatus(0);
            result.setSubmitTime(recommenderInfo.getAuditSubmitTime());
        }

        // 设置通用身份信息和收款信息
        buildIdentityAndPaymentInfo(result, recommenderInfo, bankInfo);

        return result;
    }

    /**
     * 构建身份信息和收款信息（通用方法）
     */
    private void buildIdentityAndPaymentInfo(RecommenderAuditDetailVo result, RecommenderInfo recommenderInfo, RecommenderBank bankInfo) {
        // 设置通用身份信息
        result.setName(recommenderInfo.getName());
        result.setIdentifier(recommenderInfo.getIdentifier());

        // 根据推荐方类型设置特定字段
        if (recommenderInfo.getType() == 1) {
            // 个人类型：设置证件类型和证件照片
            result.setCertificateType(recommenderInfo.getCertificateType());
            result.setIdCardFrontUrl(recommenderInfo.getIdCardFrontUrl());
            result.setIdCardBackUrl(recommenderInfo.getIdCardBackUrl());


        } else if (recommenderInfo.getType() == 2) {
            // 企业类型：设置营业执照
            result.setBusinessLicenseUrl(recommenderInfo.getBusinessLicenseUrl());
        }

        // 设置通用收款信息
        if (bankInfo != null) {
            result.setAccountName(bankInfo.getAccountName());
            result.setAccountCertificateType(bankInfo.getAccountCertificateType());
            result.setAccountIdentifier(bankInfo.getAccountIdentifier());
            result.setAccountPhone(bankInfo.getAccountPhone());
            result.setBankCardNo(bankInfo.getBankCardNo());
            result.setBankName(bankInfo.getBankName());
            result.setBranchName(bankInfo.getBranchName());
            result.setProvince(bankInfo.getProvince());
            result.setCity(bankInfo.getCity());
            result.setBankAddress(bankInfo.getBankAddress());
        }
    }


    /**
     * 验证请求参数
     */
    private void validateRequest(RecommenderReqDto dto) {
        log.info("验证请求参数开始，参数：{}", JSON.toJSONString(dto));

        if (StrUtil.isBlank(dto.getName())) {
            throw new BusinessException("推荐方名称不能为空");
        }
        if (StrUtil.isBlank(dto.getIdentifier())) {
            throw new BusinessException("推荐方证件号不能为空");
        }

        // 验证推荐方类型对应的证件信息
        if (dto.getType() == 1) {
            // 个人类型：必须有身份证正反面
            if (!dto.getIsDraft() && (dto.getIdCardFrontUrl() == null || dto.getIdCardBackUrl() == null)) {
                throw new BusinessException("个人推荐方必须上传身份证正反面照片");
            }
            if (dto.getName().length() > 20) {
                throw new BusinessException("推荐方姓名不能超过20个字符");
            }
            boolean validCard = IdcardUtil.isValidCard(dto.getIdentifier());
            if (!validCard) {
                throw new BusinessException("推荐方身份证证件号格式不正确");
            }

            if (dto.getBankInfo() == null) {
                throw new BusinessException("推荐方银行卡信息不能为空");
            }
            if (dto.getCertificateType() == null) {
                throw new BusinessException("推荐方证件类型不能为空");
            }
            if (!dto.getName().equals(dto.getBankInfo().getAccountName())) {
                throw new BusinessException("推荐方银行卡信息开户户名和推荐方名称不一致");
            }
            if (!dto.getIdentifier().equals(dto.getBankInfo().getIdCardNo())) {
                throw new BusinessException("推荐方银行卡信息身份证证件号和推荐方身份证证件号不一致");
            }

            // 验证银行卡信息
            validateBankInfo(dto.getBankInfo(), dto.getType());
        } else if (dto.getType() == 2) {
            // 企业类型：必须有营业执照
            if (!dto.getIsDraft() && dto.getBusinessLicenseUrl() == null) {
                throw new BusinessException("企业推荐方必须上传营业执照");
            }
            if (dto.getName().length() > 50) {
                throw new BusinessException("推荐方企业全称不能超过50个字符");
            }
            boolean creditCode = CreditCodeUtil.isCreditCode(dto.getIdentifier());
            if (!creditCode) {
                throw new BusinessException("推荐方社会信用代码格式不正确");
            }
            if (dto.getBankInfo() == null) {
                throw new BusinessException("推荐方银行卡信息不能为空");
            }
            if (StrUtil.isBlank(dto.getBankInfo().getAccountName())) {
                throw new BusinessException("推荐方银行卡信息开户户名不能为空");
            }

            if (!dto.getName().equals(dto.getBankInfo().getAccountName())) {
                throw new BusinessException("推荐方银行卡信息开户户名和推荐方名称不一致");
            }
            if (StrUtil.isBlank(dto.getBankInfo().getIdCardNo())) {
                throw new BusinessException("推荐方银行卡信息纳税人识别号不能为空");
            }

            if (!dto.getIdentifier().equals(dto.getBankInfo().getIdCardNo())) {
                throw new BusinessException("推荐方银行卡信息纳税人识别号和推荐方社会信用代码不一致");
            }

            if (StrUtil.isBlank(dto.getBankInfo().getBankCardNo())) {
                throw new BusinessException("推荐方银行卡信息对公银行卡号不能为空");
            }
            if (StrUtil.isBlank(dto.getBankInfo().getAccountPhone())) {
                throw new BusinessException("推荐方银行卡信息开户手机号不能为空");
            }
            if (StrUtil.isBlank(dto.getBankInfo().getBankName())) {
                throw new BusinessException("推荐方银行卡信息开户银行不能为空");
            }
            if (StrUtil.isBlank(dto.getBankInfo().getProvince())) {
                throw new BusinessException("推荐方银行卡信息银行所在省份不能为空");
            }
            if (StrUtil.isBlank(dto.getBankInfo().getCity())) {
                throw new BusinessException("推荐方银行卡信息银行所在城市不能为空");
            }
            if (StrUtil.isBlank(dto.getBankInfo().getBranchName())) {
                throw new BusinessException("推荐方银行卡信息开户支行不能为空");
            }
        } else {
            throw new BusinessException("推荐方类型无效");
        }
    }

    /**
     * 验证银行信息
     *
     * @param bankInfo 银行信息
     * @param type     推荐方类型
     */
    private void validateBankInfo(RecommenderBankInfoDto bankInfo, Integer type) {
        if (bankInfo == null) {
            throw new BusinessException("推荐方银行卡信息不能为空");
        }

        // 验证开户名
        if (StrUtil.isBlank(bankInfo.getAccountName())) {
            throw new BusinessException("开户人姓名不能为空");
        }


        // 验证开户证件信息
        if (type == 1) {
            // 个人类型需要验证开户证件类型和证件号
            if (bankInfo.getAccountCertificateType() == null) {
                throw new BusinessException("开户证件类型不能为空");
            }
            if (!CertificateTypeEnum.isValidCode(bankInfo.getAccountCertificateType())) {
                throw new BusinessException("开户证件类型无效");
            }
            if (StrUtil.isBlank(bankInfo.getAccountIdentifier())) {
                throw new BusinessException("开户证件号不能为空");
            }

            // 验证开户证件号格式
            if (!IdcardUtil.isValidCard(bankInfo.getAccountIdentifier())) {
                throw new BusinessException("开户证件号格式不正确");
            }
        } else if (type == 2) {
            // 企业类型验证统一社会信用代码
            if (StrUtil.isBlank(bankInfo.getAccountIdentifier())) {
                throw new BusinessException("开户证件号（统一社会信用代码）不能为空");
            }
            if (!CreditCodeUtil.isCreditCode(bankInfo.getAccountIdentifier())) {
                throw new BusinessException("统一社会信用代码格式不正确");
            }
        }

        // 验证其他必填字段
        if (StrUtil.isBlank(bankInfo.getAccountPhone())) {
            throw new BusinessException("开户手机号不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getBankCardNo())) {
            throw new BusinessException("银行卡号不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getBankName())) {
            throw new BusinessException("开户银行不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getProvince())) {
            throw new BusinessException("银行所在省份不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getCity())) {
            throw new BusinessException("银行所在城市不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getBranchName())) {
            throw new BusinessException("开户支行不能为空");
        }
    }

    /**
     * 保存推荐方基本信息
     *
     * @param recommenderInfo 推荐方信息
     * @param dto             参数
     */
    private Boolean saveRecommenderInfo(RecommenderInfo recommenderInfo, RecommenderReqDto dto) {
        recommenderInfo.setType(dto.getType());
        recommenderInfo.setName(dto.getName());
        recommenderInfo.setIdentifier(dto.getIdentifier());
        recommenderInfo.setIsDraft(dto.getIsDraft());

        // 设置证件图片
        if (dto.getIdCardFrontUrl() != null) {
            recommenderInfo.setIdCardFrontUrl(dto.getIdCardFrontUrl());
        }
        if (dto.getIdCardBackUrl() != null) {
            recommenderInfo.setIdCardBackUrl(dto.getIdCardBackUrl());
        }
        if (dto.getBusinessLicenseUrl() != null) {
            recommenderInfo.setBusinessLicenseUrl(dto.getBusinessLicenseUrl());
        }

        // 如果不是草稿，设置提交审核时间
        if (!dto.getIsDraft()) {
            recommenderInfo.setAuditSubmitTime(LocalDateTime.now());
        }
        recommenderInfo.setCreateTime(LocalDateTime.now());
        recommenderInfo.setUpdateTime(LocalDateTime.now());

        if (recommenderInfo.getId() == null) {
            int i = recommenderInfoMapper.insert(recommenderInfo);
            return i > 0;
        } else {
            int i = recommenderInfoMapper.updateById(recommenderInfo);
            return i > 0;
        }
    }

    /**
     * 保存银行信息
     */
    private Boolean saveBankInfo(Long recommenderId, RecommenderBankInfoDto bankInfoDto, Boolean isDraft) {
        LambdaQueryWrapper<RecommenderBank> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecommenderBank::getRecommenderId, recommenderId);
        RecommenderBank bankInfo = recommenderBankMapper.selectOne(queryWrapper, false);

        if (bankInfo == null) {
            bankInfo = new RecommenderBank();
            bankInfo.setRecommenderId(recommenderId);
            // 未验证
            bankInfo.setValidateStatus(0);
        }

        // 设置银行信息
        bankInfo.setAccountName(bankInfoDto.getAccountName());
        bankInfo.setAccountCertificateType(bankInfoDto.getAccountCertificateType());
        bankInfo.setAccountIdentifier(bankInfoDto.getAccountIdentifier());
        bankInfo.setAccountPhone(bankInfoDto.getAccountPhone());
        bankInfo.setBankCardNo(bankInfoDto.getBankCardNo());
        bankInfo.setBankName(bankInfoDto.getBankName());
        bankInfo.setBankCode(bankInfoDto.getBankCode());
        bankInfo.setBranchCode(bankInfoDto.getBranchCode());
        bankInfo.setBranchName(bankInfoDto.getBranchName());
        bankInfo.setProvince(bankInfoDto.getProvince());
        bankInfo.setCity(bankInfoDto.getCity());
        bankInfo.setBankAddress(bankInfoDto.getBankAddress());
        bankInfo.setIsDraft(isDraft);

        // 如果不是草稿，设置提交审核时间
        if (!isDraft) {
            bankInfo.setAuditSubmitTime(LocalDateTime.now());
        }

        if (bankInfo.getId() == null) {
            bankInfo.setCreateTime(LocalDateTime.now());
            bankInfo.setUpdateTime(LocalDateTime.now());
            int i = recommenderBankMapper.insert(bankInfo);
            return i > 0;
        } else {
            bankInfo.setUpdateTime(LocalDateTime.now());
            int i = recommenderBankMapper.updateById(bankInfo);
            return i > 0;
        }
    }

}
