package tripai.recommend.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderGuideRelationQueryDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderHotelRelationQueryDto;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.vo.recommender.*;
import tripai.recommend.system.domain.vo.user.MobileInfo;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.mapper.recommender.RecommenderRelationMapper;
import tripai.recommend.system.service.RecommenderRelationService;
import tripai.recommend.system.service.UserInfoService;
import tripai.recommend.system.util.DataMaskingUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: RecommenderRelationServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/7/25 17:05
 * @version: 1.0
 */
@Service
@Slf4j
public class RecommenderRelationServiceImpl implements RecommenderRelationService {


    @Resource
    private RecommenderRelationMapper recommenderRelationMapper;

    @Resource
    private RecommenderMapper recommenderMapper;
    @Resource
    private UserInfoService userInfoService;


    /**
     * 查询推荐方酒店关系列表
     *
     * @param queryDto 查询条件
     * @return 推荐方酒店关系列表响应
     */
    @Override
    public ResponseResult<RecommenderHotelRelationListVo> getRecommenderHotelRelationList(RecommenderHotelRelationQueryDto queryDto) {
        log.info("查询推荐方酒店关系列表开始，查询条件：{}", queryDto);

        try {
            // 1. 参数验证
            if (queryDto == null) {
                log.error("查询推荐方酒店关系列表失败，查询条件为空");
                return ResponseResult.fail("查询条件不能为空");
            }

            if (queryDto.getUserId() == null) {
                log.error("查询推荐方酒店关系列表失败，用户ID为空");
                return ResponseResult.fail("用户ID不能为空");
            }

            // 2. 验证推荐方是否存在
            LambdaQueryWrapper<RecommenderInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RecommenderInfo::getUserId, queryDto.getUserId())
                    .eq(RecommenderInfo::getIsDel, 0);
            RecommenderInfo recommenderInfo = recommenderMapper.selectOne(queryWrapper, false);
            if (recommenderInfo == null) {
                log.error("查询推荐方酒店关系列表失败，推荐方不存在，推荐方ID：{}", queryDto.getRecommenderId());
                return ResponseResult.fail("推荐方不存在");
            }
            queryDto.setRecommenderId(recommenderInfo.getId());
            // 3. 参数校验和处理
            validateAndProcessQueryParams(queryDto);

            // 4. 查询关系列表
            List<RecommenderHotelRelationVo> relationList = recommenderRelationMapper.selectRecommenderHotelRelationList(queryDto);
            if (CollUtil.isNotEmpty(relationList)) {
                relationList.forEach(this::processHotelRelationDetail);
            }

            // 5. 查询总数
            Long totalCount = recommenderRelationMapper.selectRecommenderHotelRelationCount(queryDto);

            // 6. 查询统计信息
            RecommenderHotelRelationListVo.RecommenderHotelRelationStatisticsVo statistics =
                    recommenderRelationMapper.selectRecommenderHotelRelationStatistics(queryDto);

            // 7. 构建响应结果
            RecommenderHotelRelationListVo result = buildRelationListVo(relationList, totalCount, statistics, queryDto);

            log.info("查询推荐方酒店关系列表成功，推荐方ID：{}，总数：{}", queryDto.getRecommenderId(), totalCount);
            return ResponseResult.ok(result);

        } catch (Exception e) {
            log.error("查询推荐方酒店关系列表失败，查询条件：{}", queryDto, e);
            return ResponseResult.fail("查询推荐方酒店关系列表失败");
        }
    }

    /**
     * 处理酒店关系详情数据
     */
    private void processHotelRelationDetail(RecommenderHotelRelationVo relation) {
        if (relation == null) {
            return;
        }

        // 1. 手机号解密
        if (StrUtil.isNotBlank(relation.getSupplierMobile())) {
            // 使用UserInfoService的解密方法
            MobileInfo mobileInfo = userInfoService.mobileAesDecryptPublic(relation.getSupplierMobile());
            relation.setSupplierMobile(mobileInfo.getMobile());
            String decryptedMobile = mobileInfo.getMobile();

            // 手机号脱敏处理，中间4位显示****
            String maskedMobile = DataMaskingUtil.maskMobile(decryptedMobile);
            relation.setSupplierMobile(maskedMobile);
        }
    }

    /**
     * 查询导游供应商列表
     *
     * @param queryDto 查询参数
     * @return 导游供应商列表响应结果
     */
    @Override
    public ResponseResult<RecommenderGuideRelationListVo> getTourGuideSupplierList(RecommenderGuideRelationQueryDto queryDto) {

        log.info("查询推荐方导游关系列表开始，查询条件：{}", queryDto);

        try {
            // 1. 参数验证
            if (queryDto == null) {
                log.error("查询推荐方导游关系列表失败，查询条件为空");
                return ResponseResult.fail("查询条件不能为空");
            }
            if (queryDto.getUserId() == null) {
                log.error("查询推荐方酒店关系列表失败，用户ID为空");
                return ResponseResult.fail("用户ID不能为空");
            }

            // 2. 验证推荐方是否存在
            LambdaQueryWrapper<RecommenderInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RecommenderInfo::getUserId, queryDto.getUserId())
                    .eq(RecommenderInfo::getIsDel, 0);
            RecommenderInfo recommenderInfo = recommenderMapper.selectOne(queryWrapper, false);

            if (recommenderInfo == null) {
                log.error("查询推荐方导游关系列表失败，推荐方不存在，推荐方ID：{}", queryDto.getRecommenderId());
                return ResponseResult.fail("推荐方不存在");
            }

            if (recommenderInfo.getId() == null) {
                log.error("查询推荐方导游关系列表失败，推荐方ID为空");
                return ResponseResult.fail("推荐方ID不能为空");
            }
            queryDto.setRecommenderId(recommenderInfo.getId());

            // 3. 参数校验和处理
            validateAndProcessGuideQueryParams(queryDto);

            // 4. 查询关系列表
            List<RecommenderGuideRelationVo> relationList = recommenderRelationMapper.selectRecommenderGuideRelationList(queryDto);

            // 5. 处理数据脱敏和状态名称设置
            processGuideRelationList(relationList);

            // 6. 查询总数
            Long totalCount = recommenderRelationMapper.selectRecommenderGuideRelationCount(queryDto);

            // 7. 查询统计信息
            RecommenderGuideRelationStatisticsVo statistics =
                    recommenderRelationMapper.selectRecommenderGuideRelationStatistics(queryDto);

            // 8. 构建响应结果
            RecommenderGuideRelationListVo result = buildGuideRelationListVo(relationList, totalCount, statistics, queryDto);

            log.info("查询推荐方导游关系列表成功，推荐方ID：{}，总数：{}", queryDto.getRecommenderId(), totalCount);
            return ResponseResult.ok(result);

        } catch (Exception e) {
            log.error("查询推荐方导游关系列表失败，查询条件：{}", queryDto, e);
            return ResponseResult.fail("查询推荐方导游关系列表失败");
        }

    }


    /**
     * 验证和处理导游查询参数
     */
    private void validateAndProcessGuideQueryParams(RecommenderGuideRelationQueryDto queryDto) {

        // 处理关键词
        if (StrUtil.isNotBlank(queryDto.getKeyword())) {
            queryDto.setKeyword(queryDto.getKeyword().trim());
        }
    }

    /**
     * 构建导游关系列表VO
     */
    private RecommenderGuideRelationListVo buildGuideRelationListVo(List<RecommenderGuideRelationVo> relationList,
                                                                    Long totalCount,
                                                                    RecommenderGuideRelationStatisticsVo statistics,
                                                                    RecommenderGuideRelationQueryDto queryDto) {
        RecommenderGuideRelationListVo result = new RecommenderGuideRelationListVo();

        // 转换数据
        if (CollUtil.isNotEmpty(relationList)) {
            List<TourGuideSupplierVo> suppliers = relationList.stream().map(this::convertToTourGuideSupplierVo).collect(Collectors.toList());
            result.setSuppliers(suppliers);
        }


        result.setTotal(totalCount);
        result.setPageNum(queryDto.getPageNum());
        result.setPageSize(queryDto.getPageSize());


        // 设置统计信息
        if (statistics != null) {
            result.setStatistics(statistics);
        }

        // 计算分页信息
        result.calculatePaginationInfo();

        return result;
    }

    /**
     * 转换RecommenderGuideRelationVo为TourGuideSupplierVo
     */
    private TourGuideSupplierVo convertToTourGuideSupplierVo(RecommenderGuideRelationVo relation) {
        if (relation == null) {
            return null;
        }

        TourGuideSupplierVo supplier = new TourGuideSupplierVo();

        // 基本信息
        supplier.setRelationId(relation.getRelationId());
        supplier.setRecommenderId(relation.getRecommenderId());
        supplier.setGuideId(relation.getGuideId());
        supplier.setGuideName(relation.getGuideName());
        supplier.setPhone(relation.getPhone());
        supplier.setUserId(relation.getUserId());

        // 产线信息
        supplier.setProductionLine(relation.getProductionLine());
        supplier.setProductionLineName(relation.getProductionLineName());

        // 服务信息
        supplier.setServiceScore(relation.getServiceScore());
        supplier.setServiceCount(relation.getServiceCount());
        supplier.setOrderCount(relation.getOrderCount());

        // 状态信息
        supplier.setControlStatus(relation.getControlStatus());
        supplier.setControlStatusName(relation.getControlStatusName());
        supplier.setRelationStatus(relation.getRelationStatus());
        supplier.setAuditStatus(relation.getAuditStatus());
        supplier.setAuditStatusName(relation.getAuditStatusName());

        // 时间信息
        supplier.setRelationCreateTime(relation.getRelationCreateTime());
        supplier.setRejectCountMonth(relation.getRejectCountMonth());

        // 服务信息
        supplier.setServiceCities(relation.getServiceCities());
        supplier.setPrimaryServiceCity(relation.getPrimaryServiceCity());
        supplier.setLanguage(relation.getLanguage());
        supplier.setIntroduction(relation.getIntroduction());
        supplier.setRejectionCount(relation.getRejectionCount());

        // 业务类型
        supplier.setBizType(relation.getBizType());
        return supplier;
    }


    /**
     * 处理导游关系列表数据
     */
    private void processGuideRelationList(List<RecommenderGuideRelationVo> relationList) {
        if (relationList == null || relationList.isEmpty()) {
            return;
        }

        for (RecommenderGuideRelationVo relation : relationList) {
            processGuideRelationDetail(relation);
        }
    }

    /**
     * 处理导游关系详情数据
     */
    private void processGuideRelationDetail(RecommenderGuideRelationVo relation) {
        if (relation == null) {
            return;
        }

        // 1. 手机号脱敏
        if (StrUtil.isNotBlank(relation.getPhone())) {
            relation.setPhone(DataMaskingUtil.maskMobile(relation.getPhone()));
        }

        // 2. 查询并设置上架服务数
        if (relation.getGuideId() != null) {
            Integer serviceCount = recommenderRelationMapper.selectServiceCountByGuideId(relation.getGuideId());
            relation.setServiceCount(serviceCount != null ? serviceCount : 0);
        }

        // 3. 查询并设置成单数
        if (relation.getGuideId() != null && relation.getRecommenderId() != null && relation.getRelationCreateTime() != null) {
            Integer orderCount = recommenderRelationMapper.selectOrderCountByGuideId(
                    relation.getGuideId(),
                    relation.getRecommenderId(),
                    relation.getRelationCreateTime().toString()
            );
            relation.setOrderCount(orderCount != null ? orderCount : 0);
        }
    }


    /**
     * 验证和处理查询参数
     */
    private void validateAndProcessQueryParams(RecommenderHotelRelationQueryDto queryDto) {


        // 处理关键词
        if (StrUtil.isNotBlank(queryDto.getKeyword())) {
            queryDto.setKeyword(queryDto.getKeyword().trim());
        }
    }

    /**
     * 构建关系列表VO
     */
    private RecommenderHotelRelationListVo buildRelationListVo(List<RecommenderHotelRelationVo> relationList, Long totalCount,
                                                               RecommenderHotelRelationListVo.RecommenderHotelRelationStatisticsVo statistics,
                                                               RecommenderHotelRelationQueryDto queryDto) {
        RecommenderHotelRelationListVo result = new RecommenderHotelRelationListVo();
        result.setRelations(relationList);
        result.setTotal(totalCount);
        result.setPageNum(queryDto.getPageNum());
        result.setPageSize(queryDto.getPageSize());
        result.setSortType(queryDto.getSortType());
        result.setStatistics(statistics);

        // 计算分页信息
        result.calculatePaginationInfo();

        return result;
    }
}
