package tripai.recommend.system.service.impl;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.BankCardVerificationRequest;
import com.tencentcloudapi.faceid.v20180301.models.BankCardVerificationResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tripai.recommend.system.domain.dto.BankCardVerifyDto;
import tripai.recommend.system.domain.vo.BankCardVerifyResultVo;
import tripai.recommend.system.service.TencentBankCardVerifyService;

import java.time.LocalDate;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: TencentBankCardVerifyServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 11:40
 * @version: 1.0
 */
@Slf4j
@Service
public class TencentBankCardVerifyServiceImpl implements TencentBankCardVerifyService {

    /**
     * 银行卡验证redis键前缀 - 分钟级限制
     */
    private final String BANK_VERIFY_MINUTE_REDISKEY = "rms:bank_verify:minute:";

    /**
     * 银行卡验证redis键前缀 - 天级限制
     */
    private final String BANK_VERIFY_DAY_REDISKEY = "rms:bank_verify:day:";

    /**
     * 同一用户1分钟内最多请求15次
     */
    private final int MAX_MINUTE_LIMIT = 15;

    /**
     * 同一用户1天内最多请求30次
     */
    private final int MAX_DAY_LIMIT = 30;

    /**
     * 分钟级redis过期时间（秒）
     */
    private final int MINUTE_EXPIRE_TIME = 60;

    /**
     * 天级redis过期时间（秒）
     */
    private final int DAY_EXPIRE_TIME = 24 * 60 * 60;

    @Value("${tencent.cloud.bank.secret-id}")
    private String secretId;

    @Value("${tencent.cloud.bank.secret-key}")
    private String secretKey;

    @Value("${tencent.cloud.bank.region}")
    private String region;

    @Value("${tencent.cloud.bank.endpoint}")
    private String endpoint;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 银行卡三要素核验
     *
     * @param userId    用户id
     * @param verifyDto 核验请求参数
     * @return
     */
    @Override
    public BankCardVerifyResultVo verifyBankCardWithRateLimit(Long userId, BankCardVerifyDto verifyDto) {
        // 1. 执行频率限制检查
        BankCardVerifyResultVo rateLimitResult = checkRateLimit(userId);
        if (rateLimitResult != null) {
            // 频率限制超限，直接返回错误
            return rateLimitResult;
        }

        // 2. 频率检查通过，执行银行卡验证
        try {
            return verifyBankCard(verifyDto);
        } catch (Exception e) {
            log.error("银行卡验证失败，用户ID: {}", userId, e);
            return BankCardVerifyResultVo.fail("银行卡验证失败: " + e.getMessage());
        }
    }


    @Override
    public BankCardVerifyResultVo verifyBankCard(BankCardVerifyDto verifyDto) {
        try {
            log.info("开始进行银行卡三要素核验，卡号：{}", maskBankCardNumber(verifyDto.getBankCardNumber()));

            Credential cred = new Credential(secretId, secretKey);

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endpoint);

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            FaceidClient client = new FaceidClient(cred, region, clientProfile);

            BankCardVerificationRequest req = new BankCardVerificationRequest();
            req.setName(verifyDto.getCardholderName());
            req.setIdCard(verifyDto.getIdNumber());
            req.setBankCard(verifyDto.getBankCardNumber());

            BankCardVerificationResponse resp = client.BankCardVerification(req);

            log.info("银行卡核验完成，结果：{}", resp.getResult());
            return BankCardVerifyResultVo.success(Integer.valueOf(resp.getResult()), resp.getDescription());

        } catch (TencentCloudSDKException e) {
            log.error("银行卡核验失败", e);
            return BankCardVerifyResultVo.fail("银行卡核验失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("银行卡核验异常", e);
            return BankCardVerifyResultVo.fail("银行卡核验异常：" + e.getMessage());
        }
    }

    /**
     * 检查频率限制
     *
     * @param userId 用户ID
     * @return 如果超限返回错误结果，否则返回null
     */
    private BankCardVerifyResultVo checkRateLimit(Long userId) {
        // 根据用户ID确定Redis键
        String minuteRedisKey = BANK_VERIFY_MINUTE_REDISKEY + userId;
        String dayRedisKey = BANK_VERIFY_DAY_REDISKEY + userId + ":" + LocalDate.now().toString();

        // 防刷限制：双重限制检查
        Long minuteCount = null;
        Long dayCount = null;

        try {
            // 1. 检查分钟级限制
            minuteCount = stringRedisTemplate.opsForValue().increment(minuteRedisKey, 1);
            // 如果minuteCount为null，设置默认值为1
            if (minuteCount == null) {
                minuteCount = 1L;
                stringRedisTemplate.opsForValue().set(minuteRedisKey, String.valueOf(minuteCount),
                        MINUTE_EXPIRE_TIME, TimeUnit.SECONDS);
                log.warn("Redis increment返回null（分钟级），已设置默认值为1，用户ID: {}", userId);
            } else if (minuteCount == 1) {
                // 设置过期时间
                stringRedisTemplate.expire(minuteRedisKey, MINUTE_EXPIRE_TIME, TimeUnit.SECONDS);
            }

            // 2. 检查天级限制
            dayCount = stringRedisTemplate.opsForValue().increment(dayRedisKey, 1);
            // 如果dayCount为null，设置默认值为1
            if (dayCount == null) {
                dayCount = 1L;
                stringRedisTemplate.opsForValue().set(dayRedisKey, String.valueOf(dayCount),
                        DAY_EXPIRE_TIME, TimeUnit.SECONDS);
                log.warn("Redis increment返回null（天级），已设置默认值为1，用户ID: {}", userId);
            } else if (dayCount == 1) {
                // 设置过期时间
                stringRedisTemplate.expire(dayRedisKey, DAY_EXPIRE_TIME, TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            // Redis操作异常，记录日志但允许请求继续
            log.info("Redis操作异常，无法进行防刷限制，用户ID: {}", userId);
            return null;
        }

        // 检查分钟级限制
        if (minuteCount > MAX_MINUTE_LIMIT) {
            // 获取分钟级Redis键的剩余过期时间（秒）
            Long expireTime = stringRedisTemplate.getExpire(minuteRedisKey, TimeUnit.SECONDS);
            // 如果无法获取过期时间或已过期，默认为60秒
            if (expireTime == null || expireTime <= 0) {
                expireTime = (long) MINUTE_EXPIRE_TIME;
            }
            String message = String.format("请求过于频繁，1分钟内最多请求%d次，请%d秒后再试",
                    MAX_MINUTE_LIMIT, expireTime);
            return BankCardVerifyResultVo.fail(message);
        }

        // 检查天级限制
        if (dayCount > MAX_DAY_LIMIT) {
            // 获取天级Redis键的剩余过期时间（秒）
            Long expireTime = stringRedisTemplate.getExpire(dayRedisKey, TimeUnit.SECONDS);
            // 如果无法获取过期时间或已过期，默认为24小时
            if (expireTime == null || expireTime <= 0) {
                expireTime = (long) DAY_EXPIRE_TIME;
            }
            // 将秒转换为小时显示
            long expireHours = expireTime / 3600;
            long expireMinutes = (expireTime % 3600) / 60;
            String timeMessage = expireHours > 0 ?
                    String.format("%d小时%d分钟", expireHours, expireMinutes) :
                    String.format("%d分钟", expireMinutes);
            String message = String.format("今日请求次数已达上限（%d次），请%s后再试",
                    MAX_DAY_LIMIT, timeMessage);
            return BankCardVerifyResultVo.fail(message);
        }

        return null;
    }

    /**
     * 银行卡号脱敏
     */
    private String maskBankCardNumber(String bankCardNumber) {
        if (bankCardNumber == null || bankCardNumber.length() < 8) {
            return bankCardNumber;
        }
        return bankCardNumber.substring(0, 4) + "****" + bankCardNumber.substring(bankCardNumber.length() - 4);
    }
}
