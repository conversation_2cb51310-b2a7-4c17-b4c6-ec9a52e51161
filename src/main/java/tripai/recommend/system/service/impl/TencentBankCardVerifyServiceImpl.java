package tripai.recommend.system.service.impl;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.BankCardVerificationRequest;
import com.tencentcloudapi.faceid.v20180301.models.BankCardVerificationResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tripai.recommend.system.domain.dto.BankCardVerifyDto;
import tripai.recommend.system.domain.vo.BankCardVerifyResultVo;
import tripai.recommend.system.service.TencentBankCardVerifyService;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: TencentBankCardVerifyServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 11:40
 * @version: 1.0
 */
@Slf4j
@Service
public class TencentBankCardVerifyServiceImpl implements TencentBankCardVerifyService {

    @Value("${tencent.cloud.bank.secret-id}")
    private String secretId;

    @Value("${tencent.cloud.bank.secret-key}")
    private String secretKey;

    @Value("${tencent.cloud.bank.region}")
    private String region;

    @Value("${tencent.cloud.bank.endpoint}")
    private String endpoint;

    @Override
    public BankCardVerifyResultVo verifyBankCard(BankCardVerifyDto verifyDto) {
        try {
            log.info("开始进行银行卡三要素核验，卡号：{}", maskBankCardNumber(verifyDto.getBankCardNumber()));

            Credential cred = new Credential(secretId, secretKey);

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endpoint);

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            FaceidClient client = new FaceidClient(cred, region, clientProfile);

            BankCardVerificationRequest req = new BankCardVerificationRequest();
            req.setName(verifyDto.getCardholderName());
            req.setIdCard(verifyDto.getIdNumber());
            req.setBankCard(verifyDto.getBankCardNumber());

            BankCardVerificationResponse resp = client.BankCardVerification(req);

            log.info("银行卡核验完成，结果：{}", resp.getResult());
            return BankCardVerifyResultVo.success(Integer.valueOf(resp.getResult()), resp.getDescription());

        } catch (TencentCloudSDKException e) {
            log.error("银行卡核验失败", e);
            return BankCardVerifyResultVo.fail("银行卡核验失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("银行卡核验异常", e);
            return BankCardVerifyResultVo.fail("银行卡核验异常：" + e.getMessage());
        }
    }

    /**
     * 银行卡号脱敏
     */
    private String maskBankCardNumber(String bankCardNumber) {
        if (bankCardNumber == null || bankCardNumber.length() < 8) {
            return bankCardNumber;
        }
        return bankCardNumber.substring(0, 4) + "****" + bankCardNumber.substring(bankCardNumber.length() - 4);
    }
}
