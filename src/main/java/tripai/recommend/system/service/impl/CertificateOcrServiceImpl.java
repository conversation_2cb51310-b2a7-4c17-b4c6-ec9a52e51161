package tripai.recommend.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tripai.recommend.system.domain.vo.CertificateVo;
import tripai.recommend.system.domain.vo.IdCardInfoVo;
import tripai.recommend.system.service.CertificateOcrService;

import java.io.IOException;
import java.util.Base64;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: CertificateOcrServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 14:09
 * @version: 1.0
 */

@Slf4j
@Service
public class CertificateOcrServiceImpl implements CertificateOcrService {

    @Autowired
    private OcrClient ocrClient;

    /**
     * OCR证件信息
     *
     * @param type 证件类型 1：身份证 2：护照
     * @param file 护照图片
     * @return 证件信息
     */
    @Override
    public CertificateVo recognizeCertificate(Integer type, MultipartFile file) {
        CertificateVo certificateVo = recognizeFrontSide(file);
        log.info("返回的身份证信息：{}", JSON.toJSONString(certificateVo));
        return certificateVo;
    }

    public CertificateVo recognizeFrontSide(MultipartFile file) {
        try {
            // 构建请求
            IDCardOCRRequest req = new IDCardOCRRequest();
            // 设置为身份证正面
            req.setCardSide("FRONT");
            // 设置图片Base64
            req.setImageBase64(Base64.getEncoder().encodeToString(file.getBytes()));
            // 发送请求
            IDCardOCRResponse response = ocrClient.IDCardOCR(req);
            // 构建返回结果
            IdCardInfoVo idCardInfo = new IdCardInfoVo();
            idCardInfo.setCertificateNumber(response.getIdNum());
            idCardInfo.setName(response.getName());
            idCardInfo.setGender(response.getSex());
            idCardInfo.setBirthday(response.getBirth());
            idCardInfo.setAddress(response.getAddress());

            return idCardInfo;
        } catch (TencentCloudSDKException e) {
            log.info("身份证正面识别失败", e);
            throw new RuntimeException("身份证正面识别失败: " + e.getMessage());
        } catch (IOException e) {
            log.info("读取图片文件失败", e);
            throw new RuntimeException("读取图片文件失败: " + e.getMessage());
        }
    }

}
