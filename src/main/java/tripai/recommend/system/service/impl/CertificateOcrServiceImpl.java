package tripai.recommend.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tripai.recommend.system.domain.vo.CertificateVo;
import tripai.recommend.system.domain.vo.IdCardInfoVo;
import tripai.recommend.system.service.CertificateOcrService;

import java.io.IOException;
import java.time.LocalDate;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: CertificateOcrServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 14:09
 * @version: 1.0
 */

@Slf4j
@Service
public class CertificateOcrServiceImpl implements CertificateOcrService {

    /**
     * OCR证件信息redis键前缀 - 分钟级限制
     */
    private final String CERTIFICATE_MINUTE_REDISKEY = "rms:certificate:minute:";

    /**
     * OCR证件信息redis键前缀 - 天级限制
     */
    private final String CERTIFICATE_DAY_REDISKEY = "rms:certificate:day:";

    /**
     * 同一用户1分钟内最多请求15次
     */
    private final int MAX_MINUTE_LIMIT = 15;

    /**
     * 同一用户1天内最多请求30次
     */
    private final int MAX_DAY_LIMIT = 30;

    /**
     * 分钟级redis过期时间（秒）
     */
    private final int MINUTE_EXPIRE_TIME = 60;

    /**
     * 天级redis过期时间（秒）
     */
    private final int DAY_EXPIRE_TIME = 24 * 60 * 60;

    @Autowired
    private OcrClient ocrClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * OCR证件信息
     *
     * @param userId 用户userId
     * @param file   证件图片
     * @param type   证件类型 1：身份证 2：护照
     * @return
     */
    @Override
    public CertificateVo recognizeCertificateWithRateLimit(Long userId, MultipartFile file, Integer type) {
        // 1. 执行频率限制检查
        String rateLimitError = checkRateLimit(userId, type);
        if (rateLimitError != null) {
            // 频率限制超限，抛出异常
            throw new RuntimeException(rateLimitError);
        }

        // 2. 频率检查通过，执行OCR识别
        try {
            return recognizeCertificate(type, file);
        } catch (Exception e) {
            log.error("OCR识别失败，用户ID: {}", userId, e);
            throw new RuntimeException("OCR识别失败: " + e.getMessage());
        }
    }

    /**
     * OCR证件信息
     *
     * @param type 证件类型 1：身份证 2：护照
     * @param file 护照图片
     * @return 证件信息
     */
    @Override
    public CertificateVo recognizeCertificate(Integer type, MultipartFile file) {
        CertificateVo certificateVo = recognizeFrontSide(file);
        log.info("返回的身份证信息：{}", JSON.toJSONString(certificateVo));
        return certificateVo;
    }

    /**
     * 检查频率限制
     *
     * @param userId 用户ID
     * @param type   证件类型
     * @return 如果超限返回错误信息，否则返回null
     */
    private String checkRateLimit(Long userId, Integer type) {
        // 根据证件类型和用户ID确定Redis键
        String minuteRedisKey = CERTIFICATE_MINUTE_REDISKEY + type + ":" + userId;
        String dayRedisKey = CERTIFICATE_DAY_REDISKEY + type + ":" + userId + ":" +
                LocalDate.now().toString();

        // 防刷限制：双重限制检查
        Long minuteCount = null;
        Long dayCount = null;

        try {
            // 1. 检查分钟级限制
            minuteCount = stringRedisTemplate.opsForValue().increment(minuteRedisKey, 1);
            // 如果minuteCount为null，设置默认值为1
            if (minuteCount == null) {
                minuteCount = 1L;
                stringRedisTemplate.opsForValue().set(minuteRedisKey, String.valueOf(minuteCount),
                        MINUTE_EXPIRE_TIME, TimeUnit.SECONDS);
                log.warn("Redis increment返回null（分钟级），已设置默认值为1，用户ID: {}", userId);
            } else if (minuteCount == 1) {
                // 设置过期时间
                stringRedisTemplate.expire(minuteRedisKey, MINUTE_EXPIRE_TIME, TimeUnit.SECONDS);
            }

            // 2. 检查天级限制
            dayCount = stringRedisTemplate.opsForValue().increment(dayRedisKey, 1);
            // 如果dayCount为null，设置默认值为1
            if (dayCount == null) {
                dayCount = 1L;
                stringRedisTemplate.opsForValue().set(dayRedisKey, String.valueOf(dayCount),
                        DAY_EXPIRE_TIME, TimeUnit.SECONDS);
                log.warn("Redis increment返回null（天级），已设置默认值为1，用户ID: {}", userId);
            } else if (dayCount == 1) {
                // 设置过期时间
                stringRedisTemplate.expire(dayRedisKey, DAY_EXPIRE_TIME, TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            // Redis操作异常，记录日志但允许请求继续
            log.info("Redis操作异常，无法进行防刷限制，用户ID: {}", userId);
            return null;
        }

        // 检查分钟级限制
        if (minuteCount > MAX_MINUTE_LIMIT) {
            // 获取分钟级Redis键的剩余过期时间（秒）
            Long expireTime = stringRedisTemplate.getExpire(minuteRedisKey, TimeUnit.SECONDS);
            // 如果无法获取过期时间或已过期，默认为60秒
            if (expireTime == null || expireTime <= 0) {
                expireTime = (long) MINUTE_EXPIRE_TIME;
            }
            return String.format("请求过于频繁，1分钟内最多请求%d次，请%d秒后再试",
                    MAX_MINUTE_LIMIT, expireTime);
        }

        // 检查天级限制
        if (dayCount > MAX_DAY_LIMIT) {
            // 获取天级Redis键的剩余过期时间（秒）
            Long expireTime = stringRedisTemplate.getExpire(dayRedisKey, TimeUnit.SECONDS);
            // 如果无法获取过期时间或已过期，默认为24小时
            if (expireTime == null || expireTime <= 0) {
                expireTime = (long) DAY_EXPIRE_TIME;
            }
            // 将秒转换为小时显示
            long expireHours = expireTime / 3600;
            long expireMinutes = (expireTime % 3600) / 60;
            String timeMessage = expireHours > 0 ?
                    String.format("%d小时%d分钟", expireHours, expireMinutes) :
                    String.format("%d分钟", expireMinutes);
            return String.format("今日请求次数已达上限（%d次），请%s后再试",
                    MAX_DAY_LIMIT, timeMessage);
        }

        return null;
    }

    public CertificateVo recognizeFrontSide(MultipartFile file) {
        try {
            // 构建请求
            IDCardOCRRequest req = new IDCardOCRRequest();
            // 设置为身份证正面
            req.setCardSide("FRONT");
            // 设置图片Base64
            req.setImageBase64(Base64.getEncoder().encodeToString(file.getBytes()));
            // 发送请求
            IDCardOCRResponse response = ocrClient.IDCardOCR(req);
            // 构建返回结果
            IdCardInfoVo idCardInfo = new IdCardInfoVo();
            idCardInfo.setCertificateNumber(response.getIdNum());
            idCardInfo.setName(response.getName());
            idCardInfo.setGender(response.getSex());
            idCardInfo.setBirthday(response.getBirth());
            idCardInfo.setAddress(response.getAddress());

            return idCardInfo;
        } catch (TencentCloudSDKException e) {
            log.info("身份证正面识别失败", e);
            throw new RuntimeException("身份证正面识别失败: " + e.getMessage());
        } catch (IOException e) {
            log.info("读取图片文件失败", e);
            throw new RuntimeException("读取图片文件失败: " + e.getMessage());
        }
    }

}
