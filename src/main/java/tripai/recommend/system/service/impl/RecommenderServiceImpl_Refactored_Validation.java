package tripai.recommend.system.service.impl;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankInfoDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderReqDto;
import tripai.recommend.system.enums.CertificateTypeEnum;
import tripai.recommend.system.exception.BusinessException;
import tripai.recommend.system.utils.CreditCodeUtil;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: RecommenderServiceImpl_Refactored_Validation
 * @author: lijunqi
 * @description: 重构后的推荐方验证逻辑（清晰版本）
 * @date: 2025/7/29 16:30
 * @version: 1.0
 */

@Slf4j
public class RecommenderServiceImpl_Refactored_Validation {

    /**
     * 验证请求参数 - 重构后的清晰版本
     */
    private void validateRequest(RecommenderReqDto dto) {
        log.info("验证请求参数开始，参数：{}", JSON.toJSONString(dto));

        // 1. 基础字段验证
        validateBasicFields(dto);

        // 2. 根据类型进行专门验证
        if (dto.getType() == 1) {
            validatePersonalRecommender(dto);
        } else if (dto.getType() == 2) {
            validateEnterpriseRecommender(dto);
        } else {
            throw new BusinessException("推荐方类型无效");
        }

        // 3. 银行信息验证
        validateBankInfo(dto.getBankInfo(), dto.getType());

        log.info("验证请求参数完成");
    }

    /**
     * 验证基础字段（个人和企业共同的字段）
     */
    private void validateBasicFields(RecommenderReqDto dto) {
        if (StrUtil.isBlank(dto.getName())) {
            throw new BusinessException("推荐方名称不能为空");
        }
        if (StrUtil.isBlank(dto.getIdentifier())) {
            throw new BusinessException("推荐方证件号不能为空");
        }
        if (dto.getBankInfo() == null) {
            throw new BusinessException("推荐方银行卡信息不能为空");
        }
    }

    /**
     * 验证个人类型推荐方
     */
    private void validatePersonalRecommender(RecommenderReqDto dto) {
        log.info("验证个人类型推荐方，推荐方ID：{}", dto.getUserId());

        // 1. 个人姓名长度验证
        if (dto.getName().length() > 20) {
            throw new BusinessException("推荐方姓名不能超过20个字符");
        }

        // 2. 身份证号格式验证
        if (!IdcardUtil.isValidCard(dto.getIdentifier())) {
            throw new BusinessException("推荐方身份证证件号格式不正确");
        }

        // 3. 证件类型验证
        if (dto.getCertificateType() == null) {
            throw new BusinessException("推荐方证件类型不能为空");
        }

        // 4. 身份证照片验证（非草稿模式）
        if (!dto.getIsDraft()) {
            if (dto.getIdCardFrontUrl() == null || dto.getIdCardBackUrl() == null) {
                throw new BusinessException("个人推荐方必须上传身份证正反面照片");
            }
        }

        // 5. 个人与银行信息一致性验证
        validatePersonalBankConsistency(dto);
    }

    /**
     * 验证企业类型推荐方
     */
    private void validateEnterpriseRecommender(RecommenderReqDto dto) {
        log.info("验证企业类型推荐方，推荐方ID：{}", dto.getUserId());

        // 1. 企业名称长度验证
        if (dto.getName().length() > 50) {
            throw new BusinessException("推荐方企业全称不能超过50个字符");
        }

        // 2. 统一社会信用代码格式验证
        if (!CreditCodeUtil.isCreditCode(dto.getIdentifier())) {
            throw new BusinessException("推荐方社会信用代码格式不正确");
        }

        // 3. 营业执照验证（非草稿模式）
        if (!dto.getIsDraft()) {
            if (dto.getBusinessLicenseUrl() == null) {
                throw new BusinessException("企业推荐方必须上传营业执照");
            }
        }

        // 4. 企业与银行信息一致性验证
        validateEnterpriseBankConsistency(dto);
    }

    /**
     * 验证个人与银行信息的一致性
     */
    private void validatePersonalBankConsistency(RecommenderReqDto dto) {
        RecommenderBankInfoDto bankInfo = dto.getBankInfo();

        // 姓名一致性验证
        if (!dto.getName().equals(bankInfo.getAccountName())) {
            throw new BusinessException("推荐方银行卡信息开户户名和推荐方名称不一致");
        }

        // 身份证号一致性验证
        if (!dto.getIdentifier().equals(bankInfo.getIdCardNo())) {
            throw new BusinessException("推荐方银行卡信息身份证证件号和推荐方身份证证件号不一致");
        }
    }

    /**
     * 验证企业与银行信息的一致性
     */
    private void validateEnterpriseBankConsistency(RecommenderReqDto dto) {
        RecommenderBankInfoDto bankInfo = dto.getBankInfo();

        // 企业名称一致性验证
        if (StrUtil.isBlank(bankInfo.getAccountName())) {
            throw new BusinessException("推荐方银行卡信息开户户名不能为空");
        }
        if (!dto.getName().equals(bankInfo.getAccountName())) {
            throw new BusinessException("推荐方银行卡信息开户户名和推荐方名称不一致");
        }

        // 纳税人识别号一致性验证
        if (StrUtil.isBlank(bankInfo.getIdCardNo())) {
            throw new BusinessException("推荐方银行卡信息纳税人识别号不能为空");
        }
        if (!dto.getIdentifier().equals(bankInfo.getIdCardNo())) {
            throw new BusinessException("推荐方银行卡信息纳税人识别号和推荐方社会信用代码不一致");
        }

        // 企业银行信息必填字段验证
        validateEnterpriseRequiredBankFields(bankInfo);
    }

    /**
     * 验证企业银行信息必填字段
     */
    private void validateEnterpriseRequiredBankFields(RecommenderBankInfoDto bankInfo) {
        if (StrUtil.isBlank(bankInfo.getBankCardNo())) {
            throw new BusinessException("推荐方银行卡信息对公银行卡号不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getAccountPhone())) {
            throw new BusinessException("推荐方银行卡信息开户手机号不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getBankName())) {
            throw new BusinessException("推荐方银行卡信息开户银行不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getProvince())) {
            throw new BusinessException("推荐方银行卡信息银行所在省份不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getCity())) {
            throw new BusinessException("推荐方银行卡信息银行所在城市不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getBranchName())) {
            throw new BusinessException("推荐方银行卡信息开户支行不能为空");
        }
    }

    /**
     * 验证银行信息 - 重构后的清晰版本
     */
    private void validateBankInfo(RecommenderBankInfoDto bankInfo, Integer type) {
        if (bankInfo == null) {
            throw new BusinessException("推荐方银行卡信息不能为空");
        }

        log.info("验证银行信息，类型：{}", type == 1 ? "个人" : "企业");

        // 1. 通用字段验证
        validateCommonBankFields(bankInfo);

        // 2. 根据类型验证特定字段
        if (type == 1) {
            validatePersonalBankFields(bankInfo);
        } else if (type == 2) {
            validateEnterpriseBankFields(bankInfo);
        }
    }

    /**
     * 验证银行信息通用字段
     */
    private void validateCommonBankFields(RecommenderBankInfoDto bankInfo) {
        if (StrUtil.isBlank(bankInfo.getAccountName())) {
            throw new BusinessException("开户人姓名不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getAccountPhone())) {
            throw new BusinessException("开户手机号不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getBankCardNo())) {
            throw new BusinessException("银行卡号不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getBankName())) {
            throw new BusinessException("开户银行不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getProvince())) {
            throw new BusinessException("银行所在省份不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getCity())) {
            throw new BusinessException("银行所在城市不能为空");
        }
        if (StrUtil.isBlank(bankInfo.getBranchName())) {
            throw new BusinessException("开户支行不能为空");
        }
    }

    /**
     * 验证个人银行信息特定字段
     */
    private void validatePersonalBankFields(RecommenderBankInfoDto bankInfo) {
        // 验证开户证件类型
        if (bankInfo.getAccountCertificateType() == null) {
            throw new BusinessException("开户证件类型不能为空");
        }
        if (!CertificateTypeEnum.isValidCode(bankInfo.getAccountCertificateType())) {
            throw new BusinessException("开户证件类型无效");
        }

        // 验证开户证件号
        if (StrUtil.isBlank(bankInfo.getAccountIdentifier())) {
            throw new BusinessException("开户证件号不能为空");
        }
        if (!IdcardUtil.isValidCard(bankInfo.getAccountIdentifier())) {
            throw new BusinessException("开户证件号格式不正确");
        }
    }

    /**
     * 验证企业银行信息特定字段
     */
    private void validateEnterpriseBankFields(RecommenderBankInfoDto bankInfo) {
        // 验证统一社会信用代码
        if (StrUtil.isBlank(bankInfo.getAccountIdentifier())) {
            throw new BusinessException("开户证件号（统一社会信用代码）不能为空");
        }
        if (!CreditCodeUtil.isCreditCode(bankInfo.getAccountIdentifier())) {
            throw new BusinessException("统一社会信用代码格式不正确");
        }
    }
}
