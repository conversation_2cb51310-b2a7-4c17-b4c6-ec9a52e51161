package tripai.recommend.system.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.config.AuthDefaultSource;
import me.zhyd.oauth.model.AuthUser;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tripai.recommend.system.constant.CryptoSecretConstants;
import tripai.recommend.system.constant.HttpHeadConstant;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.entity.*;
import tripai.recommend.system.domain.enums.QrChannelBindEnum;
import tripai.recommend.system.domain.enums.SmsBuisnessEnum;
import tripai.recommend.system.domain.vo.TokenInfo;
import tripai.recommend.system.domain.vo.user.*;
import tripai.recommend.system.exception.BusinessException;
import tripai.recommend.system.mapper.SocialUserAuthMapper;
import tripai.recommend.system.mapper.SocialUserMapper;
import tripai.recommend.system.mapper.UserInfoMapper;
import tripai.recommend.system.mapper.recommender.RecommenderBankMapper;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.OssFileOpService;
import tripai.recommend.system.service.SmsService;
import tripai.recommend.system.service.UserInfoService;
import tripai.recommend.system.service.common.RecommenderCodeGenerate;
import tripai.recommend.system.util.DataMaskingUtil;
import tripai.recommend.system.util.JwtUtils;
import tripai.recommend.system.util.MailUtil;
import tripai.recommend.system.util.RedisKeyUtil;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: UserInfoServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/7/22 15:39
 * @version: 1.0
 */


@Service
@Slf4j
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {
    private final String REDIS_KEY = "hotel-supplier-system:userinfo:";
    @Resource
    private SmsService smsService;
    @Resource
    private SocialUserMapper socialUserMapper;
    @Resource
    private SocialUserAuthMapper socialUserAuthMapper;
    @Resource
    private RecommenderMapper recommenderMapper;
    @Resource
    private RecommenderBankMapper recommenderBankMapper;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    private AES mobileAes;
    private HMac passwordHmac;
    @Resource
    private RecommenderCodeGenerate recommenderCodeGenerate;
    @Resource
    private OssFileOpService ossFileOpService;

    @PostConstruct
    public void init() {
        mobileAes = new AES(Mode.CTS, Padding.PKCS5Padding,
                CryptoSecretConstants.AES_MOBILE_KEY.getBytes(),
                CryptoSecretConstants.AES_MOBILE_IV.getBytes());
        passwordHmac = new HMac(HmacAlgorithm.HmacSHA512, CryptoSecretConstants.HMAC_PASSWORD_KEY.getBytes());
    }

    @Override
    public ResponseResult<Boolean> sendBoundEmailCode(String email) {
        String code = RandomUtil.randomNumbers(6);
        String sub = "SomytripSMS供应商入驻验证码";
        String content = "您正在进行邮箱身份验证，验证码如下：\n" +
                code + "\n" +
                "验证码有效期10分钟，请在有效期内使用。\n" +
                "\n" +
                "感谢您使用SomytripSMS。";
        boolean sendResult = mailUtil.sendGeneraEmail(sub, content, email);
        if (sendResult) {
            String key = REDIS_KEY + "bind-email:" + email;
            stringRedisTemplate.opsForValue().set(key, code, 600, TimeUnit.SECONDS);
        }
        return ResponseResult.ok(sendResult);
    }

    @Override
    public ResponseResult<Boolean> editEmail(EditUserEmailVo vo, String token) {
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        UserInfo userInfo = getById(userInfoVo.getId());
        if (userInfo == null) {
            throw new BusinessException("403 Forbidden");
        }
        String key = REDIS_KEY + "bind-email:" + vo.getEmail();
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isBlank(value)) {
            throw new BusinessException("not found effective code");
        }
        if (!StrUtil.equals(vo.getVerificationCode(), value)) {
            throw new BusinessException("verification code error");
        }

        userInfo.setEmail(vo.getEmail());
        if (updateById(userInfo)) {
            stringRedisTemplate.delete(key);
            return ResponseResult.ok(true);
        }
        return ResponseResult.ok(false);
    }

    @Override
    public ResponseResult<Boolean> sendLoginSms(CaptchaCodeVo vo) {
        if (!smsService.verifyImageCode(vo.getCaptchaInfo())) {
            return ResponseResult.fail(400, "captcha code not found");
        }
        log.info("receive user mobile: {}", JSON.toJSONString(vo));
        SendSmsVo sendSmsVo = new SendSmsVo();
        sendSmsVo.setMobileInfo(vo.getMobileInfo());
        sendSmsVo.setBusinessCode(SmsBuisnessEnum.USER_LOGIN_OR_REGISTER);
        List<String> contents = ListUtil.of(RandomUtil.randomNumbers(6), "10");
        log.info("from {}, send login sms: {}", JSON.toJSONString(vo), JSON.toJSONString(contents));
        ResponseResult<Boolean> result = smsService.sendSms(sendSmsVo, contents);
        log.info("from {}, send login sms result: {}", JSON.toJSONString(vo), JSON.toJSONString(result));
        return result;
    }

    @Override
    public ResponseResult<Boolean> sendResetPasswordSms(CaptchaCodeVo vo) {
        if (!smsService.verifyImageCode(vo.getCaptchaInfo())) {
            return ResponseResult.fail(400, "captcha code not found");
        }
        SendSmsVo sendSmsVo = new SendSmsVo();
        sendSmsVo.setMobileInfo(vo.getMobileInfo());
        sendSmsVo.setBusinessCode(SmsBuisnessEnum.USER_RESET_PASSWORD);
        List<String> contents = ListUtil.of(RandomUtil.randomNumbers(6));
        log.info("send reset password sms: {}", JSON.toJSONString(vo));
        ResponseResult<Boolean> result = smsService.sendSms(sendSmsVo, contents);
        log.info("send reset password sms result: {}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public ResponseResult<LoginResultVo> login(AuthUser authUser) {
        LambdaQueryWrapper<SocialUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialUser::getUuid, authUser.getUuid())
                .eq(SocialUser::getSource, authUser.getSource())
        ;
        SocialUser socialUser = socialUserMapper.selectOne(queryWrapper);
        if (socialUser == null) {
            socialUser = new SocialUser();
            socialUser.setUuid(authUser.getUuid());
            socialUser.setSource(authUser.getSource());
            socialUser.setAccessToken(authUser.getToken().getAccessToken());
            socialUser.setExpiresIn(authUser.getToken().getExpireIn());
            socialUser.setRefreshToken(authUser.getToken().getRefreshToken());
            socialUser.setOpenId(authUser.getToken().getOpenId());
            socialUser.setUid(authUser.getToken().getUid());
            socialUser.setAccessCode(authUser.getToken().getAccessCode());
            socialUser.setUnionId(authUser.getToken().getUnionId());
            socialUser.setScope(authUser.getToken().getScope());
            socialUser.setTokenType(authUser.getToken().getTokenType());
            socialUser.setIdToken(authUser.getToken().getIdToken());
            socialUser.setMacAlgorithm(authUser.getToken().getMacAlgorithm());
            socialUser.setMacKey(authUser.getToken().getMacKey());
            socialUser.setCode(authUser.getToken().getCode());
            socialUser.setOauthToken(authUser.getToken().getOauthToken());
            socialUser.setOauthTokenSecret(authUser.getToken().getOauthTokenSecret());
            if (socialUserMapper.insert(socialUser) <= 0) {
                log.error("save social error: {}", JSON.toJSONString(authUser));
                throw new BusinessException("save social error");
            }
        }
        LambdaQueryWrapper<SocialUserAuth> socialUserAuthLambdaQueryWrapper = new LambdaQueryWrapper<>();
        socialUserAuthLambdaQueryWrapper.eq(SocialUserAuth::getSocialUserId, socialUser.getId());
        SocialUserAuth socialUserAuth = socialUserAuthMapper.selectOne(socialUserAuthLambdaQueryWrapper);
        if (socialUserAuth == null) {
            return ResponseResult.fail(306, "social is not bind channel");
        }
        UserInfo userInfo = getById(socialUserAuth.getUserId());
        return ResponseResult.ok(formatLoginResultVo(userInfo));
    }

    @Override
    public ResponseResult<Boolean> boundUser(AuthUser authUser, String token) {
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        UserInfo userInfo = getById(userInfoVo.getId());
        if (userInfo == null) {
            throw new BusinessException("user is not register");
        }
        LambdaQueryWrapper<SocialUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialUser::getUuid, authUser.getUuid())
                .eq(SocialUser::getSource, authUser.getSource())
        ;
        SocialUser socialUser = socialUserMapper.selectOne(queryWrapper);
        SocialUserAuth socialUserAuth;
        if (socialUser != null) {
            LambdaQueryWrapper<SocialUserAuth> socialUserAuthLambdaQueryWrapper = new LambdaQueryWrapper<>();
            socialUserAuthLambdaQueryWrapper.eq(SocialUserAuth::getSocialUserId, socialUser.getId());
            socialUserAuthLambdaQueryWrapper.eq(SocialUserAuth::getUserId, userInfo.getId());
            socialUserAuth = socialUserAuthMapper.selectOne(socialUserAuthLambdaQueryWrapper);
            if (socialUserAuth == null) {
                socialUserAuth = new SocialUserAuth();
                socialUserAuth.setUserId(userInfo.getId().toString());
                socialUserAuth.setSocialUserId(socialUser.getId().toString());
                if (socialUserAuthMapper.insert(socialUserAuth) <= 0) {
                    throw new BusinessException("social auth bound user error");
                }
            }
        } else {
            socialUser = new SocialUser();
            socialUser.setUuid(authUser.getUuid());
            socialUser.setSource(authUser.getSource());
            socialUser.setAccessToken(authUser.getToken().getAccessToken());
            socialUser.setExpiresIn(authUser.getToken().getExpireIn());
            socialUser.setRefreshToken(authUser.getToken().getRefreshToken());
            socialUser.setOpenId(authUser.getToken().getOpenId());
            socialUser.setUid(authUser.getToken().getUid());
            socialUser.setAccessCode(authUser.getToken().getAccessCode());
            socialUser.setUnionId(authUser.getToken().getUnionId());
            socialUser.setScope(authUser.getToken().getScope());
            socialUser.setTokenType(authUser.getToken().getTokenType());
            socialUser.setIdToken(authUser.getToken().getIdToken());
            socialUser.setMacAlgorithm(authUser.getToken().getMacAlgorithm());
            socialUser.setMacKey(authUser.getToken().getMacKey());
            socialUser.setCode(authUser.getToken().getCode());
            socialUser.setOauthToken(authUser.getToken().getOauthToken());
            socialUser.setOauthTokenSecret(authUser.getToken().getOauthTokenSecret());
            if (socialUserMapper.insert(socialUser) <= 0) {
                log.error("save social error: {}", JSON.toJSONString(authUser));
                throw new BusinessException("save social error");
            }
            socialUserAuth = new SocialUserAuth();
            socialUserAuth.setUserId(userInfo.getId().toString());
            socialUserAuth.setSocialUserId(socialUser.getId().toString());
            if (socialUserAuthMapper.insert(socialUserAuth) <= 0) {
                throw new BusinessException("social auth bound user error");
            }
        }
        return ResponseResult.ok(true);
    }

    @Override
    public ResponseResult<LoginResultVo> login(MobileLoginVo vo) {
        boolean checkCode = smsService.verifySmsCode(new SendSmsVo(vo.getMobileInfo(),
                        SmsBuisnessEnum.USER_LOGIN_OR_REGISTER, null),
                vo.getVerificationCode());
        if (!checkCode) {
            return ResponseResult.fail("verification code error");
        }
        String mobileAesEncrypt = mobileAesEncrypt(vo.getMobileInfo());
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getMobile, mobileAesEncrypt);
        UserInfo userInfo = getOne(queryWrapper);
        if (userInfo == null) {
            return ResponseResult.fail(500, "Not registered yet");
        }
        return ResponseResult.ok(formatLoginResultVo(userInfo));
    }

    @Override
    @Transactional(rollbackFor = {BusinessException.class})
    public ResponseResult<LoginResultVo> register(ScanQrRegisterVo vo) {
        boolean checkCode = smsService.verifySmsCode(new SendSmsVo(vo.getMobileInfo(),
                        SmsBuisnessEnum.USER_LOGIN_OR_REGISTER, null),
                vo.getVerificationCode());
        if (!checkCode) {
            log.info("register user check code fail: {}", JSON.toJSONString(vo));
            return ResponseResult.fail("verification code error");
        }
        String mobileAesEncrypt = mobileAesEncrypt(vo.getMobileInfo());
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getMobile, mobileAesEncrypt);
        UserInfo userInfo = getOne(queryWrapper);
        if (userInfo != null) {
            log.info("register user return: {}", JSON.toJSONString(userInfo));
            return ResponseResult.ok(formatLoginResultVo(userInfo));
        }
        userInfo = new UserInfo();
        userInfo.setMobile(mobileAesEncrypt);
        userInfo.setPassword("");
        userInfo.setRecommenderCode(recommenderCodeGenerate.generateRecommenderCode());
        userInfo.setNickName("SMS" + RandomUtil.randomNumbers(3));
        if (save(userInfo)) {
            log.info("register user save success: {}", JSON.toJSONString(userInfo));
            return ResponseResult.ok(formatLoginResultVo(userInfo));
        }
        throw new BusinessException("fail to register");
    }

    @Override
    public ResponseResult<LoginResultVo> login(UsernameLoginVo vo) {
        String mobileAesEncrypt = mobileAesEncrypt(vo.getMobileInfo());
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getMobile, mobileAesEncrypt);
        UserInfo userInfo = getOne(queryWrapper);
        if (userInfo == null) {
            return ResponseResult.fail(500, "Not registered yet");
        }
        String s = passwordHmac.digestHex(vo.getPassword());
        if (!StrUtil.equals(s, userInfo.getPassword())) {
            return ResponseResult.fail(500, "password error");
        }
        return ResponseResult.ok(formatLoginResultVo(userInfo));
    }

    @Override
    @Transactional(rollbackFor = {BusinessException.class})
    public ResponseResult<LoginResultVo> register(MobileRegisterVo vo) {
        boolean checkCode = smsService.verifySmsCode(new SendSmsVo(vo.getMobileInfo(),
                        SmsBuisnessEnum.USER_LOGIN_OR_REGISTER, null),
                vo.getVerificationCode());
        if (!checkCode) {
            return ResponseResult.fail("verification code error");
        }
        if (!StrUtil.equals(vo.getPassword(), vo.getConfirmPassword())) {
            return ResponseResult.fail("The password is incorrect");
        }
        String mobileAesEncrypt = mobileAesEncrypt(vo.getMobileInfo());
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getMobile, mobileAesEncrypt);
        UserInfo userInfo = getOne(queryWrapper);
        if (userInfo != null) {
            return ResponseResult.fail("This cell phone number has been registered");
        }
        userInfo = new UserInfo();
        userInfo.setMobile(mobileAesEncrypt);
        userInfo.setPassword(passwordHmac.digestHex(vo.getPassword()));
        userInfo.setRecommenderCode(recommenderCodeGenerate.generateRecommenderCode());
        userInfo.setNickName("SMS" + RandomUtil.randomNumbers(3));
        if (save(userInfo)) {
            return ResponseResult.ok(formatLoginResultVo(userInfo));
        }
        return ResponseResult.fail(500, "fail to register");
    }

    @Override
    public ResponseResult<Boolean> resetPassword(MobileRegisterVo vo) {
        log.info("reset password vo: {}", JSON.toJSONString(vo));
        if (!StrUtil.equals(vo.getPassword(), vo.getConfirmPassword())) {
            throw new BusinessException("密码不正确");
        }
        String mobileAesEncrypt = mobileAesEncrypt(vo.getMobileInfo());
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getMobile, mobileAesEncrypt);
        UserInfo userInfo = getOne(queryWrapper);
        if (userInfo == null) {
            return ResponseResult.fail(500, "尚未注册");
        }
        SendSmsVo sendSmsVo = new SendSmsVo();
        sendSmsVo.setMobileInfo(vo.getMobileInfo());
        sendSmsVo.setBusinessCode(SmsBuisnessEnum.USER_RESET_PASSWORD);
        List<String> sendSmsValue = smsService.getSendSmsValue(sendSmsVo);
        if (sendSmsValue.isEmpty()) {
            throw new RuntimeException("验证码未发送，请检查您的验证码");
        }
        if (!StrUtil.equals(sendSmsValue.get(0), vo.getVerificationCode())) {
            throw new BusinessException("验证码不正确");
        }
        String signPassword = passwordHmac.digestHex(vo.getPassword());
        if (StrUtil.equals(signPassword, userInfo.getPassword())) {
            throw new BusinessException("输入的密码与旧密码相同");
        }
        userInfo.setPassword(signPassword);
        return ResponseResult.ok(updateById(userInfo));
    }

    @Override
    public ResponseResult<Boolean> editNickname(String name, String token) {
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        UserInfo userInfo = getById(userInfoVo.getId());
        if (userInfo == null) {
            throw new BusinessException("user is not exist");
        }
        userInfo.setNickName(name);
        return ResponseResult.ok(updateById(userInfo));
    }

    @Override
    public ResponseResult<Boolean> editMobile(MobileLoginVo vo, String token) {
        boolean checkCode = smsService.verifySmsCode(new SendSmsVo(vo.getMobileInfo(),
                        SmsBuisnessEnum.USER_LOGIN_OR_REGISTER, null),
                vo.getVerificationCode());
        if (!checkCode) {
            return ResponseResult.fail("verification code error");
        }
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        UserInfo userInfoById = getById(userInfoVo.getId());
        String mobileAesEncrypt = mobileAesEncrypt(vo.getMobileInfo());
        if (StrUtil.equals(mobileAesEncrypt, userInfoById.getMobile())) {
            return ResponseResult.fail("Same cell phone number");
        }
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getMobile, mobileAesEncrypt);
        UserInfo userInfo = getOne(queryWrapper);
        if (userInfo != null) {
            return ResponseResult.fail(500, "The phone number is already tied to another user");
        }
        userInfoById.setMobile(mobileAesEncrypt);
        return ResponseResult.ok(updateById(userInfoById));
    }

    @Override
    public ResponseResult<String> editHeadImage(EditUserHeadImageVo vo, String token) {
        UserInfoVo userInfoVo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        UserInfo userInfo = getById(userInfoVo.getId());
        if (userInfo == null) {
            throw new BusinessException("user is not exist");
        }
        List<String> split = StrUtil.split(vo.getImage().getOriginalFilename(), StrUtil.C_DOT);
        if (split.isEmpty()) {
            return ResponseResult.fail(400, "file name parse error");
        }
        String suffix = split.get(split.size() - 1);
        String objectName = "user/sms-pic/" + userInfo.getRecommenderCode() + StrUtil.C_DOT + suffix;
        String bucket = "static";
        try {
            String url = ossFileOpService.internalCallUpload(vo.getImage().getInputStream(), bucket, objectName);
            userInfo.setHeadUrl(url);
            updateById(userInfo);
            return ResponseResult.ok(url);
        } catch (Exception e) {
            log.error("edit head image error", e);
        }
        return ResponseResult.fail("system error");
    }

    @Override
    public ResponseResult<Boolean> unbindEmailOrQrChannel(String token, int op) {
        UserInfoVo vo = JSON.parseObject(StrUtil.str(JwtUtils.parseTokenSubValue(token), StandardCharsets.UTF_8), UserInfoVo.class);
        log.info("user info unbind email or qr channel: {}, {}", JSON.toJSONString(vo), op);
        UserInfo userInfo = getById(vo.getId());
        if (userInfo == null) {
            throw new BusinessException("user is not exist");
        }
        switch (op) {
            case 0 -> {
                // 解绑邮箱
                userInfo.setEmail("");
                return ResponseResult.ok(updateById(userInfo));
            }
            case 1 -> {
                // 解绑微信渠道
                LambdaQueryWrapper<SocialUserAuth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(SocialUserAuth::getUserId, userInfo.getId());
                List<SocialUserAuth> socialUserAuths = socialUserAuthMapper.selectList(lambdaQueryWrapper);
                if (!socialUserAuths.isEmpty()) {
                    for (SocialUserAuth socialUserAuth : socialUserAuths) {
                        SocialUser socialUser = socialUserMapper.selectById(socialUserAuth.getSocialUserId());
                        if (EnumUtil.equals(AuthDefaultSource.WECHAT_OPEN, socialUser.getSource())) {
                            int result = socialUserAuthMapper.deleteById(socialUserAuth.getId());
                            return ResponseResult.ok(result > 0);
                        }
                    }
                }
                return ResponseResult.ok("not bind qr channel", true);
            }
            default -> {
                throw new BusinessException("unbind email or qr channel error");
            }
        }
    }

    /**
     * 手机号AES加密
     *
     * @param mobileInfo 手机号信息
     * @return
     */
    @Override
    public String mobileAesEncryptPublic(MobileInfo mobileInfo) {
        return mobileAesEncrypt(mobileInfo);
    }

    /**
     * 手机号AES解密
     *
     * @param phone 手机号信息
     * @return
     */
    @Override
    public MobileInfo mobileAesDecryptPublic(String phone) {
        return mobileAesDecrypt(phone);
    }


    /**
     * 获取用户个人中心信息
     *
     * @param userId 用户token
     * @return 个人中心信息
     */
    @Override
    public ResponseResult<UserProfileVo> getUserProfile(Long userId) {
        log.info("获取用户个人中心信息开始，userId：{}", userId);

        try {
            // 1. 判断用户ID是否存在
            if (userId == null) {
                log.error("获取用户个人中心信息失败，userId为空");
                return ResponseResult.fail("用户userId为空");
            }

            // 2. 查询用户基本信息
            UserInfo userInfo = this.getById(userId);
            if (userInfo == null) {
                log.error("获取用户个人中心信息失败，用户不存在，userId：{}", userId);
                return ResponseResult.fail("用户不存在");
            }

            // 3. 查询推荐方信息
            LambdaQueryWrapper<RecommenderInfo> recommenderWrapper = new LambdaQueryWrapper<>();
            recommenderWrapper.eq(RecommenderInfo::getUserId, userId)
                    .eq(RecommenderInfo::getIsDel, 0);
            RecommenderInfo recommenderInfo = recommenderMapper.selectOne(recommenderWrapper, false);

            // 4. 查询社交账户信息（微信绑定状态）
            LambdaQueryWrapper<SocialUser> socialWrapper = new LambdaQueryWrapper<>();
            socialWrapper.eq(SocialUser::getUid, userId)
                    .eq(SocialUser::getSource, AuthDefaultSource.WECHAT_OPEN);
            SocialUser wechatUser = socialUserMapper.selectOne(socialWrapper, false);

            // 5. 查询银行账户信息
            RecommenderBank bankInfo = null;
            if (recommenderInfo != null) {
                LambdaQueryWrapper<RecommenderBank> bankWrapper = new LambdaQueryWrapper<>();
                bankWrapper.eq(RecommenderBank::getRecommenderId, recommenderInfo.getId())
                        .eq(RecommenderBank::getIsDel, 0);
                bankInfo = recommenderBankMapper.selectOne(bankWrapper, false);
            }

            // 6. 构建返回结果
            UserProfileVo result = buildUserProfileVo(userInfo, recommenderInfo, wechatUser, bankInfo);

            log.info("获取用户个人中心信息成功，userId：{}", userId);
            return ResponseResult.ok(result);

        } catch (Exception e) {
            log.error("获取用户个人中心信息失败，userId：{}", userId, e);
            return ResponseResult.fail("获取用户个人中心信息失败");
        }
    }


    /**
     * 构建用户个人中心VO
     */
    private UserProfileVo buildUserProfileVo(UserInfo userInfo, RecommenderInfo recommenderInfo,
                                             SocialUser wechatUser, RecommenderBank bankInfo) {
        UserProfileVo vo = new UserProfileVo();

        vo.setUserId(userInfo.getId());

        if (recommenderInfo != null) {
            vo.setAvatarUrl(recommenderInfo.getAvatarUrl());
            vo.setName(recommenderInfo.getName());
            vo.setIdentityType(recommenderInfo.getType());
            vo.setInvitationCode(recommenderInfo.getInvitationCode());
            vo.setIdentifier(recommenderInfo.getIdentifier());
        }

        // 脱敏显示手机号和邮箱
        if (StrUtil.isNotBlank(userInfo.getMobile())) {
            vo.setMobile(DataMaskingUtil.maskMobile(userInfo.getMobile()));
        }
        if (StrUtil.isNotBlank(userInfo.getEmail())) {
            vo.setEmail(DataMaskingUtil.maskEmail(userInfo.getEmail()));
        }

        // 微信绑定状态
        if (wechatUser != null){
            vo.setWechatBindStatus(Objects.equals(wechatUser.getSource(), QrChannelBindEnum.BIND_WECHAT.getDesc()) ? 1 : 0);
        }

        // 银行卡信息
        if (bankInfo != null) {
            vo.setBankCardNo(DataMaskingUtil.maskBankCardNo(bankInfo.getBankCardNo()));
            vo.setBankName(bankInfo.getBankName());
            vo.setBankValidateStatus(bankInfo.getValidateStatus());
        }
        return vo;
    }


    private LoginResultVo formatLoginResultVo(UserInfo userInfo) {
        LoginResultVo vo = new LoginResultVo();
        UserInfoVo userInfoVo = new UserInfoVo();
        userInfoVo.setMobileInfo(mobileAesDecrypt(userInfo.getMobile()));
        userInfoVo.getMobileInfo().setMobile(DesensitizedUtil.mobilePhone(userInfoVo.getMobileInfo().getMobile()));
        userInfoVo.setId(userInfo.getId());
        userInfoVo.setRecommenderCode(userInfo.getRecommenderCode());
        userInfoVo.setNickName(userInfo.getNickName());
        userInfoVo.setGender(userInfo.getGender());
        userInfoVo.setHeadUrl(userInfo.getHeadUrl());
        userInfoVo.setCreateTime(userInfo.getCreateTime());
        userInfoVo.setEmail(DesensitizedUtil.email(userInfo.getEmail()));
        userInfoVo.setNeedSetPassword(StrUtil.isBlank(userInfo.getPassword()));
        userInfoVo.setQrChannelState(getUserQrChannelBindInfo(userInfoVo.getId()));
        if (StrUtil.isBlank(userInfoVo.getHeadUrl())) {
            userInfoVo.setHeadUrl(randomImageUrl());
        }
        vo.setUserInfo(userInfoVo);
        TokenInfo tokenInfo = new TokenInfo();
        String supplierCode = JwtUtils.generateToken(JSON.toJSONString(userInfoVo));
        //token 存放Redis
        String supplierCodeRedisKey = RedisKeyUtil.redisKeyReplace(HttpHeadConstant.RECOMMENDER_TOKEN_REDIS, supplierCode);
        stringRedisTemplate.opsForValue().set(supplierCodeRedisKey, supplierCode, HttpHeadConstant.TOKEN_EXPIRES_IN, TimeUnit.SECONDS);
        tokenInfo.setToken(supplierCode);
        tokenInfo.setExpireSecond(JwtUtils.getExpire());
        vo.setTokenInfo(tokenInfo);
        return vo;
    }

    private String randomImageUrl() {
        String url = "https://picsum.photos/200";
        HttpResponse response = HttpUtil.createGet(url).execute();
        String location = response.header("location");
        log.info("random url: {}", location);
        return location;
    }

    private QrChannelBindEnum getUserQrChannelBindInfo(Long uid) {
        LambdaQueryWrapper<SocialUserAuth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SocialUserAuth::getUserId, uid);
        List<SocialUserAuth> socialUserAuths = socialUserAuthMapper.selectList(lambdaQueryWrapper);
        if (socialUserAuths.isEmpty()) {
            return QrChannelBindEnum.NOT_BIND_WECHAT;
        }
        List<SocialUser> socialUsers = socialUserMapper.selectBatchIds(socialUserAuths.stream().map(SocialUserAuth::getSocialUserId).toList());
        for (SocialUser socialUser : socialUsers) {
            if (EnumUtil.equals(AuthDefaultSource.WECHAT_OPEN, socialUser.getSource())) {
                return QrChannelBindEnum.BIND_WECHAT;
            }
        }
        return QrChannelBindEnum.NOT_BIND_WECHAT;
    }

    private String mobileAesEncrypt(MobileInfo mobileInfo) {
        return mobileAes.encryptHex(mobileInfo.areaCodeMobile());
    }

    private MobileInfo mobileAesDecrypt(String content) {
        String dec = mobileAes.decryptStr(content);
        List<String> areaCodeMobile = StrUtil.split(dec, StrUtil.COMMA);
        return new MobileInfo(areaCodeMobile.get(0), areaCodeMobile.get(1));
    }
}

