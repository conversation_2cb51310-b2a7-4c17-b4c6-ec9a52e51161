package tripai.recommend.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderOrderQueryDto;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.enums.OrderTypeEnum;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderListVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderVo;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.mapper.recommender.RecommenderOrderMapper;
import tripai.recommend.system.service.RecommenderOrderService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: RecommenderOrderServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/7/24 18:20
 * @version: 1.0
 */

@Slf4j
@Service
public class RecommenderOrderServiceImpl implements RecommenderOrderService {

    @Resource
    private RecommenderOrderMapper recommenderOrderMapper;

    @Resource
    private RecommenderMapper recommenderMapper;


    /**
     * 根据用户ID查询推荐方订单列表
     *
     * @param userId   用户ID
     * @param queryDto 查询条件
     * @return 订单列表响应
     */
    @Override
    public ResponseResult<RecommenderOrderListVo> getRecommenderOrderListByUserId(Long userId, RecommenderOrderQueryDto queryDto) {
        log.info("根据用户ID查询推荐方订单列表开始，用户ID：{}，查询条件：{}", userId, queryDto);

        try {
            // 1. 参数验证
            if (userId == null) {
                log.error("根据用户ID查询推荐方订单列表失败，用户ID为空");
                return ResponseResult.fail("用户ID不能为空");
            }

            if (queryDto == null) {
                queryDto = new RecommenderOrderQueryDto();
            }

            // 2. 根据用户ID查询推荐方信息
            LambdaQueryWrapper<RecommenderInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RecommenderInfo::getUserId, userId)
                    .eq(RecommenderInfo::getIsDel, 0);
            RecommenderInfo recommenderInfo = recommenderMapper.selectOne(wrapper, false);

            if (recommenderInfo == null) {
                log.error("根据用户ID查询推荐方订单列表失败，推荐方不存在，用户ID：{}", userId);
                return ResponseResult.fail("推荐方不存在");
            }

            // 3. 设置推荐方ID并查询
            queryDto.setRecommenderId(recommenderInfo.getId());
            return getRecommenderOrderList(queryDto);

        } catch (Exception e) {
            log.error("根据用户ID查询推荐方订单列表失败，用户ID：{}，查询条件：{}", userId, queryDto, e);
            return ResponseResult.fail("查询订单列表失败");
        }
    }

    /**
     * 查询推荐方订单列表
     *
     * @param queryDto 查询条件
     * @return 订单列表响应
     */
    public ResponseResult<RecommenderOrderListVo> getRecommenderOrderList(RecommenderOrderQueryDto queryDto) {
        log.info("查询推荐方订单列表开始，查询条件：{}", queryDto);

        try {
            // 1. 参数验证
            if (queryDto == null) {
                log.error("查询推荐方订单列表失败，查询条件为空");
                return ResponseResult.fail("查询条件不能为空");
            }

            if (queryDto.getRecommenderId() == null) {
                log.error("查询推荐方订单列表失败，推荐方ID为空");
                return ResponseResult.fail("推荐方ID不能为空");
            }

            // 2. 验证推荐方是否存在
            RecommenderInfo recommenderInfo = recommenderMapper.selectById(queryDto.getRecommenderId());
            if (recommenderInfo == null) {
                log.error("查询推荐方订单列表失败，推荐方不存在，推荐方ID：{}", queryDto.getRecommenderId());
                return ResponseResult.fail("推荐方不存在");
            }

            // 3. 参数校验和处理
            validateAndProcessQueryParams(queryDto);

            // 4. 查询订单列表
            List<RecommenderOrderVo> orderList = queryOrderList(queryDto);

            // 5. 查询总数
            Long totalCount = queryOrderCount(queryDto);

            // 6. 查询分佣金额小计
            BigDecimal totalCommissionAmount = queryCommissionSum(queryDto);

            // 7. 构建响应结果
            RecommenderOrderListVo result = buildOrderListVo(orderList, totalCount, totalCommissionAmount, queryDto);

            log.info("查询推荐方订单列表成功，推荐方ID：{}，总数：{}", queryDto.getRecommenderId(), totalCount);
            return ResponseResult.ok(result);

        } catch (Exception e) {
            log.error("查询推荐方订单列表失败，查询条件：{}", queryDto, e);
            return ResponseResult.fail("查询订单列表失败");
        }
    }


    /**
     * 验证和处理查询参数
     */
    private void validateAndProcessQueryParams(RecommenderOrderQueryDto queryDto) {
        // 验证分页参数
        queryDto.validatePageParams();

        // 验证日期参数
        queryDto.validateDateParams();

        // 验证订单类型
        if (queryDto.getOrderType() != null && !OrderTypeEnum.isValidCode(queryDto.getOrderType())) {
            // 无效的订单类型设置为null（查询全部）
            queryDto.setOrderType(null);
        }

        // 处理关键词
        if (StrUtil.isNotBlank(queryDto.getKeyword())) {
            queryDto.setKeyword(queryDto.getKeyword().trim());
        }
    }

    /**
     * 查询订单列表
     */
    private List<RecommenderOrderVo> queryOrderList(RecommenderOrderQueryDto queryDto) {
        Integer orderType = queryDto.getOrderType();

        if (orderType == null) {
            // 查询全部订单
            return recommenderOrderMapper.selectRecommenderOrderList(queryDto);
        } else if (OrderTypeEnum.isHotelOrder(orderType)) {
            // 查询酒店订单
            return recommenderOrderMapper.selectHotelOrderList(queryDto);
        } else if (OrderTypeEnum.isTourGuideOrder(orderType)) {
            // 查询导游订单
            return recommenderOrderMapper.selectTourGuideOrderList(queryDto);
        } else {
            // 无效的订单类型，返回空列表
            return new ArrayList<>();
        }
    }

    /**
     * 查询订单总数
     */
    private Long queryOrderCount(RecommenderOrderQueryDto queryDto) {
        Integer orderType = queryDto.getOrderType();

        if (orderType == null) {
            // 查询全部订单总数
            return recommenderOrderMapper.selectRecommenderOrderCount(queryDto);
        } else if (OrderTypeEnum.isHotelOrder(orderType)) {
            // 查询酒店订单总数
            return recommenderOrderMapper.selectHotelOrderCount(queryDto);
        } else if (OrderTypeEnum.isTourGuideOrder(orderType)) {
            // 查询导游订单总数
            return recommenderOrderMapper.selectTourGuideOrderCount(queryDto);
        } else {
            // 无效的订单类型，返回0
            return 0L;
        }
    }

    /**
     * 查询分佣金额小计
     */
    private BigDecimal queryCommissionSum(RecommenderOrderQueryDto queryDto) {
        Integer orderType = queryDto.getOrderType();

        if (orderType == null) {
            // 查询全部订单分佣金额小计
            return recommenderOrderMapper.selectRecommenderOrderCommissionSum(queryDto);
        } else if (OrderTypeEnum.isHotelOrder(orderType)) {
            // 查询酒店订单分佣金额小计
            return recommenderOrderMapper.selectHotelOrderCommissionSum(queryDto);
        } else if (OrderTypeEnum.isTourGuideOrder(orderType)) {
            // 查询导游订单分佣金额小计
            return recommenderOrderMapper.selectTourGuideOrderCommissionSum(queryDto);
        } else {
            // 无效的订单类型，返回0
            return BigDecimal.ZERO;
        }
    }

    /**
     * 构建订单列表VO
     */
    private RecommenderOrderListVo buildOrderListVo(List<RecommenderOrderVo> orderList, Long totalCount,
                                                    BigDecimal totalCommissionAmount, RecommenderOrderQueryDto queryDto) {
        RecommenderOrderListVo result = new RecommenderOrderListVo();
        result.setOrders(orderList);
        result.setTotal(totalCount);
        result.setPageNum(queryDto.getPageNum());
        result.setPageSize(queryDto.getPageSize());
        result.setTotalCommissionAmount(totalCommissionAmount != null ? totalCommissionAmount : BigDecimal.ZERO);

        // 计算分页信息
        result.calculatePaginationInfo();

        return result;
    }
}
