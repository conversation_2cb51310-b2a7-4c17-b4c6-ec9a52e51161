package tripai.recommend.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.BankCardVerifyDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankAccountDto;
import tripai.recommend.system.domain.entity.RecommenderBank;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.enums.CertificateTypeEnum;
import tripai.recommend.system.domain.vo.BankCardVerifyResultVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderBankAccountVo;
import tripai.recommend.system.exception.BusinessException;
import tripai.recommend.system.mapper.recommender.RecommenderBankMapper;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.RecommenderBankService;
import tripai.recommend.system.service.TencentBankCardVerifyService;
import tripai.recommend.system.util.DataMaskingUtil;

import java.time.LocalDateTime;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service.impl
 * @className: RecommenderBankService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/24 10:46
 * @version: 1.0
 */
@Service
@Slf4j
public class RecommenderBankServiceImpl extends ServiceImpl<RecommenderBankMapper, RecommenderBank> implements RecommenderBankService {

    @Resource
    private RecommenderMapper recommenderMapper;
    @Resource
    private RecommenderBankMapper recommenderBankMapper;

    /**
     * 获取推荐方银行账户信息
     *
     * @param userId 用户id
     * @return 银行账户信息
     */
    @Override
    public ResponseResult<RecommenderBankAccountVo> getBankAccount(Long userId) {
        log.info("获取推荐方银行账户信息开始，userId：{}", userId);

        try {
            if (userId == null) {
                return ResponseResult.fail("用户userId不能为空");
            }

            // 2. 查询推荐方信息
            LambdaQueryWrapper<RecommenderInfo> recommenderWrapper = new LambdaQueryWrapper<>();
            recommenderWrapper.eq(RecommenderInfo::getUserId, userId)
                    .eq(RecommenderInfo::getIsDel, 0);
            RecommenderInfo recommenderInfo = recommenderMapper.selectOne(recommenderWrapper, false);

            if (recommenderInfo == null) {
                log.error("获取推荐方银行账户信息失败，推荐方信息不存在，userId：{}", userId);
                return ResponseResult.fail("推荐方信息不存在");
            }

            // 3. 查询银行账户信息
            LambdaQueryWrapper<RecommenderBank> bankWrapper = new LambdaQueryWrapper<>();
            bankWrapper.eq(RecommenderBank::getRecommenderId, recommenderInfo.getId())
                    .eq(RecommenderBank::getIsDel, 0);
            RecommenderBank bankInfo = recommenderBankMapper.selectOne(bankWrapper, false);

            if (bankInfo == null) {
                log.info("推荐方银行账户信息不存在，userId：{},recommenderId：{}", userId, recommenderInfo.getId());
                return ResponseResult.ok(null);
            }

            // 4. 构建返回结果
            RecommenderBankAccountVo result = buildBankAccountVo(bankInfo);

            log.info("获取推荐方银行账户信息成功，userId：{}", userId);
            return ResponseResult.ok(result);

        } catch (Exception e) {
            log.error("获取推荐方银行账户信息失败，userId：{}", userId, e);
            return ResponseResult.fail("获取银行账户信息失败");
        }
    }

    /**
     * 保存或更新银行账户信息
     *
     * @param dto 银行账户信息
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Long> saveBankAccount(RecommenderBankAccountDto dto) {
        log.info("保存银行账户信息开始，保存参数：{}", dto);

        try {
            // 1. 获取用户ID
            Long userId = dto.getUserId();
            if (userId == null) {
                log.error("保存银行账户信息失败，userId为空");
                return ResponseResult.fail("用户userId为空");
            }

            // 2. 查询推荐方信息
            LambdaQueryWrapper<RecommenderInfo> recommenderWrapper = new LambdaQueryWrapper<>();
            recommenderWrapper.eq(RecommenderInfo::getUserId, userId)
                    .eq(RecommenderInfo::getIsDel, 0);
            RecommenderInfo recommenderInfo = recommenderMapper.selectOne(recommenderWrapper, false);

            if (recommenderInfo == null) {
                log.error("保存银行账户信息失败，推荐方信息不存在，userId：{}", userId);
                return ResponseResult.fail("推荐方信息不存在");
            }

            // 3. 验证银行账户信息
            validateBankAccountInfo(dto, recommenderInfo.getType());

            // 4. 查询或创建银行账户信息
            RecommenderBank bankInfo;
            if (dto.getId() != null) {
                // 更新现有银行账户
                bankInfo = recommenderBankMapper.selectById(dto.getId());
                if (bankInfo == null || !bankInfo.getRecommenderId().equals(recommenderInfo.getId())) {
                    log.error("保存银行账户信息失败，银行账户不存在或不属于当前用户，bankId：{}", dto.getId());
                    return ResponseResult.fail("银行账户信息不存在");
                }
            } else {
                // 创建新的银行账户
                bankInfo = new RecommenderBank();
                bankInfo.setRecommenderId(recommenderInfo.getId());
            }

            // 5. 设置银行账户信息
            setBankAccountInfo(bankInfo, dto);

            // 6. 保存或更新银行账户信息
            boolean saveResult;
            if (dto.getId() != null) {
                saveResult = recommenderBankMapper.updateById(bankInfo) > 0;
            } else {
                saveResult = recommenderBankMapper.insert(bankInfo) > 0;
            }

            if (saveResult) {
                log.info("保存银行账户信息成功，userId：{}", userId);
                return ResponseResult.ok(bankInfo.getId());
            } else {
                log.error("保存银行账户信息失败，数据库操作失败，userId：{}", userId);
                return ResponseResult.fail("保存银行账户信息失败");
            }

        } catch (Exception e) {
            log.error("保存银行账户信息失败，userId：{}", dto.getUserId(), e);
            return ResponseResult.fail("保存银行账户信息失败");
        }
    }

    /**
     * 删除银行账户信息
     *
     * @param bankAccountId 银行账户ID
     * @param userId        用户userId
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Boolean> deleteBankAccount(Long bankAccountId, Long userId) {
        log.info("删除银行账户信息开始，bankAccountId：{}，token：{}", bankAccountId, userId);

        try {
            // 1. 判断用户ID是否为空
            if (userId == null) {
                log.error("删除银行账户信息失败，userId为空");
                return ResponseResult.fail("用户userId为空");
            }

            // 2. 查询推荐方信息
            LambdaQueryWrapper<RecommenderInfo> recommenderWrapper = new LambdaQueryWrapper<>();
            recommenderWrapper.eq(RecommenderInfo::getUserId, userId)
                    .eq(RecommenderInfo::getIsDel, 0);
            RecommenderInfo recommenderInfo = recommenderMapper.selectOne(recommenderWrapper, false);

            if (recommenderInfo == null) {
                log.error("删除银行账户信息失败，推荐方信息不存在，userId：{}", userId);
                return ResponseResult.fail("推荐方信息不存在");
            }

            // 3. 查询银行账户信息
            RecommenderBank bankInfo = recommenderBankMapper.selectById(bankAccountId);
            if (bankInfo == null || !bankInfo.getRecommenderId().equals(recommenderInfo.getId())) {
                log.error("删除银行账户信息失败，银行账户不存在或不属于当前用户，bankAccountId：{}", bankAccountId);
                return ResponseResult.fail("银行账户信息不存在");
            }

            // 4. 检查银行账户状态，只有审核驳回的记录才能删除
            if (bankInfo.getValidateStatus() != 4) {
                log.error("删除银行账户信息失败，只能删除审核驳回的记录，当前状态：{}", bankInfo.getValidateStatus());
                return ResponseResult.fail("只能删除审核驳回的银行账户记录");
            }

            // 5. 逻辑删除银行账户信息
            bankInfo.setIsDel(true);
            int deleteResult = recommenderBankMapper.updateById(bankInfo);

            if (deleteResult > 0) {
                log.info("删除银行账户信息成功，bankAccountId：{}", bankAccountId);
                return ResponseResult.ok(true);
            } else {
                log.error("删除银行账户信息失败，数据库操作失败，bankAccountId：{}", bankAccountId);
                return ResponseResult.fail("删除银行账户信息失败");
            }

        } catch (Exception e) {
            log.error("删除银行账户信息失败，bankAccountId：{}，userId：{}", bankAccountId, userId, e);
            return ResponseResult.fail("删除银行账户信息失败");
        }
    }

    /**
     * 构建银行账户VO
     */
    private RecommenderBankAccountVo buildBankAccountVo(RecommenderBank bankInfo) {
        RecommenderBankAccountVo vo = new RecommenderBankAccountVo();

        vo.setId(bankInfo.getId());
        vo.setAccountName(bankInfo.getAccountName());
        vo.setAccountCertificateType(bankInfo.getAccountCertificateType());
        vo.setAccountIdentifier(bankInfo.getAccountIdentifier());
        vo.setAccountPhone(bankInfo.getAccountPhone());
        vo.setBankCardNo(DataMaskingUtil.maskBankCardNo(bankInfo.getBankCardNo()));
        vo.setBankName(bankInfo.getBankName());
        vo.setBankCode(bankInfo.getBankCode());
        vo.setBranchCode(bankInfo.getBranchCode());
        vo.setBranchName(bankInfo.getBranchName());
        vo.setProvince(bankInfo.getProvince());
        vo.setCity(bankInfo.getCity());
        vo.setBankAddress(bankInfo.getBankAddress());
        vo.setValidateStatus(bankInfo.getValidateStatus());
        vo.setAuditSubmitTime(bankInfo.getAuditSubmitTime());
        vo.setIsDraft(bankInfo.getIsDraft());
        vo.setCreateTime(bankInfo.getCreateTime());
        vo.setUpdateTime(bankInfo.getUpdateTime());

        return vo;
    }


    /**
     * 验证银行账户信息
     */
    private void validateBankAccountInfo(RecommenderBankAccountDto dto, Integer recommenderType) {
        // 验证开户名
        if (StrUtil.isBlank(dto.getAccountName())) {
            throw new BusinessException("开户名不能为空");
        }

        // 验证开户证件信息
        if (recommenderType == 1) {
            // 个人类型需要验证开户证件类型和证件号
            if (dto.getAccountCertificateType() == null) {
                throw new BusinessException("开户证件类型不能为空");
            }
            if (!CertificateTypeEnum.isValidCode(dto.getAccountCertificateType())) {
                throw new BusinessException("开户证件类型无效");
            }
            if (StrUtil.isBlank(dto.getAccountIdentifier())) {
                throw new BusinessException("开户证件号不能为空");
            }
        } else if (recommenderType == 2) {
            // 企业类型验证统一社会信用代码
            if (StrUtil.isBlank(dto.getAccountIdentifier())) {
                throw new BusinessException("开户证件号（统一社会信用代码）不能为空");
            }
        }

        // 验证其他必填字段
        if (StrUtil.isBlank(dto.getAccountPhone())) {
            throw new BusinessException("开户手机号不能为空");
        }
        if (StrUtil.isBlank(dto.getBankCardNo())) {
            throw new BusinessException("银行卡号不能为空");
        }
        if (StrUtil.isBlank(dto.getBankName())) {
            throw new BusinessException("开户银行不能为空");
        }
        if (StrUtil.isBlank(dto.getProvince())) {
            throw new BusinessException("银行所在省份不能为空");
        }
        if (StrUtil.isBlank(dto.getCity())) {
            throw new BusinessException("银行所在城市不能为空");
        }
        if (StrUtil.isBlank(dto.getBranchName())) {
            throw new BusinessException("开户支行不能为空");
        }
    }

    /**
     * 设置银行账户信息
     */
    private void setBankAccountInfo(RecommenderBank bankInfo, RecommenderBankAccountDto dto) {
        bankInfo.setAccountName(dto.getAccountName());
        bankInfo.setAccountCertificateType(dto.getAccountCertificateType());
        bankInfo.setAccountIdentifier(dto.getAccountIdentifier());
        bankInfo.setAccountPhone(dto.getAccountPhone());
        bankInfo.setBankCardNo(dto.getBankCardNo());
        bankInfo.setBankName(dto.getBankName());
        bankInfo.setBankCode(dto.getBankCode());
        bankInfo.setBranchCode(dto.getBranchCode());
        bankInfo.setBranchName(dto.getBranchName());
        bankInfo.setProvince(dto.getProvince());
        bankInfo.setCity(dto.getCity());
        bankInfo.setBankAddress(dto.getBankAddress());

        // 重新提交审核时重置状态
        // 未验证
        bankInfo.setValidateStatus(0);
        // 已提交审核
        bankInfo.setIsDraft(false);
        bankInfo.setAuditSubmitTime(LocalDateTime.now());
    }

}
