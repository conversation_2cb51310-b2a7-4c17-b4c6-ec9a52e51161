package tripai.recommend.system.service;

import org.springframework.web.multipart.MultipartFile;
import tripai.recommend.system.domain.vo.CertificateVo;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service
 * @className: CertificateOcrService
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/30 11:56
 * @version: 1.0
 */
public interface CertificateOcrService {
    /**
     * 识别证件信息
     *
     * @param type 证件类型 1：身份证 2：护照
     * @param file 护照图片
     * @return 证件信息
     */
    CertificateVo recognizeCertificate(Integer type, MultipartFile file);
}

