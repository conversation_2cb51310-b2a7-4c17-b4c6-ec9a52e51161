package tripai.recommend.system.service;

import tripai.recommend.system.domain.dto.BankCardVerifyDto;
import tripai.recommend.system.domain.vo.BankCardVerifyResultVo;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.service
 * @className: TencentBankCardVerifyService
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/7/30 11:37
 * @version: 1.0
 */

public interface TencentBankCardVerifyService {

    /**
     * 银行卡三要素核验
     *
     * @param verifyDto 核验请求参数
     * @return 核验结果
     */
    BankCardVerifyResultVo verifyBankCard(BankCardVerifyDto verifyDto);

    /**
     * 银行卡三要素核验（带频率限制）
     *
     * @param token 用户认证token
     * @param verifyDto 核验请求参数
     * @return 核验结果
     */
    BankCardVerifyResultVo verifyBankCardWithRateLimit(String token, BankCardVerifyDto verifyDto);
}
