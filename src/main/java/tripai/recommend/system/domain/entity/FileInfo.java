package tripai.recommend.system.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.entity
 * @className: FileInfoVo
 * @author: lijunqi
 * @description:
 * @date: 2025/7/23 14:10
 * @version: 1.0
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileInfo {

    /**
     * 桶
     */
    private String bucket;

    /**
     * 名称
     */
    private String name;

    /**
     * url
     */
    private String url;

    /**
     * 文件类型
     * 1-图片, 2-视频
     */
    private Integer type;

    public FileInfo(String bucket, String name, String url) {
        this.bucket = bucket;
        this.name = name;
        this.url = url;
    }
}

