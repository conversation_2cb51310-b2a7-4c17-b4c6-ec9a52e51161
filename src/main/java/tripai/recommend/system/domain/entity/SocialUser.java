package tripai.recommend.system.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.entity
 * @className: SocialUser
 * @author: lijunqi
 * @description:
 * @date: 2025/7/22 15:47
 * @version: 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("somytrip_hotel_supplier.social_user")
public class SocialUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键，id自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 第三方系统的唯一ID
     */
    private String uuid;

    /**
     * 第三方用户来源
     */
    private String source;

    /**
     * 第三方用户授予的权限
     */
    private String scope;

    /**
     * 用户的授权令牌
     */
    private String accessToken;

    /**
     * 第三方用户的授权令牌的有效期
     */
    private Integer expiresIn;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 第三方用户的 open id
     */
    private String openId;

    /**
     * 第三方用户的 ID
     */
    private String uid;

    /**
     * 个别平台的授权信息
     */
    private String accessCode;

    /**
     * 第三方用户的 union id
     */
    private String unionId;

    /**
     * 个别平台的授权信息
     */
    private String tokenType;

    /**
     * id token
     */
    private String idToken;

    /**
     * 小米平台用户的附带属性
     */
    private String macAlgorithm;

    /**
     * 小米平台用户的附带属性
     */
    private String macKey;

    /**
     * 用户的授权code
     */
    private String code;

    /**
     * Twitter平台用户的附带属性
     */
    private String oauthToken;

    /**
     * Twitter平台用户的附带属性
     */
    private String oauthTokenSecret;
}
