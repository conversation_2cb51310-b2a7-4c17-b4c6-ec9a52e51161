package tripai.recommend.system.domain.vo;

import lombok.Data;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo
 * @className: BankCardVerifyResultVo
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 11:38
 * @version: 1.0
 */

@Data
public class BankCardVerifyResultVo {

    /**
     * 核验结果：0-一致，1-不一致，2-无记录
     */
    private Integer result;

    /**
     * 核验描述
     */
    private String description;

    /**
     * 是否核验成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    public static BankCardVerifyResultVo success(Integer result, String description) {
        BankCardVerifyResultVo vo = new BankCardVerifyResultVo();
        vo.setResult(result);
        vo.setDescription(description);
        vo.setSuccess(true);
        return vo;
    }

    public static BankCardVerifyResultVo fail(String errorMessage) {
        BankCardVerifyResultVo vo = new BankCardVerifyResultVo();
        vo.setSuccess(false);
        vo.setErrorMessage(errorMessage);
        return vo;
    }
}
