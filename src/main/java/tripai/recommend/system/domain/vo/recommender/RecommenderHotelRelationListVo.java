package tripai.recommend.system.domain.vo.recommender;

import lombok.Data;

import java.util.List;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo.recommender
 * @className: RecommenderHotelRelationListVo
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/25 16:59
 * @version: 1.0
 */


@Data
public class RecommenderHotelRelationListVo {

    /**
     * 推荐方酒店关系列表
     */
    private List<RecommenderHotelRelationVo> relations;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 排序类型：1-建立时间从新到旧（默认）、2-上架服务数从高到低、3-成单数从高到低
     */
    private Integer sortType;

    /**
     * 排序描述
     */
    private String sortDescription;

    /**
     * 统计信息
     */
    private RecommenderHotelRelationStatisticsVo statistics;

    /**
     * 计算总页数
     */
    public void calculateTotalPages() {
        if (total != null && pageSize != null && pageSize > 0) {
            this.totalPages = (int) Math.ceil((double) total / pageSize);
        } else {
            this.totalPages = 0;
        }
    }

    /**
     * 计算是否有下一页
     */
    public void calculateHasNext() {
        if (pageNum != null && totalPages != null) {
            this.hasNext = pageNum < totalPages;
        } else {
            this.hasNext = false;
        }
    }

    /**
     * 计算是否有上一页
     */
    public void calculateHasPrevious() {
        if (pageNum != null) {
            this.hasPrevious = pageNum > 1;
        } else {
            this.hasPrevious = false;
        }
    }

    /**
     * 自动计算所有分页相关字段
     */
    public void calculatePaginationInfo() {
        calculateTotalPages();
        calculateHasNext();
        calculateHasPrevious();
    }

    /**
     * 推荐方酒店关系统计信息内部类
     */
    @Data
    public static class RecommenderHotelRelationStatisticsVo {

        /**
         * 总关系数量
         */
        private Long totalRelationCount;

        /**
         * 总酒店数量
         */
        private Long totalHotelCount;

        /**
         * 总上线房型数量
         */
        private Long totalOnlineRoomCount;

        /**
         * 总成单数量
         */
        private Long totalOrderCount;

        /**
         * 正常关系数量
         */
        private Long activeRelationCount;

        /**
         * 暂停关系数量
         */
        private Long suspendRelationCount;

        /**
         * 平均每个关系的房型数量
         */
        private Double avgRoomCountPerRelation;

        /**
         * 平均每个关系的成单数量
         */
        private Double avgOrderCountPerRelation;
    }
}

