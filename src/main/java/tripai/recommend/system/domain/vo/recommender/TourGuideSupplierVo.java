package tripai.recommend.system.domain.vo.recommender;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo.recommender
 * @className: TourGuideSupplierVo
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/28 14:03
 * @version: 1.0
 */

@Data
public class TourGuideSupplierVo {

    /**
     * 关系ID
     */
    private Long relationId;

    /**
     * 推荐方ID
     */
    private Long recommenderId;

    /**
     * 导游ID（业务ID）
     */
    private Long guideId;

    /**
     * 导游姓名（来自身份证）
     */
    private String guideName;

    /**
     * 手机号（脱敏显示）
     */
    private String phone;

    /**
     * 所属产线：1=司机，2=导游，3=司兼导
     */
    private Integer productionLine;

    /**
     * 产线名称
     */
    private String productionLineName;

    /**
     * 服务分
     */
    private BigDecimal serviceScore;

    /**
     * 管控状态：0=正常，1=月度管控中，2=永久管控中，3=手动管控中
     */
    private Integer controlStatus;

    /**
     * 管控状态名称
     */
    private String controlStatusName;

    /**
     * 上架服务数
     */
    private Integer serviceCount;

    /**
     * 建立关系时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime relationCreateTime;

    /**
     * 成单数（建立关系以来已结算的订单数量）
     */
    private Integer orderCount;

    /**
     * 关系状态：1=正常，2=管控/暂停
     */
    private Integer relationStatus;


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 审核状态：0=待审核，1=审核通过，2=审核未通过
     */
    private Integer auditStatus;

    /**
     * 审核状态名称
     */
    private String auditStatusName;

    /**
     * 服务城市
     */
    private String serviceCities;

    /**
     * 主要服务城市
     */
    private String primaryServiceCity;

    /**
     * 服务语言
     */
    private String language;

    /**
     * 个人简介
     */
    private String introduction;

    /**
     * 本月已拒单次数
     */
    private Integer rejectionCount;

    /**
     * 当前拒单计数对应月份
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime rejectCountMonth;

    /**
     * 业务类型（固定为2，表示导游）
     */
    private Integer bizType = 2;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商昵称
     */
    private String supplierNickName;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 性别：M=男，F=女
     */
    private String gender;

    /**
     * 性别名称
     */
    private String genderName;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 从业年限
     */
    private Integer workingYears;

    /**
     * 证件类型：1=身份证，2=护照，3=其他
     */
    private Integer certificateType;

    /**
     * 证件号码（脱敏显示）
     */
    private String certificateNumber;

    /**
     * 导游证号
     */
    private String guideCardNumber;

    /**
     * 导游证有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime guideCardExpiry;

    /**
     * 驾驶证号（司机/司兼导）
     */
    private String drivingLicenseNumber;

    /**
     * 驾驶证有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime drivingLicenseExpiry;

    /**
     * 车辆信息（司机/司兼导）
     */
    private String vehicleInfo;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 紧急联系人
     */
    private String emergencyContact;

    /**
     * 紧急联系人电话
     */
    private String emergencyPhone;

    /**
     * 银行账户信息
     */
    private String bankAccount;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 账户状态：1=正常，2=冻结，3=注销
     */
    private Integer accountStatus;

    /**
     * 账户状态名称
     */
    private String accountStatusName;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    /**
     * 最后活跃时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastActiveTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 标签（JSON格式）
     */
    private String tags;

    /**
     * 评价等级：1-5星
     */
    private Integer rating;

    /**
     * 评价数量
     */
    private Integer reviewCount;

    /**
     * 好评率
     */
    private BigDecimal positiveRate;

    /**
     * 月度收入
     */
    private BigDecimal monthlyIncome;

    /**
     * 累计收入
     */
    private BigDecimal totalIncome;

    /**
     * 是否推荐：0=否，1=是
     */
    private Integer isRecommended;

    /**
     * 推荐理由
     */
    private String recommendReason;


}