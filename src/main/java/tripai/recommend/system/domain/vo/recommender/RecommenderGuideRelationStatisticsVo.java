package tripai.recommend.system.domain.vo.recommender;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo.recommender
 * @className: RecommenderGuideRelationStatisticsVo
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/7/28 16:12
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecommenderGuideRelationStatisticsVo {

    /**
     * 导游总数
     */
    private Long totalGuideCount;

    /**
     * 正常状态导游数
     */
    private Long normalGuideCount;

    /**
     * 管控状态导游数
     */
    private Long controlledGuideCount;

    /**
     * 司机数量
     */
    private Long driverCount;

    /**
     * 导游数量
     */
    private Long guideCount;

    /**
     * 司兼导数量
     */
    private Long driverGuideCount;

    /**
     * 总服务数
     */
    private Long totalServiceCount;

    /**
     * 总订单数
     */
    private Long totalOrderCount;

    /**
     * 平均服务分
     */
    private BigDecimal averageServiceScore;

    /**
     * 最高服务分
     */
    private BigDecimal maxServiceScore;

    /**
     * 最低服务分
     */
    private BigDecimal minServiceScore;

    /**
     * 本月新增导游数
     */
    private Long monthlyNewGuideCount;

    /**
     * 本周新增导游数
     */
    private Long weeklyNewGuideCount;

    /**
     * 今日新增导游数
     */
    private Long dailyNewGuideCount;
}