package tripai.recommend.system.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo
 * @className: IdCardInfoVo
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/30 14:10
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class IdCardInfoVo extends CertificateVo {
    private String address;
}
