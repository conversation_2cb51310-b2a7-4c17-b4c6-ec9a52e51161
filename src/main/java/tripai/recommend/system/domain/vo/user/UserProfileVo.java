package tripai.recommend.system.domain.vo.user;

import lombok.Data;
import tripai.recommend.system.domain.entity.FileInfo;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo.user
 * @className: UserProfileVo
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/24 9:47
 * @version: 1.0
 */

@Data
public class UserProfileVo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 头像（可修改）
     */
    private String avatarUrl;

    /**
     * 姓名（个人=身份证姓名；企业=企业全称，不可修改）
     */
    private String name;

    /**
     * 身份类型：1=个人 2=企业
     */
    private Integer identityType;


    /**
     * 推荐方邀请码（认证通过后自动生成）
     */
    private String invitationCode;

    /**
     * 身份证号/统一社会信用代码
     */
    private String identifier;

    /**
     * 手机号（脱敏显示）
     */
    private String mobile;

    /**
     * 邮箱（脱敏显示）
     */
    private String email;

    /**
     * 微信绑定状态：0-未绑定、1-已绑定
     */
    private Integer wechatBindStatus;


    /**
     * 银行卡号（脱敏显示）
     */
    private String bankCardNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行卡验证状态：0=未验证 1=三要素失败 2=待人工审核 3=审核通过 4=审核驳回
     */
    private Integer bankValidateStatus;

}
