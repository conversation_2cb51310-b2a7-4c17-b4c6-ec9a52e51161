package tripai.recommend.system.domain.vo.recommender;

import lombok.Data;

import java.util.List;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo.recommender
 * @className: RecommenderGuideRelationListVo
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/25 17:33
 * @version: 1.0
 */
@Data
public class RecommenderGuideRelationListVo {

    /**
     * 导游供应商列表
     */
    private List<TourGuideSupplierVo> suppliers;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 排序类型
     */
    private String sortType;


    /**
     * 统计信息
     */
    private RecommenderGuideRelationStatisticsVo statistics;

    /**
     * 计算总页数
     */
    public void calculateTotalPages() {
        if (total != null && pageSize != null && pageSize > 0) {
            this.totalPages = (int) Math.ceil((double) total / pageSize);
        } else {
            this.totalPages = 0;
        }
    }

    /**
     * 计算是否有下一页
     */
    public void calculateHasNext() {
        if (pageNum != null && totalPages != null) {
            this.hasNext = pageNum < totalPages;
        } else {
            this.hasNext = false;
        }
    }

    /**
     * 计算是否有上一页
     */
    public void calculateHasPrevious() {
        if (pageNum != null) {
            this.hasPrevious = pageNum > 1;
        } else {
            this.hasPrevious = false;
        }
    }

    /**
     * 自动计算所有分页相关字段
     */
    public void calculatePaginationInfo() {
        calculateTotalPages();
        calculateHasNext();
        calculateHasPrevious();
    }


    /**
     * 判断是否为空列表
     */
    public boolean isEmpty() {
        return suppliers == null || suppliers.isEmpty();
    }

    /**
     * 获取当前页的记录数
     */
    public int getCurrentPageSize() {
        return suppliers != null ? suppliers.size() : 0;
    }

    /**
     * 判断是否为第一页
     */
    public boolean isFirstPage() {
        return pageNum != null && pageNum == 1;
    }

    /**
     * 判断是否为最后一页
     */
    public boolean isLastPage() {
        return pageNum != null && totalPages != null && pageNum.equals(totalPages);
    }

    /**
     * 获取起始记录号
     */
    public int getStartRecord() {
        if (pageNum == null || pageSize == null) {
            return 0;
        }
        return (pageNum - 1) * pageSize + 1;
    }

    /**
     * 获取结束记录号
     */
    public int getEndRecord() {
        if (pageNum == null || pageSize == null || total == null) {
            return 0;
        }
        int endRecord = pageNum * pageSize;
        return Math.min(endRecord, total.intValue());
    }

    /**
     * 构建分页信息描述
     */
    public String getPaginationDescription() {
        if (total == null || total == 0) {
            return "暂无数据";
        }
        return String.format("第 %d-%d 条，共 %d 条记录，第 %d/%d 页",
                getStartRecord(), getEndRecord(), total, pageNum, totalPages);
    }

}
