package tripai.recommend.system.domain.vo;

import lombok.Data;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo
 * @className: CertificateVo
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 14:08
 * @version: 1.0
 */

@Data
public class CertificateVo {

    /**
     * 证件编号，身份证号码或导游证号码或驾驶证号
     */
    private String certificateNumber;

    /**
     * 身份证上的姓名或驾驶证名字
     */
    private String name;

    /**
     * 性别：0-未知，1-男，2-女
     */
    private String gender;

    /**
     * 出生日期
     */
    private String birthday;

}