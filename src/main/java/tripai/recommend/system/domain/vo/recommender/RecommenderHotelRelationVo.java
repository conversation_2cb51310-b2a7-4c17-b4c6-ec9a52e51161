package tripai.recommend.system.domain.vo.recommender;

import lombok.Data;
import tripai.recommend.system.domain.vo.user.MobileInfo;
import tripai.recommend.system.util.MobileCryptoUtil;

import java.time.LocalDateTime;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo.recommender
 * @className: RecommenderHotelRelationVo
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/7/25 17:11
 * @version: 1.0
 */

@Data
public class RecommenderHotelRelationVo {

    /**
     * 关系ID
     */
    private Long relationId;

    /**
     * 推荐方ID
     */
    private Long recommenderId;

    /**
     * 酒店ID（业务ID）
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店供应商编码
     */
    private String hotelSupplierCode;

    /**
     * 供应商手机号
     */
    private String supplierMobile;

    /**
     * 供应商昵称
     */
    private String supplierNickName;

    /**
     * 已上线房型数量
     */
    private Integer onlineRoomCount;

    /**
     * 建立关系时间
     */
    private LocalDateTime relationCreateTime;

    /**
     * 成单数量（建立关系以来佣金已结算的订单数量）
     */
    private Integer orderCount;

    /**
     * 关系状态：1=正常，2=管控/暂停
     */
    private Integer relationStatus;

    /**
     * 酒店状态：0-草稿; 1-审核中; 2-审核失败; 3-未上线;  4-已上线;
     */
    private Integer hotelStatus;

    /**
     * 酒店地址
     */
    private String hotelAddress;

    /**
     * 酒店星级
     */
    private Integer starRate;

    /**
     * 酒店评分
     */
    private String score;

    /**
     * 酒店所在城市
     */
    private String cityName;

    /**
     * 业务类型（固定为1，表示酒店）
     */
    private Integer bizType = 1;

}
