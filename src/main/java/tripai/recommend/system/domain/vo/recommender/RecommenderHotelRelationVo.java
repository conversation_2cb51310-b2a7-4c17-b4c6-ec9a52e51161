package tripai.recommend.system.domain.vo.recommender;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.vo.recommender
 * @className: RecommenderHotelRelationVo
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/7/25 17:11
 * @version: 1.0
 */

@Data
public class RecommenderHotelRelationVo {

    /**
     * 关系ID
     */
    private Long relationId;

    /**
     * 推荐方ID
     */
    private Long recommenderId;

    /**
     * 酒店ID（业务ID）
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店供应商编码
     */
    private String hotelSupplierCode;

    /**
     * 供应商手机号
     */
    private String supplierMobile;

    /**
     * 供应商昵称
     */
    private String supplierNickName;

    /**
     * 已上线房型数量
     */
    private Integer onlineRoomCount;

    /**
     * 建立关系时间
     */
    private LocalDateTime relationCreateTime;

    /**
     * 成单数量（建立关系以来佣金已结算的订单数量）
     */
    private Integer orderCount;

    /**
     * 关系状态：1=正常，2=管控/暂停
     */
    private Integer relationStatus;

    /**
     * 关系状态描述
     */
    private String relationStatusDesc;

    /**
     * 酒店状态
     */
    private Integer hotelStatus;

    /**
     * 酒店状态描述
     */
    private String hotelStatusDesc;

    /**
     * 酒店地址
     */
    private String hotelAddress;

    /**
     * 酒店星级
     */
    private Integer starRate;

    /**
     * 酒店评分
     */
    private String score;

    /**
     * 酒店所在城市
     */
    private String cityName;

    /**
     * 业务类型（固定为1，表示酒店）
     */
    private Integer bizType = 1;

    /**
     * 设置关系状态并自动设置描述
     */
    public void setRelationStatus(Integer relationStatus) {
        this.relationStatus = relationStatus;
        if (relationStatus != null) {
            switch (relationStatus) {
                case 1:
                    this.relationStatusDesc = "正常";
                    break;
                case 2:
                    this.relationStatusDesc = "管控/暂停";
                    break;
                default:
                    this.relationStatusDesc = "未知";
                    break;
            }
        }
    }

    /**
     * 设置酒店状态并自动设置描述
     */
    public void setHotelStatus(Integer hotelStatus) {
        this.hotelStatus = hotelStatus;
        if (hotelStatus != null) {
            switch (hotelStatus) {
                case 0:
                    this.hotelStatusDesc = "草稿";
                    break;
                case 1:
                    this.hotelStatusDesc = "审核中";
                    break;
                case 2:
                    this.hotelStatusDesc = "审核失败";
                    break;
                case 3:
                    this.hotelStatusDesc = "未上线";
                    break;
                case 4:
                    this.hotelStatusDesc = "已上线";
                    break;
                default:
                    this.hotelStatusDesc = "未知";
                    break;
            }
        }
    }
}
