package tripai.recommend.system.domain.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.dto
 * @className: BankCardVerifyDto
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/30 11:38
 * @version: 1.0
 */

@Data
public class BankCardVerifyDto {

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空")
    private String bankCardNumber;

    /**
     * 持卡人姓名
     */
    @NotBlank(message = "持卡人姓名不能为空")
    private String cardholderName;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    private String idNumber;
}
