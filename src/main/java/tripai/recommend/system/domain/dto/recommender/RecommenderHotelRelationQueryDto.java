package tripai.recommend.system.domain.dto.recommender;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.dto.recommender
 * @className: RecommenderHotelRelationQueryDto
 * @author: lijunqi
 * @description:
 * @date: 2025/7/25 17:00
 * @version: 1.0
 */

@Data
public class RecommenderHotelRelationQueryDto {

    /**
     * 推荐方ID
     */
    private Long recommenderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 建立关系开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate relationStartDate;

    /**
     * 建立关系结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate relationEndDate;

    /**
     * 关键词搜索（供应商编码、酒店名称、供应商姓名）
     */
    private String keyword;

    /**
     * 排序类型（默认按建立关系时间降序）
     */
    private String sortType = "1";

    /**
     * 页码（从1开始）
     */
    private Integer pageNum = 1;

    /**
     * 每页大小（支持50/100/150/200条/页）
     */
    private Integer pageSize = 50;


    /**
     * 验证日期参数
     */
    public void validateDateParams() {
        if (relationStartDate != null && relationEndDate != null) {
            if (relationStartDate.isAfter(relationEndDate)) {
                // 如果开始时间晚于结束时间，交换它们
                LocalDate temp = relationStartDate;
                relationStartDate = relationEndDate;
                relationEndDate = temp;
            }
        }
    }


    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return pageSize;
    }


}

