package tripai.recommend.system.domain.dto.recommender;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.LocalDate;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.dto.recommender
 * @className: RecommenderGuideRelationQueryDto
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/7/25 17:34
 * @version: 1.0
 */
@Data
public class RecommenderGuideRelationQueryDto {

    /**
     * 推荐方ID
     */
    private Long recommenderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 产线筛选：null或"all"=全部，1=司机，2=导游，3=司兼导
     */
    private Integer productionLine;

    /**
     * 状态筛选：null或"all"=全部，"normal"=正常(0)，"controlled"=管控(1,2,3)
     */
    private String controlStatus;

    /**
     * 建立关系开始时间
     */
    private LocalDate relationStartDate;

    /**
     * 建立关系结束时间
     */
    private LocalDate relationEndDate;

    /**
     * 搜索关键字（导游ID或导游姓名）
     */
    private String keyword;

    /**
     * 排序类型
     */
    private String sortType = "1";

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小（支持50/100/150/200条/页）
     */
    private Integer pageSize = 50;


    /**
     * 验证日期参数
     */
    public void validateDateParams() {
        if (relationStartDate != null && relationEndDate != null) {
            if (relationStartDate.isAfter(relationEndDate)) {
                // 如果开始时间晚于结束时间，交换它们
                LocalDate temp = relationStartDate;
                relationStartDate = relationEndDate;
                relationEndDate = temp;
            }
        }
    }


    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return pageSize;
    }


    /**
     * 判断是否有产线筛选条件
     */
    public boolean hasProductionLineFilter() {
        return productionLine != null && productionLine > 0;
    }

    /**
     * 判断是否有状态筛选条件
     */
    public boolean hasControlStatusFilter() {
        return controlStatus != null &&
                !"all".equalsIgnoreCase(controlStatus.trim()) &&
                !controlStatus.trim().isEmpty();
    }

    /**
     * 判断是否有日期筛选条件
     */
    public boolean hasDateFilter() {
        return relationStartDate != null || relationEndDate != null;
    }

    /**
     * 判断是否有搜索关键字
     */
    public boolean hasKeyword() {
        return keyword != null && !keyword.trim().isEmpty();
    }

    /**
     * 获取处理后的搜索关键字
     */
    public String getProcessedKeyword() {
        if (!hasKeyword()) {
            return null;
        }
        return keyword.trim();
    }

    /**
     * 判断是否为正常状态筛选
     */
    public boolean isNormalStatusFilter() {
        return "normal".equalsIgnoreCase(controlStatus);
    }

    /**
     * 判断是否为管控状态筛选
     */
    public boolean isControlledStatusFilter() {
        return "controlled".equalsIgnoreCase(controlStatus);
    }

}
