package tripai.recommend.system.domain.enums;

import lombok.Getter;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.enums
 * @className: HotelRelationSortTypeEnum
 * @author: lijunqi
 * @description: 酒店关系排序类型枚举
 * @date: 2025/7/28 16:00
 * @version: 1.0
 */
@Getter
public enum HotelRelationSortTypeEnum {

    /**
     * 建立时间从新到旧（默认）
     */
    CREATE_TIME_DESC(1, "建立时间从新到旧", "按建立关系时间降序排列"),

    /**
     * 上架服务数从高到低
     */
    SERVICE_COUNT_DESC(2, "上架服务数从高到低", "按上架房型数量降序排列"),

    /**
     * 成单数从高到低
     */
    ORDER_COUNT_DESC(3, "成单数从高到低", "按成单数量降序排列");

    /**
     * 排序类型代码
     */
    private final Integer code;

    /**
     * 排序类型名称
     */
    private final String name;

    /**
     * 排序类型描述
     */
    private final String description;

    HotelRelationSortTypeEnum(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 排序类型代码
     * @return 排序类型枚举
     */
    public static HotelRelationSortTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (HotelRelationSortTypeEnum sortType : values()) {
            if (sortType.getCode().equals(code)) {
                return sortType;
            }
        }
        return null;
    }

    /**
     * 验证排序类型代码是否有效
     *
     * @param code 排序类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 获取默认排序类型
     *
     * @return 默认排序类型
     */
    public static HotelRelationSortTypeEnum getDefault() {
        return CREATE_TIME_DESC;
    }

    /**
     * 获取排序类型名称
     *
     * @param code 排序类型代码
     * @return 排序类型名称
     */
    public static String getNameByCode(Integer code) {
        HotelRelationSortTypeEnum sortType = getByCode(code);
        return sortType != null ? sortType.getName() : "未知排序类型";
    }

    /**
     * 获取排序类型描述
     *
     * @param code 排序类型代码
     * @return 排序类型描述
     */
    public static String getDescriptionByCode(Integer code) {
        HotelRelationSortTypeEnum sortType = getByCode(code);
        return sortType != null ? sortType.getDescription() : "未知排序类型";
    }
}
