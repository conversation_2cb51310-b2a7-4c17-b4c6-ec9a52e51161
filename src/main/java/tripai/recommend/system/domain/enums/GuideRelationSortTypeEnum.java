package tripai.recommend.system.domain.enums;

import lombok.Getter;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.enums
 * @className: GuideRelationSortTypeEnum
 * @author: lijunqi
 * @description: 导游关系排序类型枚举
 * @date: 2025/7/28 17:25
 * @version: 1.0
 */
@Getter
public enum GuideRelationSortTypeEnum {

    /**
     * 建立时间从新到旧（默认）
     */
    CREATE_TIME_DESC("1", "建立时间从新到旧", "按建立关系时间降序排列"),

    /**
     * 服务分从高到低
     */
    SERVICE_SCORE_DESC("2", "服务分从高到低", "按导游服务分降序排列"),

    /**
     * 上架服务数从高到低
     */
    SERVICE_COUNT_DESC("3", "上架服务数从高到低", "按上架服务数量降序排列"),

    /**
     * 成单数从高到低
     */
    ORDER_COUNT_DESC("4", "成单数从高到低", "按成单数量降序排列");

    /**
     * 排序类型代码
     */
    private final String code;

    /**
     * 排序类型名称
     */
    private final String name;

    /**
     * 排序类型描述
     */
    private final String description;

    GuideRelationSortTypeEnum(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 排序类型代码
     * @return 排序类型枚举
     */
    public static GuideRelationSortTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (GuideRelationSortTypeEnum sortType : values()) {
            if (sortType.getCode().equals(code)) {
                return sortType;
            }
        }
        return null;
    }

    /**
     * 验证排序类型代码是否有效
     *
     * @param code 排序类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取默认排序类型
     *
     * @return 默认排序类型
     */
    public static GuideRelationSortTypeEnum getDefault() {
        return CREATE_TIME_DESC;
    }

    /**
     * 获取排序类型名称
     *
     * @param code 排序类型代码
     * @return 排序类型名称
     */
    public static String getNameByCode(String code) {
        GuideRelationSortTypeEnum sortType = getByCode(code);
        return sortType != null ? sortType.getName() : "未知排序类型";
    }

    /**
     * 获取排序类型描述
     *
     * @param code 排序类型代码
     * @return 排序类型描述
     */
    public static String getDescriptionByCode(String code) {
        GuideRelationSortTypeEnum sortType = getByCode(code);
        return sortType != null ? sortType.getDescription() : "未知排序类型";
    }

    /**
     * 获取SQL排序字段
     *
     * @param code 排序类型代码
     * @return SQL排序字段
     */
    public static String getSqlOrderBy(String code) {
        GuideRelationSortTypeEnum sortType = getByCode(code);
        if (sortType == null) {
            sortType = getDefault();
        }

        switch (sortType) {
            case SERVICE_SCORE_DESC:
                return "tgs.service_score DESC, rr.create_time DESC";
            case SERVICE_COUNT_DESC:
                return "service_count DESC, rr.create_time DESC";
            case ORDER_COUNT_DESC:
                return "order_count DESC, rr.create_time DESC";
            case CREATE_TIME_DESC:
            default:
                return "rr.create_time DESC, rr.id ASC";
        }
    }

    /**
     * 判断是否为时间排序
     *
     * @param code 排序类型代码
     * @return 是否为时间排序
     */
    public static boolean isTimeSort(String code) {
        return CREATE_TIME_DESC.getCode().equals(code);
    }

    /**
     * 判断是否为服务分排序
     *
     * @param code 排序类型代码
     * @return 是否为服务分排序
     */
    public static boolean isServiceScoreSort(String code) {
        return SERVICE_SCORE_DESC.getCode().equals(code);
    }

    /**
     * 判断是否为服务数排序
     *
     * @param code 排序类型代码
     * @return 是否为服务数排序
     */
    public static boolean isServiceCountSort(String code) {
        return SERVICE_COUNT_DESC.getCode().equals(code);
    }

    /**
     * 判断是否为成单数排序
     *
     * @param code 排序类型代码
     * @return 是否为成单数排序
     */
    public static boolean isOrderCountSort(String code) {
        return ORDER_COUNT_DESC.getCode().equals(code);
    }

    /**
     * 获取所有排序类型代码
     *
     * @return 排序类型代码数组
     */
    public static String[] getAllCodes() {
        return new String[]{
                CREATE_TIME_DESC.getCode(),
                SERVICE_SCORE_DESC.getCode(),
                SERVICE_COUNT_DESC.getCode(),
                ORDER_COUNT_DESC.getCode()
        };
    }

    /**
     * 获取所有排序类型名称
     *
     * @return 排序类型名称数组
     */
    public static String[] getAllNames() {
        return new String[]{
                CREATE_TIME_DESC.getName(),
                SERVICE_SCORE_DESC.getName(),
                SERVICE_COUNT_DESC.getName(),
                ORDER_COUNT_DESC.getName()
        };
    }
}
