package tripai.recommend.system.domain.enums;

import lombok.Getter;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.enums
 * @className: ControlStatusEnum
 * @author: lijunqi
 * @description: 导游管控状态枚举
 * @date: 2025/7/28 17:20
 * @version: 1.0
 */
@Getter
public enum ControlStatusEnum {

    /**
     * 正常状态
     */
    NORMAL(0, "正常", "normal", "导游状态正常，可正常接单"),

    /**
     * 月度管控中
     */
    MONTHLY_CONTROLLED(1, "月度管控中", "controlled", "导游处于月度管控状态，暂时无法接单"),

    /**
     * 永久管控中
     */
    PERMANENTLY_CONTROLLED(2, "永久管控中", "controlled", "导游处于永久管控状态，无法接单"),

    /**
     * 手动管控中
     */
    MANUALLY_CONTROLLED(3, "手动管控中", "controlled", "导游处于手动管控状态，暂时无法接单");

    /**
     * 状态代码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 状态分组（用于筛选）
     */
    private final String group;

    /**
     * 状态描述
     */
    private final String description;

    ControlStatusEnum(Integer code, String name, String group, String description) {
        this.code = code;
        this.name = name;
        this.group = group;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 状态枚举
     */
    public static ControlStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ControlStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据分组获取枚举列表
     *
     * @param group 状态分组
     * @return 状态枚举列表
     */
    public static ControlStatusEnum[] getByGroup(String group) {
        if (group == null) {
            return values();
        }
        return java.util.Arrays.stream(values())
                .filter(status -> group.equals(status.getGroup()))
                .toArray(ControlStatusEnum[]::new);
    }

    /**
     * 验证状态代码是否有效
     *
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 验证状态分组是否有效
     *
     * @param group 状态分组
     * @return 是否有效
     */
    public static boolean isValidGroup(String group) {
        if (group == null || "all".equalsIgnoreCase(group)) {
            return true;
        }
        return "normal".equalsIgnoreCase(group) || "controlled".equalsIgnoreCase(group);
    }

    /**
     * 获取状态名称
     *
     * @param code 状态代码
     * @return 状态名称
     */
    public static String getNameByCode(Integer code) {
        ControlStatusEnum status = getByCode(code);
        return status != null ? status.getName() : "未知状态";
    }

    /**
     * 获取状态描述
     *
     * @param code 状态代码
     * @return 状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        ControlStatusEnum status = getByCode(code);
        return status != null ? status.getDescription() : "未知状态";
    }

    /**
     * 判断是否为正常状态
     *
     * @param code 状态代码
     * @return 是否为正常状态
     */
    public static boolean isNormal(Integer code) {
        return NORMAL.getCode().equals(code);
    }

    /**
     * 判断是否为管控状态
     *
     * @param code 状态代码
     * @return 是否为管控状态
     */
    public static boolean isControlled(Integer code) {
        ControlStatusEnum status = getByCode(code);
        return status != null && "controlled".equals(status.getGroup());
    }

    /**
     * 判断是否为月度管控
     *
     * @param code 状态代码
     * @return 是否为月度管控
     */
    public static boolean isMonthlyControlled(Integer code) {
        return MONTHLY_CONTROLLED.getCode().equals(code);
    }

    /**
     * 判断是否为永久管控
     *
     * @param code 状态代码
     * @return 是否为永久管控
     */
    public static boolean isPermanentlyControlled(Integer code) {
        return PERMANENTLY_CONTROLLED.getCode().equals(code);
    }

    /**
     * 判断是否为手动管控
     *
     * @param code 状态代码
     * @return 是否为手动管控
     */
    public static boolean isManuallyControlled(Integer code) {
        return MANUALLY_CONTROLLED.getCode().equals(code);
    }

    /**
     * 获取正常状态的代码
     *
     * @return 正常状态代码
     */
    public static Integer getNormalCode() {
        return NORMAL.getCode();
    }

    /**
     * 获取所有管控状态的代码
     *
     * @return 管控状态代码数组
     */
    public static Integer[] getControlledCodes() {
        return new Integer[]{
                MONTHLY_CONTROLLED.getCode(),
                PERMANENTLY_CONTROLLED.getCode(),
                MANUALLY_CONTROLLED.getCode()
        };
    }

    /**
     * 获取所有状态代码
     *
     * @return 状态代码数组
     */
    public static Integer[] getAllCodes() {
        return new Integer[]{
                NORMAL.getCode(),
                MONTHLY_CONTROLLED.getCode(),
                PERMANENTLY_CONTROLLED.getCode(),
                MANUALLY_CONTROLLED.getCode()
        };
    }

    /**
     * 获取所有状态名称
     *
     * @return 状态名称数组
     */
    public static String[] getAllNames() {
        return new String[]{
                NORMAL.getName(),
                MONTHLY_CONTROLLED.getName(),
                PERMANENTLY_CONTROLLED.getName(),
                MANUALLY_CONTROLLED.getName()
        };
    }
}
