package tripai.recommend.system.domain.enums;

import lombok.Getter;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.domain.enums
 * @className: ProductionLineEnum
 * @author: lijunqi
 * @description: 导游产线枚举
 * @date: 2025/7/28 17:15
 * @version: 1.0
 */
@Getter
public enum ProductionLineEnum {

    /**
     * 司机
     */
    DRIVER(1, "司机", "专职司机，负责接送服务"),

    /**
     * 导游
     */
    GUIDE(2, "导游", "专职导游，负责讲解和带团服务"),

    /**
     * 司兼导
     */
    DRIVER_GUIDE(3, "司兼导", "司机兼导游，提供接送和讲解服务");

    /**
     * 产线代码
     */
    private final Integer code;

    /**
     * 产线名称
     */
    private final String name;

    /**
     * 产线描述
     */
    private final String description;

    ProductionLineEnum(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 产线代码
     * @return 产线枚举
     */
    public static ProductionLineEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductionLineEnum productionLine : values()) {
            if (productionLine.getCode().equals(code)) {
                return productionLine;
            }
        }
        return null;
    }

    /**
     * 验证产线代码是否有效
     *
     * @param code 产线代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 获取产线名称
     *
     * @param code 产线代码
     * @return 产线名称
     */
    public static String getNameByCode(Integer code) {
        ProductionLineEnum productionLine = getByCode(code);
        return productionLine != null ? productionLine.getName() : "未知产线";
    }

    /**
     * 获取产线描述
     *
     * @param code 产线代码
     * @return 产线描述
     */
    public static String getDescriptionByCode(Integer code) {
        ProductionLineEnum productionLine = getByCode(code);
        return productionLine != null ? productionLine.getDescription() : "未知产线";
    }

    /**
     * 判断是否为司机
     *
     * @param code 产线代码
     * @return 是否为司机
     */
    public static boolean isDriver(Integer code) {
        return DRIVER.getCode().equals(code);
    }

    /**
     * 判断是否为导游
     *
     * @param code 产线代码
     * @return 是否为导游
     */
    public static boolean isGuide(Integer code) {
        return GUIDE.getCode().equals(code);
    }

    /**
     * 判断是否为司兼导
     *
     * @param code 产线代码
     * @return 是否为司兼导
     */
    public static boolean isDriverGuide(Integer code) {
        return DRIVER_GUIDE.getCode().equals(code);
    }

    /**
     * 判断是否需要驾驶证
     *
     * @param code 产线代码
     * @return 是否需要驾驶证
     */
    public static boolean needsDrivingLicense(Integer code) {
        return isDriver(code) || isDriverGuide(code);
    }

    /**
     * 判断是否需要导游证
     *
     * @param code 产线代码
     * @return 是否需要导游证
     */
    public static boolean needsGuideCard(Integer code) {
        return isGuide(code) || isDriverGuide(code);
    }

    /**
     * 获取所有产线代码
     *
     * @return 产线代码数组
     */
    public static Integer[] getAllCodes() {
        return new Integer[]{DRIVER.getCode(), GUIDE.getCode(), DRIVER_GUIDE.getCode()};
    }

    /**
     * 获取所有产线名称
     *
     * @return 产线名称数组
     */
    public static String[] getAllNames() {
        return new String[]{DRIVER.getName(), GUIDE.getName(), DRIVER_GUIDE.getName()};
    }
}
