package tripai.recommend.system.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.crypto.symmetric.AES;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tripai.recommend.system.constant.CryptoSecretConstants;
import tripai.recommend.system.domain.vo.user.MobileInfo;

import java.util.List;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.util
 * @className: MobileCryptoUtil
 * @author: lijunqi
 * @description:
 * @date: 2025/7/28 15:01
 * @version: 1.0
 */

@Component
@Slf4j
public class MobileCryptoUtil {

    private AES mobileAes;
    private HMac passwordHmac;

    @PostConstruct
    public void init() {
        mobileAes = new AES(Mode.CTS, Padding.PKCS5Padding,
                CryptoSecretConstants.AES_MOBILE_KEY.getBytes(),
                CryptoSecretConstants.AES_MOBILE_IV.getBytes());
        passwordHmac = new HMac(HmacAlgorithm.HmacSHA512, CryptoSecretConstants.HMAC_PASSWORD_KEY.getBytes());
    }

    /**
     * 解密手机号
     *
     * @param content 加密内容
     * @return 手机号信息
     */
    public MobileInfo mobileAesDecrypt(String content) {
        if (StrUtil.isBlank(content)) {
            log.warn("解密手机号失败：加密内容为空");
            return new MobileInfo("86", "");
        }

        try {
            String dec = mobileAes.decryptStr(content);
            List<String> areaCodeMobile = StrUtil.split(dec, StrUtil.COMMA);
            if (areaCodeMobile.size() < 2) {
                log.warn("解密手机号格式异常：{}", dec);
                return new MobileInfo("86", dec);
            }
            return new MobileInfo(areaCodeMobile.get(0), areaCodeMobile.get(1));
        } catch (Exception e) {
            log.error("解密手机号异常：{}", content, e);
            return new MobileInfo("86", "");
        }
    }

}
