package tripai.recommend.system.mapper.recommender;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tripai.recommend.system.domain.dto.recommender.RecommenderOrderQueryDto;
import tripai.recommend.system.domain.entity.RecommenderRelation;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.mapper.recommender
 * @className: RecommenderOrderMapper
 * @author: lijunqi
 * @description:
 * @date: 2025/7/24 18:21
 * @version: 1.0
 */
@Mapper
public interface RecommenderOrderMapper {

    /**
     * 查询推荐方订单列表
     *
     * @param queryDto 查询条件
     * @return 订单列表
     */
    List<RecommenderOrderVo> selectRecommenderOrderList(@Param("query") RecommenderOrderQueryDto queryDto);

    /**
     * 查询推荐方订单总数
     *
     * @param queryDto 查询条件
     * @return 订单总数
     */
    Long selectRecommenderOrderCount(@Param("query") RecommenderOrderQueryDto queryDto);

    /**
     * 查询推荐方订单分佣金额小计
     *
     * @param queryDto 查询条件
     * @return 分佣金额小计
     */
    BigDecimal selectRecommenderOrderCommissionSum(@Param("query") RecommenderOrderQueryDto queryDto);

    /**
     * 根据推荐方ID查询酒店订单列表
     *
     * @param queryDto 查询条件
     * @return 酒店订单列表
     */
    List<RecommenderOrderVo> selectHotelOrderList(@Param("query") RecommenderOrderQueryDto queryDto);

    /**
     * 根据推荐方ID查询导游订单列表
     *
     * @param queryDto 查询条件
     * @return 导游订单列表
     */
    List<RecommenderOrderVo> selectTourGuideOrderList(@Param("query") RecommenderOrderQueryDto queryDto);

    /**
     * 查询酒店订单总数
     *
     * @param queryDto 查询条件
     * @return 酒店订单总数
     */
    Long selectHotelOrderCount(@Param("query") RecommenderOrderQueryDto queryDto);

    /**
     * 查询导游订单总数
     *
     * @param queryDto 查询条件
     * @return 导游订单总数
     */
    Long selectTourGuideOrderCount(@Param("query") RecommenderOrderQueryDto queryDto);

    /**
     * 查询酒店订单分佣金额小计
     *
     * @param queryDto 查询条件
     * @return 酒店订单分佣金额小计
     */
    BigDecimal selectHotelOrderCommissionSum(@Param("query") RecommenderOrderQueryDto queryDto);

    /**
     * 查询导游订单分佣金额小计
     *
     * @param queryDto 查询条件
     * @return 导游订单分佣金额小计
     */
    BigDecimal selectTourGuideOrderCommissionSum(@Param("query") RecommenderOrderQueryDto queryDto);
}
