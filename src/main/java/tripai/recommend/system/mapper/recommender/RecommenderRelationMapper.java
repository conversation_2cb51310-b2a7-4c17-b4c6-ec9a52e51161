package tripai.recommend.system.mapper.recommender;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tripai.recommend.system.domain.dto.recommender.RecommenderGuideRelationQueryDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderHotelRelationQueryDto;
import tripai.recommend.system.domain.entity.RecommenderRelation;
import tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationStatisticsVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationListVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationVo;

import java.util.List;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.mapper.recommender
 * @className: RecommenderRelationMapper
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/7/25 17:13
 * @version: 1.0
 */
@Mapper
public interface RecommenderRelationMapper extends BaseMapper<RecommenderRelation> {

    /**
     * 查询推荐方酒店关系列表
     *
     * @param queryDto 查询条件
     * @return 推荐方酒店关系列表
     */
    List<RecommenderHotelRelationVo> selectRecommenderHotelRelationList(@Param("query") RecommenderHotelRelationQueryDto queryDto);


    /**
     * 查询推荐方酒店关系总数
     *
     * @param queryDto 查询条件
     * @return 推荐方酒店关系总数
     */
    Long selectRecommenderHotelRelationCount(@Param("query") RecommenderHotelRelationQueryDto queryDto);


    /**
     * 查询推荐方酒店关系统计信息
     *
     * @param queryDto 查询条件
     * @return 统计信息
     */
    RecommenderHotelRelationListVo.RecommenderHotelRelationStatisticsVo selectRecommenderHotelRelationStatistics(@Param("query") RecommenderHotelRelationQueryDto queryDto);


    /**
     * 查询推荐方导游关系列表
     *
     * @param queryDto 查询条件
     * @return 推荐方导游关系列表
     */
    List<RecommenderGuideRelationVo> selectRecommenderGuideRelationList(@Param("query") RecommenderGuideRelationQueryDto queryDto);

    /**
     * 查询推荐方导游关系总数
     *
     * @param queryDto 查询条件
     * @return 推荐方导游关系总数
     */
    Long selectRecommenderGuideRelationCount(@Param("query") RecommenderGuideRelationQueryDto queryDto);


    /**
     * 查询推荐方导游关系统计信息
     *
     * @param queryDto 查询条件
     * @return 推荐方导游关系统计信息
     */
   RecommenderGuideRelationStatisticsVo selectRecommenderGuideRelationStatistics(@Param("query") RecommenderGuideRelationQueryDto queryDto);



    /**
     * 查询导游的上架服务数
     *
     * @param guideId 导游ID
     * @return 上架服务数
     */
    Integer selectServiceCountByGuideId(@Param("guideId") Long guideId);

    /**
     * 查询导游的成单数（建立关系后的已结算订单）
     *
     * @param guideId 导游ID
     * @param recommenderId 推荐方ID
     * @param relationCreateTime 建立关系时间
     * @return 成单数
     */
    Integer selectOrderCountByGuideId(@Param("guideId") Long guideId,
                                      @Param("recommenderId") Long recommenderId,
                                      @Param("relationCreateTime") String relationCreateTime);


}

