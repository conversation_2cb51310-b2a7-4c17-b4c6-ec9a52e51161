package tripai.recommend.system.exception;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.exception
 * @className: AuthenticationException
 * @author: lijun<PERSON>
 * @description: 认证异常
 * @date: 2025/7/30 16:00
 * @version: 1.0
 */
public class AuthenticationException extends RuntimeException {

    private final int code;

    public AuthenticationException(int code, String message) {
        super(message);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
