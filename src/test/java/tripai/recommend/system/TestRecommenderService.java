package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankInfoDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderReqDto;
import tripai.recommend.system.domain.entity.RecommenderAuditRecord;
import tripai.recommend.system.domain.entity.RecommenderBank;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.vo.recommender.RecommenderAuditDetailVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderProfileVo;
import tripai.recommend.system.mapper.recommender.RecommenderAuditRecordMapper;
import tripai.recommend.system.mapper.recommender.RecommenderBankMapper;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.RecommenderService;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderService
 * @author: lijunqi
 * @description: RecommenderService两个方法的分支覆盖测试
 * @date: 2025/7/29 14:30
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderService {

    @Resource
    private RecommenderService recommenderService;

    @MockBean
    private RecommenderMapper recommenderMapper;

    @MockBean
    private RecommenderBankMapper recommenderBankMapper;

    @MockBean
    private RecommenderAuditRecordMapper recommenderAuditRecordMapper;

    // ==================== 数据完整性验证测试（最优先执行） ====================

    /**
     * 测试数据完整性验证 - 确保测试环境和依赖正常
     * 分支：数据完整性验证（优先级最高）
     */
    @Test
    public void test01_DataIntegrityVerification() {
        log.info("=== 测试数据完整性验证 - 确保测试环境和依赖正常 ===");

        // 验证服务注入
        assertThat(recommenderService).isNotNull();
        assertThat(recommenderMapper).isNotNull();
        assertThat(recommenderBankMapper).isNotNull();
        assertThat(recommenderAuditRecordMapper).isNotNull();

        log.info("✅ 服务依赖注入验证通过");

        // 验证基础DTO构建
        RecommenderReqDto saveDto = buildValidRecommenderReqDto();
        assertThat(saveDto).isNotNull();
        assertThat(saveDto.getUserId()).isNotNull();

        log.info("✅ saveProfile基础数据构建验证通过");

        // 验证getAuditDetail参数
        Long recommenderId = 1L;
        assertThat(recommenderId).isNotNull();

        log.info("✅ getAuditDetail基础数据构建验证通过");
        log.info("数据完整性验证完成");
    }

    // ==================== saveProfile方法分支测试 ====================

    /**
     * 测试saveProfile方法 - 分支B1：dto为null的情况
     * 分支：B1 - if (dto == null) 返回失败："参数为空"
     */
    @Test
    public void test02_SaveProfile_BranchB1_DtoNull() {
        log.info("=== 测试saveProfile方法 - 分支B1：dto为null的情况 ===");

        // 调用服务方法，传入null参数
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B1的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("参数为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B1测试通过：dto为null时正确返回失败结果");
    }

    /**
     * 测试saveProfile方法 - 分支B2：草稿模式跳过验证的情况
     * 分支：B2 - if (!dto.getIsDraft()) 为false，跳过validateRequest
     */
    @Test
    public void test03_SaveProfile_BranchB2_DraftModeSkipValidation() {
        log.info("=== 测试saveProfile方法 - 分支B2：草稿模式跳过验证的情况 ===");

        // 构建草稿模式的DTO（缺少必要字段但isDraft=true）
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 设置为草稿模式
        dto.setName(null); // 故意设置为null，但草稿模式应该跳过验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息不存在，需要创建新的
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(null);
        // Mock保存操作成功
        when(recommenderMapper.insert(any())).thenReturn(1);
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isNotNull();

        log.info("✅ 分支B2测试通过：草稿模式跳过验证成功");
    }

    /**
     * 测试saveProfile方法 - 分支B2：非草稿模式触发验证异常的情况
     * 分支：B2 - if (!dto.getIsDraft()) 为true，执行validateRequest并抛出异常
     */
    @Test
    public void test04_SaveProfile_BranchB2_ValidationException() {
        log.info("=== 测试saveProfile方法 - 分支B2：非草稿模式触发验证异常的情况 ===");

        // 构建非草稿模式但缺少必要字段的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(false); // 非草稿模式
        dto.setName(null); // 缺少必要字段，会触发验证异常

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果（验证异常被catch块捕获）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：非草稿模式验证异常被正确处理");
    }

    /**
     * 测试saveProfile方法 - 分支B3 + B4：推荐方不存在但身份证号重复的情况
     * 分支：B3 - if (recommenderInfo == null) 为true，进入创建分支
     * 分支：B4 - if (existingInfo != null) 为true，返回失败："该身份证号/统一社会信用代码已被使用"
     */
    @Test
    public void test05_SaveProfile_BranchB3_B4_IdentifierDuplicate() {
        log.info("=== 测试saveProfile方法 - 分支B3 + B4：推荐方不存在但身份证号重复的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息不存在（第一次查询）
        when(recommenderMapper.selectOne(any(), eq(false)))
                .thenReturn(null) // 第一次查询：按userId查询，返回null
                .thenReturn(buildMockRecommenderInfo()); // 第二次查询：按身份证号查询，返回存在的记录

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3 + B4的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("该身份证号/统一社会信用代码已被使用");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B3 + B4测试通过：身份证号重复时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(2)).selectOne(any(), eq(false));
    }

    /**
     * 测试saveProfile方法 - 分支B3：推荐方不存在且身份证号不重复，创建新推荐方的情况
     * 分支：B3 - if (recommenderInfo == null) 为true，进入创建分支，身份证号不重复
     */
    @Test
    public void test06_SaveProfile_BranchB3_CreateNewRecommender() {
        log.info("=== 测试saveProfile方法 - 分支B3：推荐方不存在且身份证号不重复，创建新推荐方的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息不存在，身份证号也不重复
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(null);
        // Mock保存操作成功
        when(recommenderMapper.insert(any())).thenReturn(1);
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isNotNull();

        log.info("✅ 分支B3测试通过：创建新推荐方成功");

        // 验证Mock调用
        verify(recommenderMapper, times(2)).selectOne(any(), eq(false)); // 两次查询
        verify(recommenderMapper, times(1)).insert(any()); // 插入推荐方信息
        verify(recommenderBankMapper, times(1)).insert(any()); // 插入银行信息
    }

    /**
     * 测试saveProfile方法 - 分支B5：保存推荐方基本信息失败的情况
     * 分支：B5 - if (!saveRecommenderInfo) 为true，返回失败："保存推荐方认证资料失败"
     */
    @Test
    public void test07_SaveProfile_BranchB5_SaveRecommenderInfoFailed() {
        log.info("=== 测试saveProfile方法 - 分支B5：保存推荐方基本信息失败的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在（更新场景）
        RecommenderInfo existingInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(existingInfo);
        // Mock更新操作失败
        when(recommenderMapper.updateById(any())).thenReturn(0);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B5的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B5测试通过：保存推荐方基本信息失败时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderMapper, times(1)).updateById(any());
    }

    /**
     * 测试saveProfile方法 - 分支B6：保存银行信息失败的情况
     * 分支：B6 - if (!saveBankInfo) 为true，返回失败："保存推荐方银行信息资料失败"
     */
    @Test
    public void test08_SaveProfile_BranchB6_SaveBankInfoFailed() {
        log.info("=== 测试saveProfile方法 - 分支B6：保存银行信息失败的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在
        RecommenderInfo existingInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(existingInfo);
        // Mock推荐方信息保存成功
        when(recommenderMapper.updateById(any())).thenReturn(1);
        // Mock银行信息保存失败
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(0); // 插入失败

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B6的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方银行信息资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B6测试通过：保存银行信息失败时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).updateById(any());
        verify(recommenderBankMapper, times(1)).insert(any());
    }

    /**
     * 测试saveProfile方法 - 分支B7：异常处理的情况
     * 分支：B7 - catch (Exception e) 返回失败："保存推荐方认证资料失败"
     */
    @Test
    public void test09_SaveProfile_BranchB7_ExceptionHandling() {
        log.info("=== 测试saveProfile方法 - 分支B7：异常处理的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息查询抛出异常
        when(recommenderMapper.selectOne(any(), eq(false)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B7的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B7测试通过：异常处理时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试saveProfile方法 - 成功场景：完整流程成功的情况
     * 分支：正常流程，所有操作都成功
     */
    @Test
    public void test10_SaveProfile_SuccessScenario() {
        log.info("=== 测试saveProfile方法 - 成功场景：完整流程成功的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在
        RecommenderInfo existingInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(existingInfo);
        // Mock所有保存操作成功
        when(recommenderMapper.updateById(any())).thenReturn(1);
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证成功结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isEqualTo(existingInfo.getId());

        log.info("✅ 成功场景测试通过：完整流程执行成功");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).updateById(any());
        verify(recommenderBankMapper, times(1)).insert(any());
    }
}
