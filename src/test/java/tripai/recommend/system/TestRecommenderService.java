package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankInfoDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderReqDto;
import tripai.recommend.system.domain.entity.FileInfo;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.vo.recommender.RecommenderProfileVo;
import tripai.recommend.system.mapper.recommender.RecommenderAuditRecordMapper;
import tripai.recommend.system.mapper.recommender.RecommenderBankMapper;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.RecommenderService;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 11:12
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderService {

    @Resource
    private RecommenderService recommenderService;

    @Resource
    private RecommenderMapper recommenderMapper;

    @Resource
    private RecommenderBankMapper recommenderBankMapper;

    @Resource
    private RecommenderAuditRecordMapper recommenderAuditRecordMapper;

    /**
     * 测试saveProfile方法
     */
    @Test
    public void test_SaveProfile() {
        log.info("=== 测试saveProfile方法 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = new RecommenderReqDto();
        dto.setUserId(1L);
        dto.setName("张三");
        dto.setType(1); // 个人类型
        dto.setIdentifier("110101199001011234");
        dto.setCertificateType(1);


        FileInfo front = new FileInfo();
        front.setUrl("http://example.com/rms/idcard_front.jpg");
        front.setName("idcard_front.jpg");
        front.setType(1); // 默认类型为1
        front.setBucket("rms"); // 默认bucket为rms

        FileInfo back = new FileInfo();
        back.setUrl("http://example.com/rms/idcard_back.jpg");
        back.setName("idcard_back.jpg");
        back.setType(1); // 默认类型为1
        back.setBucket("rms"); // 默认bucket为rms

        dto.setIdCardFrontUrl(front);
        dto.setIdCardBackUrl(back);
        dto.setIsDraft(false);

        // 银行信息
        RecommenderBankInfoDto bankInfo = new RecommenderBankInfoDto();
        bankInfo.setAccountName("张三");
        bankInfo.setBankCardNo("****************");
        bankInfo.setBankName("工商银行");
        bankInfo.setBranchName("北京分行");
        bankInfo.setProvince("北京市");
        bankInfo.setCity("北京市");
        bankInfo.setAccountCertificateType(1);
        bankInfo.setAccountIdentifier("****************");
        dto.setBankInfo(bankInfo);

        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }
}
