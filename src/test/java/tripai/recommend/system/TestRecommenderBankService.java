package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankAccountDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderBankAccountVo;
import tripai.recommend.system.service.RecommenderBankService;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderBankService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/24 15:12
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderBankService {

    @Resource
    private RecommenderBankService recommenderBankService;

    /**
     * 测试获取推荐方银行账户信息 - 正常流程
     */
    @Test
    public void testGetBankAccountSuccess() {
        log.info("=== 测试获取推荐方银行账户信息 - 正常流程 ===");

        // 使用一个存在的用户ID（需要根据实际数据调整）
        Long userId = 1L;

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderBankAccountVo> result = recommenderBankService.getBankAccount(userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取推荐方银行账户信息 - 用户ID为空
     */
    @Test
    public void testGetBankAccountWithNullUserId() {
        log.info("=== 测试获取推荐方银行账户信息 - 用户ID为空 ===");

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderBankAccountVo> result = recommenderBankService.getBankAccount(null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取推荐方银行账户信息 - 不存在的用户ID
     */
    @Test
    public void testGetBankAccountWithNonExistentUserId() {
        log.info("=== 测试获取推荐方银行账户信息 - 不存在的用户ID ===");

        // 使用一个不存在的用户ID
        Long userId = 999999L;

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderBankAccountVo> result = recommenderBankService.getBankAccount(userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 正常流程（新增）
     */
    @Test
    public void testSaveBankAccountSuccess() {
        log.info("=== 测试保存银行账户信息 - 正常流程（新增） ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 更新现有记录
     */
    @Test
    public void testUpdateBankAccountSuccess() {
        log.info("=== 测试保存银行账户信息 - 更新现有记录 ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);
        dto.setId(1L); // 设置ID表示更新操作
        dto.setAccountName("李四"); // 修改开户名

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 参数为空
     */
    @Test
    public void testSaveBankAccountWithNullParam() {
        log.info("=== 测试保存银行账户信息 - 参数为空 ===");

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 开户名为空
     */
    @Test
    public void testSaveBankAccountWithEmptyAccountName() {
        log.info("=== 测试保存银行账户信息 - 开户名为空 ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);
        dto.setAccountName(""); // 设置开户名为空

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 银行卡号为空
     */
    @Test
    public void testSaveBankAccountWithEmptyBankCardNo() {
        log.info("=== 测试保存银行账户信息 - 银行卡号为空 ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);
        dto.setBankCardNo(""); // 设置银行卡号为空

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 开户手机号为空
     */
    @Test
    public void testSaveBankAccountWithEmptyAccountPhone() {
        log.info("=== 测试保存银行账户信息 - 开户手机号为空 ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);
        dto.setAccountPhone(""); // 设置开户手机号为空

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 开户银行为空
     */
    @Test
    public void testSaveBankAccountWithEmptyBankName() {
        log.info("=== 测试保存银行账户信息 - 开户银行为空 ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);
        dto.setBankName(""); // 设置开户银行为空

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 支行名称为空
     */
    @Test
    public void testSaveBankAccountWithEmptyBranchName() {
        log.info("=== 测试保存银行账户信息 - 支行名称为空 ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);
        dto.setBranchName(""); // 设置支行名称为空

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 所在省份为空
     */
    @Test
    public void testSaveBankAccountWithEmptyProvince() {
        log.info("=== 测试保存银行账户信息 - 所在省份为空 ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);
        dto.setProvince(""); // 设置所在省份为空

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试保存银行账户信息 - 所在城市为空
     */
    @Test
    public void testSaveBankAccountWithEmptyCity() {
        log.info("=== 测试保存银行账户信息 - 所在城市为空 ===");

        // 构建测试数据
        RecommenderBankAccountDto dto = buildBankAccountDtoWithUserId(1L);
        dto.setCity(""); // 设置所在城市为空

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试删除银行账户信息 - 正常流程
     */
    @Test
    public void testDeleteBankAccountSuccess() {
        log.info("=== 测试删除银行账户信息 - 正常流程 ===");

        // 使用一个存在的银行账户ID和用户ID（需要根据实际数据调整）
        Long bankAccountId = 1L;
        Long userId = 1L;

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = recommenderBankService.deleteBankAccount(bankAccountId, userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试删除银行账户信息 - 银行账户ID为空
     */
    @Test
    public void testDeleteBankAccountWithNullBankAccountId() {
        log.info("=== 测试删除银行账户信息 - 银行账户ID为空 ===");

        Long userId = 1L;

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = recommenderBankService.deleteBankAccount(null, userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试删除银行账户信息 - 用户ID为空
     */
    @Test
    public void testDeleteBankAccountWithNullUserId() {
        log.info("=== 测试删除银行账户信息 - 用户ID为空 ===");

        Long bankAccountId = 1L;

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = recommenderBankService.deleteBankAccount(bankAccountId, null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 构建银行账户测试数据
     */
    private RecommenderBankAccountDto buildBankAccountDto() {
        RecommenderBankAccountDto dto = new RecommenderBankAccountDto();
        dto.setAccountName("张三");
        dto.setAccountCertificateType(1); // 身份证
        dto.setAccountIdentifier("******************");
        dto.setAccountPhone("***********");
        dto.setBankCardNo("6222021234567890123");
        dto.setBankName("中国工商银行");
        dto.setBankCode("102");
        dto.setBranchCode("************");
        dto.setBranchName("中国工商银行广州分行");
        dto.setProvince("广东省");
        dto.setCity("广州市");
        dto.setBankAddress("广州市天河区珠江新城");

        return dto;
    }

    /**
     * 构建带有用户ID的银行账户测试数据
     */
    private RecommenderBankAccountDto buildBankAccountDtoWithUserId(Long userId) {
        RecommenderBankAccountDto dto = buildBankAccountDto();
        dto.setUserId(userId);
        return dto;
    }
}
