package tripai.recommend.system.mock;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DataAccessException;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.entity.RecommenderBank;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.entity.SocialUser;
import tripai.recommend.system.domain.enums.QrChannelBindEnum;
import tripai.recommend.system.domain.vo.user.UserProfileVo;
import tripai.recommend.system.mapper.SocialUserMapper;
import tripai.recommend.system.mapper.recommender.RecommenderBankMapper;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.UserInfoService;
import tripai.recommend.system.util.DataMaskingUtil;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestUserInfoServiceGetUserProfile
 * @author: lijunqi
 * @description:
 * @date: 2025/7/29 10:05
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestUserInfoServiceGetUserProfile {

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private RecommenderMapper recommenderMapper;

    //    @MockBean  //覆盖率测试用@MockBean
    @Resource  //数据查询用@Resource
    private SocialUserMapper socialUserMapper;

    //    @MockBean  //覆盖率测试用@MockBean
    @Resource  //数据查询用@Resource
    private RecommenderBankMapper recommenderBankMapper;

    // ==================== getUserProfile方法100%分支覆盖率测试 ====================

    // ==================== 用户个人中心测试 ====================

    /**
     * 测试获取用户个人中心信息 - 正常流程
     */
    @Test
    public void testGetUserProfileSuccess() {
        log.info("=== 测试获取用户个人中心信息 - 正常流程 ===");

        Long userId = 1L; // 使用存在的用户ID

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试获取用户个人中心信息 - 无效用户ID
     */
    @Test
    public void testGetUserProfileWithInvalidUserId() {
        log.info("=== 测试获取用户个人中心信息 - 无效用户ID ===");

        Long userId = -1L; // 无效的用户ID

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("无效用户ID测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 空用户ID
     */
    @Test
    public void testGetUserProfileWithNullUserId() {
        log.info("=== 测试获取用户个人中心信息 - 空用户ID ===");

        Long userId = null;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("空用户ID测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 用户存在但推荐方信息不存在
     */
    @Test
    public void testGetUserProfileWithNoRecommenderInfo() {
        log.info("=== 测试获取用户个人中心信息 - 用户存在但推荐方信息不存在 ===");

        // 使用一个存在的用户ID，但该用户没有推荐方信息
        Long userId = 6L; // 假设这个用户存在但没有推荐方信息

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 用户未绑定微信
     */
    @Test
    public void testGetUserProfileWithNoWechatBinding() {
        log.info("=== 测试获取用户个人中心信息 - 用户未绑定微信 ===");

        // 使用一个未绑定微信的用户ID
        Long userId = 2L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("未绑定微信测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试获取用户个人中心信息 - 用户已绑定微信
     */
    @Test
    public void testGetUserProfileWithWechatBinding() {
        log.info("=== 测试获取用户个人中心信息 - 用户已绑定微信 ===");

        // 使用一个已绑定微信的用户ID
        Long userId = 1L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("已绑定微信测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 推荐方存在但无银行信息
     */
    @Test
    public void testGetUserProfileWithNoBankInfo() {
        log.info("=== 测试获取用户个人中心信息 - 推荐方存在但无银行信息 ===");

        // 使用一个有推荐方信息但无银行信息的用户ID
        Long userId = 996L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("无银行信息测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证银行相关字段应该为null
            assertThat(profile.getBankCardNo()).isNull();
            assertThat(profile.getBankName()).isNull();
            assertThat(profile.getBankValidateStatus()).isNull();
            log.info("无银行信息的情况测试通过");
        }
    }

    /**
     * 测试获取用户个人中心信息 - 推荐方存在且有银行信息
     */
    @Test
    public void testGetUserProfileWithBankInfo() {
        log.info("=== 测试获取用户个人中心信息 - 推荐方存在且有银行信息 ===");

        // 使用一个有完整银行信息的用户ID
        Long userId = 995L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("有银行信息测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证银行信息字段不为null（如果数据存在的话）
            if (profile.getBankCardNo() != null) {
                assertThat(profile.getBankCardNo()).isNotEmpty();
                log.info("银行卡号: {}", profile.getBankCardNo());
            }
            if (profile.getBankName() != null) {
                assertThat(profile.getBankName()).isNotEmpty();
                log.info("银行名称: {}", profile.getBankName());
            }
            log.info("银行信息存在的情况测试通过");
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户手机号为空
     */
    @Test
    public void testGetUserProfileWithEmptyMobile() {
        log.info("=== 测试获取用户个人中心信息 - 用户手机号为空 ===");

        // 使用一个手机号为空的用户ID
        Long userId = 994L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("手机号为空测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证手机号字段的处理
            log.info("手机号字段: {}", profile.getMobile());
            // 手机号为空时，脱敏处理分支不会执行，字段应该为null
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户邮箱为空（False分支）
     * 目标：覆盖 if (StrUtil.isNotBlank(userInfo.getEmail())) 的False分支
     */
    @Test
    public void testGetUserProfileWithEmptyEmail() {
        log.info("=== 测试获取用户个人中心信息 - 用户邮箱为空（False分支） ===");

        // 测试多个可能邮箱为空的用户ID
        Long[] testUserIds = {6L};

        boolean foundEmptyEmailUser = false;

        for (Long userId : testUserIds) {
            log.info("测试用户ID: {}", userId);

            // 调用服务方法
            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            log.info("用户ID {} 测试结果: {}", userId, JSON.toJSONString(result));

            // 验证结果
            if (result != null && result.getCode() == 200 && result.getData() != null) {
                UserProfileVo profile = result.getData();
                log.info("用户ID {} 邮箱字段: {}", userId, profile.getEmail());

                // 如果邮箱字段为null，说明覆盖了False分支
                if (profile.getEmail() == null) {
                    log.info("✓ 成功覆盖邮箱为空的False分支，用户ID: {}", userId);
                    foundEmptyEmailUser = true;

                    // 验证邮箱为空时，脱敏处理分支不会执行，字段应该为null
                    assertThat(profile.getEmail()).isNull();
                    break;
                }
            } else if (result != null && result.getCode() != 200) {
                log.info("用户ID {} 不存在或查询失败: {}", userId, result.getMessage());
            }
        }

        if (!foundEmptyEmailUser) {
            log.warn("⚠ 未找到邮箱为空的用户，可能需要准备测试数据");
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户邮箱为空
     */
    @Test
    public void testGetUserProfileWithNotEmptyEmail() {
        log.info("=== 测试获取用户个人中心信息 - 用户邮箱不为空 ===");

        // 使用一个邮箱不为空的用户ID
        Long userId = 5L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("邮箱不为空测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 边界值测试
     */
    @Test
    public void testGetUserProfileBoundaryValues() {
        log.info("=== 测试获取用户个人中心信息 - 边界值测试 ===");

        // 测试各种边界值
        Long[] boundaryUserIds = {0L, -1L, Long.MAX_VALUE, Long.MIN_VALUE};

        for (Long userId : boundaryUserIds) {
            log.info("测试边界值用户ID: {}", userId);

            try {
                ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
                log.info("边界值 {} 测试结果: {}", userId,
                        result != null ? result.getCode() : "null");

                // 验证结果不为null
                assertThat(result).isNotNull();

                // 对于无效的用户ID，应该返回用户不存在的错误
                if (userId <= 0) {
                    // 预期返回用户不存在的错误
                    log.info("边界值 {} 返回预期的错误结果", userId);
                }

            } catch (Exception e) {
                log.warn("边界值 {} 测试出现异常: {}", userId, e.getMessage());
                // 边界值测试出现异常是可以接受的
            }
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据完整性验证
     */
    @Test
    public void testGetUserProfileDataIntegrity() {
        log.info("=== 测试获取用户个人中心信息 - 数据完整性验证 ===");

        Long userId = 1L; // 使用存在的用户ID

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("数据完整性测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试获取用户个人中心信息 - 组合场景测试
     */
    @Test
    public void testGetUserProfileCombinationScenarios() {
        log.info("=== 测试获取用户个人中心信息 - 组合场景测试 ===");

        // 测试不同的用户ID组合，覆盖各种数据组合情况
        Long[] testUserIds = {1L, 2L, 3L, 4L, 5L};

        for (Long userId : testUserIds) {
            log.info("测试组合场景，用户ID: {}", userId);

            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            if (result != null) {
                log.info("用户ID {} 测试结果码: {}", userId, result.getCode());

                if (result.getCode() == 200 && result.getData() != null) {
                    UserProfileVo profile = result.getData();

                    // 记录各个字段的状态，用于分析覆盖情况
                    log.info("用户ID {}: 推荐方信息={}, 微信绑定={}, 银行信息={}, 手机号={}, 邮箱={}",
                            userId,
                            profile.getName() != null ? "存在" : "不存在",
                            profile.getWechatBindStatus(),
                            profile.getBankCardNo() != null ? "存在" : "不存在",
                            profile.getMobile() != null ? "存在" : "不存在",
                            profile.getEmail() != null ? "存在" : "不存在"
                    );
                }
            }
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据库查询异常模拟
     * 注意：这个测试需要使用Mock来模拟异常情况
     */
    @Test
    public void testGetUserProfileWithDatabaseException() {
        log.info("=== 测试获取用户个人中心信息 - 数据库查询异常模拟 ===");

        // 这里可以通过以下方式模拟异常：
        // 1. 使用无效的数据库连接
        // 2. 使用Mock框架模拟异常
        // 3. 使用特殊的测试数据触发异常

        Long userId = 1L;
        Long recommenderId = 1L;

        try {
            // 在实际项目中，可以通过以下方式模拟异常：
            // - 临时关闭数据库连接
            // - 使用@MockBean注解Mock相关的Mapper
            // - 使用TestContainers模拟数据库故障
            // Mock推荐方信息查询抛出异常
            LambdaQueryWrapper<RecommenderInfo> recommenderWrapper = new LambdaQueryWrapper<>();
            recommenderWrapper.eq(RecommenderInfo::getUserId, userId)
                    .eq(RecommenderInfo::getIsDel, 0);
            when(recommenderMapper.selectOne(recommenderWrapper))
                    .thenThrow(new DataAccessException("数据库连接异常") {
                    });

            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            log.info("异常模拟测试结果: {}", JSON.toJSONString(result));

            // 验证异常处理是否正确
            assertThat(result).isNotNull();

        } catch (Exception e) {
            log.info("捕获到预期的异常: {}", e.getMessage());
            // 验证异常类型和消息
            assertThat(e).isNotNull();
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据脱敏异常模拟
     */
    @Test
    public void testGetUserProfileWithMaskingException() {
        log.info("=== 测试获取用户个人中心信息 - 数据脱敏异常模拟 ===");

        // 这个测试主要验证当数据脱敏工具出现异常时的处理
        // 在实际项目中，可以通过Mock DataMaskingUtil来模拟异常

        Long userId = 1L;

        try {
            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            log.info("数据脱敏异常测试结果: {}", JSON.toJSONString(result));

            // 验证结果
            assertThat(result).isNotNull();

            // 如果脱敏工具异常，应该通过全局异常处理返回错误信息
            if (result.getCode() != 200) {
                log.info("数据脱敏异常被正确处理，返回错误码: {}", result.getCode());
            }

        } catch (Exception e) {
            log.info("数据脱敏异常被捕获: {}", e.getMessage());
            assertThat(e).isNotNull();
        }
    }

    /**
     * 测试getUserProfile - 微信绑定状态三元运算符完整分支覆盖
     * 目标：覆盖 Objects.equals(wechatUser.getSource(), QrChannelBindEnum.BIND_WECHAT.getDesc()) ? 1 : 0 的两个分支
     */
    @Test
    public void testGetUserProfileWechatBindStatusTernaryOperator() {
        log.info("=== 测试getUserProfile - 微信绑定状态三元运算符完整分支覆盖 ===");

        Long userId = 1L;

        // 测试场景1: wechatUser存在且source匹配 (三元运算符True分支)
        log.info("--- 测试场景1: 微信用户存在且source匹配 ---");
        SocialUser wechatUserMatched = new SocialUser();
        wechatUserMatched.setSource(QrChannelBindEnum.BIND_WECHAT.getDesc());

        when(socialUserMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(wechatUserMatched);

        ResponseResult<UserProfileVo> result1 = userInfoService.getUserProfile(userId);

        if (result1 != null && result1.getCode() == 200 && result1.getData() != null) {
            UserProfileVo profile1 = result1.getData();
            log.info("微信绑定状态 (source匹配): {}", profile1.getWechatBindStatus());
            assertThat(profile1.getWechatBindStatus()).isEqualTo(1);
            log.info("✅ 三元运算符True分支覆盖成功");
        }

        // 重置Mock
        reset(socialUserMapper);

        // 测试场景2: wechatUser存在但source不匹配 (三元运算符False分支)
        log.info("--- 测试场景2: 微信用户存在但source不匹配 ---");
        SocialUser wechatUserNotMatched = new SocialUser();
        wechatUserNotMatched.setSource("OTHER_PLATFORM");

        when(socialUserMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(wechatUserNotMatched);

        ResponseResult<UserProfileVo> result2 = userInfoService.getUserProfile(userId);

        if (result2 != null && result2.getCode() == 200 && result2.getData() != null) {
            UserProfileVo profile2 = result2.getData();
            log.info("微信绑定状态 (source不匹配): {}", profile2.getWechatBindStatus());
            assertThat(profile2.getWechatBindStatus()).isEqualTo(0);
            log.info("✅ 三元运算符False分支覆盖成功");
        }

        // 重置Mock
        reset(socialUserMapper);

        // 测试场景3: wechatUser为null (外层if条件False分支)
        log.info("--- 测试场景3: 微信用户不存在 ---");
        when(socialUserMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(null);

        ResponseResult<UserProfileVo> result3 = userInfoService.getUserProfile(userId);

        if (result3 != null && result3.getCode() == 200 && result3.getData() != null) {
            UserProfileVo profile3 = result3.getData();
            log.info("微信绑定状态 (用户不存在): {}", profile3.getWechatBindStatus());
            // 当wechatUser为null时，不会设置微信绑定状态，应该为默认值
            log.info("✅ 微信用户不存在分支覆盖成功");
        }

        log.info("微信绑定状态三元运算符分支覆盖测试完成");
    }
    /**
     * 测试getUserProfile - 数据脱敏分支完整覆盖
     * 目标：确保手机号和邮箱脱敏的True/False分支都被覆盖
     */
    @Test
    public void testGetUserProfileDataMaskingBranchesComplete() {
        log.info("=== 测试getUserProfile - 数据脱敏分支完整覆盖 ===");

        // 测试多个用户ID，确保覆盖所有脱敏分支
        Long[] userIds = {1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L};

        boolean mobileNotBlankCovered = false;
        boolean mobileBlankCovered = false;
        boolean emailNotBlankCovered = false;
        boolean emailBlankCovered = false;

        for (Long userId : userIds) {
            try {
                ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

                if (result != null && result.getCode() == 200 && result.getData() != null) {
                    UserProfileVo profile = result.getData();
                    String mobile = profile.getMobile();
                    String email = profile.getEmail();

                    // 检查手机号脱敏分支覆盖
                    if (mobile != null && mobile.contains("*")) {
                        mobileNotBlankCovered = true;
                        log.info("✅ 手机号不为空分支覆盖 - 用户ID: {}, 脱敏手机号: {}", userId, mobile);
                    } else if (mobile == null) {
                        mobileBlankCovered = true;
                        log.info("✅ 手机号为空分支覆盖 - 用户ID: {}", userId);
                    }

                    // 检查邮箱脱敏分支覆盖
                    if (email != null && email.contains("*")) {
                        emailNotBlankCovered = true;
                        log.info("✅ 邮箱不为空分支覆盖 - 用户ID: {}, 脱敏邮箱: {}", userId, email);
                    } else if (email == null) {
                        emailBlankCovered = true;
                        log.info("✅ 邮箱为空分支覆盖 - 用户ID: {}", userId);
                    }
                }
            } catch (Exception e) {
                log.debug("用户ID {} 测试异常: {}", userId, e.getMessage());
            }
        }

        // 分支覆盖情况报告
        log.info("=== 数据脱敏分支覆盖情况报告 ===");
        log.info("手机号不为空分支 (True): {}", mobileNotBlankCovered ? "✅ 已覆盖" : "❌ 未覆盖");
        log.info("手机号为空分支 (False): {}", mobileBlankCovered ? "✅ 已覆盖" : "❌ 未覆盖");
        log.info("邮箱不为空分支 (True): {}", emailNotBlankCovered ? "✅ 已覆盖" : "❌ 未覆盖");
        log.info("邮箱为空分支 (False): {}", emailBlankCovered ? "✅ 已覆盖" : "❌ 未覆盖");

        // 验证至少覆盖了一些分支
        boolean anyBranchCovered = mobileNotBlankCovered || mobileBlankCovered ||
                emailNotBlankCovered || emailBlankCovered;
        assertThat(anyBranchCovered).isTrue();

        if (mobileNotBlankCovered && mobileBlankCovered && emailNotBlankCovered && emailBlankCovered) {
            log.info("🎉 所有数据脱敏分支都已覆盖！");
        } else {
            log.warn("⚠️ 部分数据脱敏分支未覆盖，可能需要准备更多测试数据");
        }
    }

    /**
     * 测试getUserProfile - 银行信息分支完整覆盖
     * 目标：覆盖银行信息存在和不存在的两个分支
     */
    @Test
    public void testGetUserProfileBankInfoBranchesComplete() {
        log.info("=== 测试getUserProfile - 银行信息分支完整覆盖 ===");

        Long userId = 1L;

        // 测试场景1: 推荐方存在且有银行信息
        log.info("--- 测试场景1: 推荐方存在且有银行信息 ---");
        RecommenderBank bankInfo = new RecommenderBank();
        bankInfo.setBankCardNo("6222****1234");
        bankInfo.setBankName("工商银行");
        bankInfo.setValidateStatus(1);

        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(bankInfo);

        ResponseResult<UserProfileVo> result1 = userInfoService.getUserProfile(userId);

        if (result1 != null && result1.getCode() == 200 && result1.getData() != null) {
            UserProfileVo profile1 = result1.getData();
            log.info("银行信息存在 - 银行卡号: {}, 银行名称: {}, 验证状态: {}",
                    profile1.getBankCardNo(), profile1.getBankName(), profile1.getBankValidateStatus());

            assertThat(profile1.getBankCardNo()).isNotNull();
            assertThat(profile1.getBankName()).isEqualTo("工商银行");
            assertThat(profile1.getBankValidateStatus()).isEqualTo(1);
            log.info("✅ 银行信息存在分支 (True) 覆盖成功");
        }

        // 重置Mock
        reset(recommenderBankMapper);

        // 测试场景2: 推荐方存在但无银行信息
        log.info("--- 测试场景2: 推荐方存在但无银行信息 ---");
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);

        ResponseResult<UserProfileVo> result2 = userInfoService.getUserProfile(userId);

        if (result2 != null && result2.getCode() == 200 && result2.getData() != null) {
            UserProfileVo profile2 = result2.getData();
            log.info("银行信息不存在 - 银行卡号: {}, 银行名称: {}, 验证状态: {}",
                    profile2.getBankCardNo(), profile2.getBankName(), profile2.getBankValidateStatus());

            assertThat(profile2.getBankCardNo()).isNull();
            assertThat(profile2.getBankName()).isNull();
            assertThat(profile2.getBankValidateStatus()).isNull();
            log.info("✅ 银行信息不存在分支 (False) 覆盖成功");
        }

        log.info("银行信息分支完整覆盖测试完成");
    }

    /**
     * 测试getUserProfile - 推荐方信息分支完整覆盖
     * 目标：覆盖推荐方信息存在和不存在的两个分支
     */
    @Test
    public void testGetUserProfileRecommenderInfoBranchesComplete() {
        log.info("=== 测试getUserProfile - 推荐方信息分支完整覆盖 ===");

        Long userId = 1L;

        // 测试场景1: 推荐方信息存在
        log.info("--- 测试场景1: 推荐方信息存在 ---");
        // 这里依赖实际的数据库数据，因为recommenderMapper没有被Mock

        ResponseResult<UserProfileVo> result1 = userInfoService.getUserProfile(userId);

        if (result1 != null && result1.getCode() == 200 && result1.getData() != null) {
            UserProfileVo profile1 = result1.getData();

            if (profile1.getName() != null || profile1.getIdentityType() != null ||
                    profile1.getInvitationCode() != null || profile1.getIdentifier() != null) {
                log.info("✅ 推荐方信息存在分支 (True) 覆盖成功");
                log.info("推荐方姓名: {}, 身份类型: {}, 邀请码: {}, 标识符: {}",
                        profile1.getName(), profile1.getIdentityType(),
                        profile1.getInvitationCode(), profile1.getIdentifier());
            } else {
                log.info("✅ 推荐方信息不存在分支 (False) 覆盖成功");
                log.info("推荐方相关字段均为null");
            }
        }

        // 测试多个用户ID以确保覆盖不同情况
        Long[] testUserIds = {2L, 3L, 4L, 5L, 6L};
        boolean hasRecommenderInfoCovered = false;
        boolean noRecommenderInfoCovered = false;

        for (Long testUserId : testUserIds) {
            try {
                ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(testUserId);

                if (result != null && result.getCode() == 200 && result.getData() != null) {
                    UserProfileVo profile = result.getData();

                    if (profile.getName() != null || profile.getIdentityType() != null) {
                        hasRecommenderInfoCovered = true;
                        log.info("用户ID {} 有推荐方信息", testUserId);
                    } else {
                        noRecommenderInfoCovered = true;
                        log.info("用户ID {} 无推荐方信息", testUserId);
                    }
                }
            } catch (Exception e) {
                log.debug("用户ID {} 测试异常: {}", testUserId, e.getMessage());
            }
        }

        log.info("=== 推荐方信息分支覆盖情况 ===");
        log.info("推荐方信息存在分支: {}", hasRecommenderInfoCovered ? "✅ 已覆盖" : "❌ 未覆盖");
        log.info("推荐方信息不存在分支: {}", noRecommenderInfoCovered ? "✅ 已覆盖" : "❌ 未覆盖");
    }

    /**
     * 测试getUserProfile - 数据脱敏工具异常分支覆盖
     * 目标：覆盖数据脱敏工具抛出异常的情况
     */
    @Test
    public void testGetUserProfileDataMaskingUtilException() {
        log.info("=== 测试getUserProfile - 数据脱敏工具异常分支覆盖 ===");

        Long userId = 1L;

        try {
            // 使用MockedStatic来Mock静态方法DataMaskingUtil
            try (MockedStatic<DataMaskingUtil> mockedDataMaskingUtil = mockStatic(DataMaskingUtil.class)) {

                // Mock手机号脱敏方法抛出异常
                mockedDataMaskingUtil.when(() -> DataMaskingUtil.maskMobile(any()))
                        .thenThrow(new RuntimeException("手机号脱敏异常"));

                // 调用服务方法
                ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

                log.info("数据脱敏异常测试结果: {}", JSON.toJSONString(result));

                // 验证异常处理结果
                assertThat(result).isNotNull();
                assertThat(result.getCode()).isNotEqualTo(200);
                assertThat(result.getMessage()).isEqualTo("获取用户个人中心信息失败");
                assertThat(result.getData()).isNull();

                log.info("✅ 数据脱敏工具异常处理验证通过");
            }

        } catch (Exception e) {
            log.info("数据脱敏异常测试中捕获异常: {}", e.getMessage());
            // 如果DataMaskingUtil无法Mock，记录信息但不失败测试
        }
    }

    /**
     * 测试getUserProfile - 完整的数据组合场景测试
     * 目标：测试各种数据存在/不存在的组合情况
     */
    @Test
    public void testGetUserProfileDataCombinationScenarios() {
        log.info("=== 测试getUserProfile - 完整的数据组合场景测试 ===");

        // 测试多个用户ID，覆盖各种数据组合
        Long[] userIds = {1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L};

        int totalScenarios = 0;
        int successfulScenarios = 0;

        for (Long userId : userIds) {
            try {
                totalScenarios++;
                ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

                if (result != null && result.getCode() == 200 && result.getData() != null) {
                    successfulScenarios++;
                    UserProfileVo profile = result.getData();

                    // 记录数据组合情况
                    String scenario = String.format(
                            "用户ID %d: 推荐方=%s, 微信=%s, 银行=%s, 手机=%s, 邮箱=%s",
                            userId,
                            profile.getName() != null ? "有" : "无",
                            profile.getWechatBindStatus() != null ? profile.getWechatBindStatus().toString() : "未设置",
                            profile.getBankCardNo() != null ? "有" : "无",
                            profile.getMobile() != null ? "有" : "无",
                            profile.getEmail() != null ? "有" : "无"
                    );

                    log.info("✅ {}", scenario);

                    // 验证数据一致性
                    assertThat(profile.getUserId()).isEqualTo(userId);

                    // 验证脱敏处理
                    if (profile.getMobile() != null) {
                        assertThat(profile.getMobile()).contains("*");
                    }
                    if (profile.getEmail() != null) {
                        assertThat(profile.getEmail()).contains("*");
                    }
                    if (profile.getBankCardNo() != null) {
                        assertThat(profile.getBankCardNo()).contains("*");
                    }

                } else if (result != null) {
                    log.info("用户ID {} 查询失败: {}", userId, result.getMessage());
                }

            } catch (Exception e) {
                log.debug("用户ID {} 测试异常: {}", userId, e.getMessage());
            }
        }

        log.info("=== 数据组合场景测试统计 ===");
        log.info("总测试场景数: {}", totalScenarios);
        log.info("成功场景数: {}", successfulScenarios);
        log.info("成功率: {}%", successfulScenarios * 100.0 / totalScenarios);

        // 验证至少有一些成功的场景
        assertThat(successfulScenarios).isGreaterThan(0);
    }
}
