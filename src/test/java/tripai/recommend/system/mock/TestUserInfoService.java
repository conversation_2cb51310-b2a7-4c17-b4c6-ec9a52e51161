package tripai.recommend.system.mock;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.config.AuthDefaultSource;
import me.zhyd.oauth.model.AuthUser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.vo.AuthCallBackVo;
import tripai.recommend.system.domain.vo.user.*;
import tripai.recommend.system.service.UserInfoService;

import static org.assertj.core.api.Assertions.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestUserInfoService
 * @author: lijunqi
 * @description: UserInfoService测试类
 * @date: 2025/7/28 18:30
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestUserInfoService {

    @Resource
    private UserInfoService userInfoService;

    // ==================== 邮箱相关测试 ====================

    /**
     * 测试发送绑定邮箱验证码 - 正常流程
     */
    @Test
    public void testSendBoundEmailCodeSuccess() {
        log.info("=== 测试发送绑定邮箱验证码 - 正常流程 ===");

        String email = "<EMAIL>";

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.sendBoundEmailCode(email);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试发送绑定邮箱验证码 - 无效邮箱
     */
    @Test
    public void testSendBoundEmailCodeWithInvalidEmail() {
        log.info("=== 测试发送绑定邮箱验证码 - 无效邮箱 ===");

        String email = "invalid-email";

        // 调用服务方法
        ResponseResult<Boolean> result = userInfoService.sendBoundEmailCode(email);
        log.info("无效邮箱测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试修改绑定邮箱 - 正常流程
     */
    @Test
    public void testEditEmailSuccess() {
        log.info("=== 测试修改绑定邮箱 - 正常流程 ===");

        // 构建测试数据
        EditUserEmailVo vo = buildEditUserEmailVo();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.editEmail(vo, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 短信验证码相关测试 ====================

    /**
     * 测试发送登录注册短信 - 正常流程
     */
    @Test
    public void testSendLoginSmsSuccess() {
        log.info("=== 测试发送登录注册短信 - 正常流程 ===");

        // 构建测试数据
        CaptchaCodeVo vo = buildCaptchaCodeVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.sendLoginSms(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试发送重置密码短信 - 正常流程
     */
    @Test
    public void testSendResetPasswordSmsSuccess() {
        log.info("=== 测试发送重置密码短信 - 正常流程 ===");

        // 构建测试数据
        CaptchaCodeVo vo = buildCaptchaCodeVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.sendResetPasswordSms(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 登录相关测试 ====================

    /**
     * 测试渠道登录 - 正常流程
     */
    @Test
    public void testChannelLoginSuccess() {
        log.info("=== 测试渠道登录 - 正常流程 ===");

        // 构建测试数据
        AuthUser authUser = buildAuthUser();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.login(authUser);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试手机号登录 - 正常流程
     */
    @Test
    public void testMobileLoginSuccess() {
        log.info("=== 测试手机号登录 - 正常流程 ===");

        // 构建测试数据
        MobileLoginVo vo = buildMobileLoginVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.login(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试用户名密码登录 - 正常流程
     */
    @Test
    public void testUsernameLoginSuccess() {
        log.info("=== 测试用户名密码登录 - 正常流程 ===");

        // 构建测试数据
        UsernameLoginVo vo = buildUsernameLoginVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.login(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 注册相关测试 ====================

    /**
     * 测试扫码注册 - 正常流程
     */
    @Test
    public void testScanQrRegisterSuccess() {
        log.info("=== 测试扫码注册 - 正常流程 ===");

        // 构建测试数据
        ScanQrRegisterVo vo = buildScanQrRegisterVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.register(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试手机号注册 - 正常流程
     */
    @Test
    public void testMobileRegisterSuccess() {
        log.info("=== 测试手机号注册 - 正常流程 ===");

        // 构建测试数据
        MobileRegisterVo vo = buildMobileRegisterVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.register(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试手机号注册 - 密码不一致
     */
    @Test
    public void testMobileRegisterWithPasswordMismatch() {
        log.info("=== 测试手机号注册 - 密码不一致 ===");

        // 构建测试数据
        MobileRegisterVo vo = buildMobileRegisterVo();
        vo.setPassword("password123");
        vo.setConfirmPassword("password456"); // 不一致的密码

        // 调用服务方法
        ResponseResult<LoginResultVo> result = userInfoService.register(vo);

        log.info("密码不一致测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
    }

    // ==================== 密码重置测试 ====================

    /**
     * 测试重设密码 - 正常流程
     */
    @Test
    public void testResetPasswordSuccess() {
        log.info("=== 测试重设密码 - 正常流程 ===");

        // 构建测试数据
        MobileRegisterVo vo = buildMobileRegisterVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.resetPassword(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 用户信息修改测试 ====================

    /**
     * 测试修改昵称 - 正常流程
     */
    @Test
    public void testEditNicknameSuccess() {
        log.info("=== 测试修改昵称 - 正常流程 ===");

        String newNickname = "新昵称" + System.currentTimeMillis();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.editNickname(newNickname, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试修改手机号 - 正常流程
     */
    @Test
    public void testEditMobileSuccess() {
        log.info("=== 测试修改手机号 - 正常流程 ===");

        // 构建测试数据
        MobileLoginVo vo = buildMobileLoginVo();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.editMobile(vo, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试修改头像 - 正常流程
     */
    @Test
    public void testEditHeadImageSuccess() {
        log.info("=== 测试修改头像 - 正常流程 ===");

        // 构建测试数据
        EditUserHeadImageVo vo = buildEditUserHeadImageVo();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<String> result = userInfoService.editHeadImage(vo, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 绑定/解绑测试 ====================

    /**
     * 测试用户绑定渠道 - 正常流程
     */
    @Test
    public void testBoundUserSuccess() {
        log.info("=== 测试用户绑定渠道 - 正常流程 ===");

        // 构建测试数据
        AuthUser authUser = buildAuthUser();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.boundUser(authUser, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试解绑邮箱 - 正常流程
     */
    @Test
    public void testUnbindEmailSuccess() {
        log.info("=== 测试解绑邮箱 - 正常流程 ===");

        String token = buildValidToken();
        int op = 0; // 0 = 邮箱

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.unbindEmailOrQrChannel(token, op);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试解绑微信渠道 - 正常流程
     */
    @Test
    public void testUnbindWechatChannelSuccess() {
        log.info("=== 测试解绑微信渠道 - 正常流程 ===");

        String token = buildValidToken();
        int op = 1; // 1 = 微信渠道

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.unbindEmailOrQrChannel(token, op);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 加密解密测试 ====================

    /**
     * 测试手机号AES加密 - 正常流程
     */
    @Test
    public void testMobileAesEncryptSuccess() {
        log.info("=== 测试手机号AES加密 - 正常流程 ===");

        // 构建测试数据
        MobileInfo mobileInfo = buildMobileInfo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        String encryptedMobile = userInfoService.mobileAesEncryptPublic(mobileInfo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("原始手机号: {}", JSON.toJSONString(mobileInfo));
        log.info("加密后手机号: {}", encryptedMobile);

        // 验证结果
        assertThat(encryptedMobile).isNotNull();
        assertThat(encryptedMobile).isNotEmpty();
    }

    /**
     * 测试手机号AES解密 - 正常流程
     */
    @Test
    public void testMobileAesDecryptSuccess() {
        log.info("=== 测试手机号AES解密 - 正常流程 ===");

        // 先加密一个手机号
        MobileInfo originalMobileInfo = buildMobileInfo();
        String encryptedMobile = userInfoService.mobileAesEncryptPublic(originalMobileInfo);

        // 调用解密服务方法
        long startTime = System.currentTimeMillis();
        MobileInfo decryptedMobileInfo = userInfoService.mobileAesDecryptPublic(encryptedMobile);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("加密手机号: {}", encryptedMobile);
        log.info("解密后手机号: {}", JSON.toJSONString(decryptedMobileInfo));

    }

    // ==================== 用户个人中心测试 ====================

    /**
     * 测试获取用户个人中心信息 - 正常流程
     */
    @Test
    public void testGetUserProfileSuccess() {
        log.info("=== 测试获取用户个人中心信息 - 正常流程 ===");

        Long userId = 1L; // 使用存在的用户ID

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试获取用户个人中心信息 - 无效用户ID
     */
    @Test
    public void testGetUserProfileWithInvalidUserId() {
        log.info("=== 测试获取用户个人中心信息 - 无效用户ID ===");

        Long userId = -1L; // 无效的用户ID

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("无效用户ID测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 空用户ID
     */
    @Test
    public void testGetUserProfileWithNullUserId() {
        log.info("=== 测试获取用户个人中心信息 - 空用户ID ===");

        Long userId = null;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("空用户ID测试结果: {}", JSON.toJSONString(result));

    }

    // ==================== 测试辅助方法 ====================

    /**
     * 构建EditUserEmailVo测试数据
     */
    private EditUserEmailVo buildEditUserEmailVo() {
        EditUserEmailVo vo = new EditUserEmailVo();
        vo.setEmail("<EMAIL>");
        vo.setVerificationCode("123456");
        return vo;
    }

    /**
     * 构建CaptchaCodeVo测试数据
     */
    private CaptchaCodeVo buildCaptchaCodeVo() {
        CaptchaCodeVo vo = new CaptchaCodeVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setCaptchaInfo(buildCaptchaVo());
        return vo;
    }

    /**
     * 构建CaptchaVo测试数据
     */
    private CaptchaVo buildCaptchaVo() {
        CaptchaVo vo = new CaptchaVo();
        vo.setSign("test-sign-" + System.currentTimeMillis());
        vo.setCaptcha("1234");
        return vo;
    }

    /**
     * 构建MobileInfo测试数据
     */
    private MobileInfo buildMobileInfo() {
        MobileInfo mobileInfo = new MobileInfo();
        mobileInfo.setMobile("13800138000");
        return mobileInfo;
    }

    /**
     * 构建AuthUser测试数据
     */
    private AuthUser buildAuthUser() {
        AuthUser authUser = new AuthUser();
        authUser.setUuid("test-uuid-" + System.currentTimeMillis());
        authUser.setUsername("testuser");
        authUser.setNickname("测试用户");
        authUser.setAvatar("https://example.com/avatar.jpg");
        authUser.setEmail("<EMAIL>");
        authUser.setSource(AuthDefaultSource.WECHAT_OPEN.toString());
        return authUser;
    }

    /**
     * 构建MobileLoginVo测试数据
     */
    private MobileLoginVo buildMobileLoginVo() {
        MobileLoginVo vo = new MobileLoginVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setVerificationCode("123456");
        return vo;
    }

    /**
     * 构建UsernameLoginVo测试数据
     */
    private UsernameLoginVo buildUsernameLoginVo() {
        UsernameLoginVo vo = new UsernameLoginVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setPassword("password123");
        return vo;
    }

    /**
     * 构建ScanQrRegisterVo测试数据
     */
    private ScanQrRegisterVo buildScanQrRegisterVo() {
        ScanQrRegisterVo vo = new ScanQrRegisterVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setVerificationCode("123456");
        vo.setAuthCallBackValue(buildAuthCallBackVo());
        vo.setSource("wechat");
        return vo;
    }

    /**
     * 构建AuthCallBackVo测试数据
     */
    private AuthCallBackVo buildAuthCallBackVo() {
        AuthCallBackVo vo = new AuthCallBackVo();
        // 根据实际的AuthCallBackVo字段设置测试数据
        return vo;
    }

    /**
     * 构建MobileRegisterVo测试数据
     */
    private MobileRegisterVo buildMobileRegisterVo() {
        MobileRegisterVo vo = new MobileRegisterVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setVerificationCode("123456");
        vo.setPassword("password123");
        vo.setConfirmPassword("password123");
        return vo;
    }

    /**
     * 构建EditUserHeadImageVo测试数据
     */
    private EditUserHeadImageVo buildEditUserHeadImageVo() {
        EditUserHeadImageVo vo = new EditUserHeadImageVo();
        // 注意：这里需要MockMultipartFile，在实际测试中可能需要mock
        // vo.setImage(mockMultipartFile);
        return vo;
    }

    /**
     * 构建有效的token（用于测试）
     */
    private String buildValidToken() {
        // 这里应该返回一个有效的JWT token
        // 在实际测试中，可能需要使用测试用的token或者mock JwtUtils
        return "test-token-" + System.currentTimeMillis();
    }

    // ==================== 边界条件和异常测试 ====================

    /**
     * 测试发送登录短信 - 验证码错误
     */
    @Test
    public void testSendLoginSmsWithInvalidCaptcha() {
        log.info("=== 测试发送登录短信 - 验证码错误 ===");

        // 构建测试数据
        CaptchaCodeVo vo = buildCaptchaCodeVo();
        vo.getCaptchaInfo().setCaptcha("wrong-captcha");

        // 调用服务方法
        ResponseResult<Boolean> result = userInfoService.sendLoginSms(vo);

        log.info("验证码错误测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
    }

    /**
     * 测试手机号登录 - 验证码错误
     */
    @Test
    public void testMobileLoginWithInvalidVerificationCode() {
        log.info("=== 测试手机号登录 - 验证码错误 ===");

        // 构建测试数据
        MobileLoginVo vo = buildMobileLoginVo();
        vo.setVerificationCode("wrong-code");

        // 调用服务方法
        ResponseResult<LoginResultVo> result = userInfoService.login(vo);

        log.info("验证码错误测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
    }

    /**
     * 测试修改昵称 - 无效token
     */
    @Test
    public void testEditNicknameWithInvalidToken() {
        log.info("=== 测试修改昵称 - 无效token ===");

        String newNickname = "新昵称";
        String invalidToken = "invalid-token";

        // 调用服务方法
        ResponseResult<Boolean> result = userInfoService.editNickname(newNickname, invalidToken);

        log.info("无效token测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试解绑 - 无效操作类型
     */
    @Test
    public void testUnbindWithInvalidOperation() {
        log.info("=== 测试解绑 - 无效操作类型 ===");

        String token = buildValidToken();
        int invalidOp = 999; // 无效的操作类型

        // 调用服务方法
        ResponseResult<Boolean> result = userInfoService.unbindEmailOrQrChannel(token, invalidOp);

        log.info("无效操作类型测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试手机号加密解密 - 完整流程
     */
    @Test
    public void testMobileEncryptDecryptFullFlow() {
        log.info("=== 测试手机号加密解密 - 完整流程 ===");

        // 构建测试数据
        MobileInfo originalMobileInfo = buildMobileInfo();
        log.info("原始手机号信息: {}", JSON.toJSONString(originalMobileInfo));

        // 加密
        String encryptedMobile = userInfoService.mobileAesEncryptPublic(originalMobileInfo);
        log.info("加密后: {}", encryptedMobile);
        assertThat(encryptedMobile).isNotNull();
        assertThat(encryptedMobile).isNotEmpty();

        // 解密
        MobileInfo decryptedMobileInfo = userInfoService.mobileAesDecryptPublic(encryptedMobile);
        log.info("解密后手机号信息: {}", JSON.toJSONString(decryptedMobileInfo));
        log.info("手机号加密解密测试通过");
    }

    /**
     * 测试批量操作 - 多个用户个人中心信息查询
     */
    @Test
    public void testGetMultipleUserProfiles() {
        log.info("=== 测试批量操作 - 多个用户个人中心信息查询 ===");

        Long[] userIds = {1L, 2L, 3L, 4L, 5L};

        for (Long userId : userIds) {
            log.info("查询用户ID: {}", userId);

            long startTime = System.currentTimeMillis();
            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
            long endTime = System.currentTimeMillis();

            log.info("用户ID: {}, 执行时间: {}ms, 结果: {}",
                    userId, endTime - startTime,
                    result != null ? result.getCode() : "null");

            if (result != null && result.getData() != null) {
                UserProfileVo profile = result.getData();
                log.info("用户信息 - ID: {}, 姓名: {}, 手机号: {}",
                        profile.getUserId(), profile.getName(), profile.getMobile());
            }
        }
    }

    /**
     * 测试性能 - 用户个人中心信息查询性能测试
     */
    @Test
    public void testGetUserProfilePerformance() {
        log.info("=== 测试性能 - 用户个人中心信息查询性能测试 ===");

        Long userId = 1L;
        int testCount = 10;
        long totalTime = 0;

        for (int i = 0; i < testCount; i++) {
            long startTime = System.currentTimeMillis();
            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
            long endTime = System.currentTimeMillis();

            long executionTime = endTime - startTime;
            totalTime += executionTime;

            log.info("第{}次查询，执行时间: {}ms", i + 1, executionTime);
        }

        long averageTime = totalTime / testCount;
        log.info("性能测试完成 - 总次数: {}, 总时间: {}ms, 平均时间: {}ms",
                testCount, totalTime, averageTime);

        // 验证平均响应时间应该在合理范围内（比如小于1000ms）
        assertThat(averageTime).isLessThan(1000);
    }

    // ==================== getUserProfile方法100%分支覆盖率测试 ====================

    /**
     * 测试获取用户个人中心信息 - 用户存在但推荐方信息不存在
     */
    @Test
    public void testGetUserProfileWithNoRecommenderInfo() {
        log.info("=== 测试获取用户个人中心信息 - 用户存在但推荐方信息不存在 ===");

        // 使用一个存在的用户ID，但该用户没有推荐方信息
        Long userId = 6L; // 假设这个用户存在但没有推荐方信息

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 用户未绑定微信
     */
    @Test
    public void testGetUserProfileWithNoWechatBinding() {
        log.info("=== 测试获取用户个人中心信息 - 用户未绑定微信 ===");

        // 使用一个未绑定微信的用户ID
        Long userId = 998L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("未绑定微信测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证微信绑定状态应该为0（未绑定）
            assertThat(profile.getWechatBindStatus()).isEqualTo(0);
            log.info("微信未绑定状态验证通过: {}", profile.getWechatBindStatus());
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户已绑定微信
     */
    @Test
    public void testGetUserProfileWithWechatBinding() {
        log.info("=== 测试获取用户个人中心信息 - 用户已绑定微信 ===");

        // 使用一个已绑定微信的用户ID
        Long userId = 997L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("已绑定微信测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证微信绑定状态应该为1（已绑定）
            assertThat(profile.getWechatBindStatus()).isEqualTo(1);
            log.info("微信已绑定状态验证通过: {}", profile.getWechatBindStatus());
        }
    }

    /**
     * 测试获取用户个人中心信息 - 推荐方存在但无银行信息
     */
    @Test
    public void testGetUserProfileWithNoBankInfo() {
        log.info("=== 测试获取用户个人中心信息 - 推荐方存在但无银行信息 ===");

        // 使用一个有推荐方信息但无银行信息的用户ID
        Long userId = 996L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("无银行信息测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证银行相关字段应该为null
            assertThat(profile.getBankCardNo()).isNull();
            assertThat(profile.getBankName()).isNull();
            assertThat(profile.getBankValidateStatus()).isNull();
            log.info("无银行信息的情况测试通过");
        }
    }

    /**
     * 测试获取用户个人中心信息 - 推荐方存在且有银行信息
     */
    @Test
    public void testGetUserProfileWithBankInfo() {
        log.info("=== 测试获取用户个人中心信息 - 推荐方存在且有银行信息 ===");

        // 使用一个有完整银行信息的用户ID
        Long userId = 995L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("有银行信息测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证银行信息字段不为null（如果数据存在的话）
            if (profile.getBankCardNo() != null) {
                assertThat(profile.getBankCardNo()).isNotEmpty();
                log.info("银行卡号: {}", profile.getBankCardNo());
            }
            if (profile.getBankName() != null) {
                assertThat(profile.getBankName()).isNotEmpty();
                log.info("银行名称: {}", profile.getBankName());
            }
            log.info("银行信息存在的情况测试通过");
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户手机号为空
     */
    @Test
    public void testGetUserProfileWithEmptyMobile() {
        log.info("=== 测试获取用户个人中心信息 - 用户手机号为空 ===");

        // 使用一个手机号为空的用户ID
        Long userId = 994L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("手机号为空测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证手机号字段的处理
            log.info("手机号字段: {}", profile.getMobile());
            // 手机号为空时，脱敏处理分支不会执行，字段应该为null
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户邮箱为空
     */
    @Test
    public void testGetUserProfileWithEmptyEmail() {
        log.info("=== 测试获取用户个人中心信息 - 用户邮箱为空 ===");

        // 使用一个邮箱为空的用户ID
        Long userId = 993L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("邮箱为空测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证邮箱字段的处理
            log.info("邮箱字段: {}", profile.getEmail());
            // 邮箱为空时，脱敏处理分支不会执行，字段应该为null
        }
    }

    /**
     * 测试获取用户个人中心信息 - 边界值测试
     */
    @Test
    public void testGetUserProfileBoundaryValues() {
        log.info("=== 测试获取用户个人中心信息 - 边界值测试 ===");

        // 测试各种边界值
        Long[] boundaryUserIds = {0L, -1L, Long.MAX_VALUE, Long.MIN_VALUE};

        for (Long userId : boundaryUserIds) {
            log.info("测试边界值用户ID: {}", userId);

            try {
                ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
                log.info("边界值 {} 测试结果: {}", userId,
                        result != null ? result.getCode() : "null");

                // 验证结果不为null
                assertThat(result).isNotNull();

                // 对于无效的用户ID，应该返回用户不存在的错误
                if (userId <= 0) {
                    // 预期返回用户不存在的错误
                    log.info("边界值 {} 返回预期的错误结果", userId);
                }

            } catch (Exception e) {
                log.warn("边界值 {} 测试出现异常: {}", userId, e.getMessage());
                // 边界值测试出现异常是可以接受的
            }
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据完整性验证
     */
    @Test
    public void testGetUserProfileDataIntegrity() {
        log.info("=== 测试获取用户个人中心信息 - 数据完整性验证 ===");

        Long userId = 1L; // 使用存在的用户ID

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("数据完整性测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();

        if (result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();

            // 验证必需字段
            assertThat(profile.getUserId()).isNotNull();
            log.info("用户ID验证通过: {}", profile.getUserId());

            // 验证微信绑定状态字段（必须有值，0或1）
            assertThat(profile.getWechatBindStatus()).isNotNull();
            assertThat(profile.getWechatBindStatus()).isIn(0, 1);
            log.info("微信绑定状态验证通过: {}", profile.getWechatBindStatus());

            // 验证数据脱敏效果（如果字段不为空）
            if (profile.getMobile() != null) {
                assertThat(profile.getMobile()).contains("*");
                log.info("手机号脱敏验证通过: {}", profile.getMobile());
            }

            if (profile.getEmail() != null) {
                assertThat(profile.getEmail()).contains("*");
                log.info("邮箱脱敏验证通过: {}", profile.getEmail());
            }

            if (profile.getBankCardNo() != null) {
                assertThat(profile.getBankCardNo()).contains("*");
                log.info("银行卡号脱敏验证通过: {}", profile.getBankCardNo());
            }
        }
    }

    /**
     * 测试获取用户个人中心信息 - 组合场景测试
     */
    @Test
    public void testGetUserProfileCombinationScenarios() {
        log.info("=== 测试获取用户个人中心信息 - 组合场景测试 ===");

        // 测试不同的用户ID组合，覆盖各种数据组合情况
        Long[] testUserIds = {1L, 2L, 3L, 4L, 5L};

        for (Long userId : testUserIds) {
            log.info("测试组合场景，用户ID: {}", userId);

            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            if (result != null) {
                log.info("用户ID {} 测试结果码: {}", userId, result.getCode());

                if (result.getCode() == 200 && result.getData() != null) {
                    UserProfileVo profile = result.getData();

                    // 记录各个字段的状态，用于分析覆盖情况
                    log.info("用户ID {}: 推荐方信息={}, 微信绑定={}, 银行信息={}, 手机号={}, 邮箱={}",
                            userId,
                            profile.getName() != null ? "存在" : "不存在",
                            profile.getWechatBindStatus(),
                            profile.getBankCardNo() != null ? "存在" : "不存在",
                            profile.getMobile() != null ? "存在" : "不存在",
                            profile.getEmail() != null ? "存在" : "不存在"
                    );
                }
            }
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据库查询异常模拟
     * 注意：这个测试需要使用Mock来模拟异常情况
     */
    @Test
    public void testGetUserProfileWithDatabaseException() {
        log.info("=== 测试获取用户个人中心信息 - 数据库查询异常模拟 ===");

        // 这里可以通过以下方式模拟异常：
        // 1. 使用无效的数据库连接
        // 2. 使用Mock框架模拟异常
        // 3. 使用特殊的测试数据触发异常

        Long userId = 1L;

        try {
            // 在实际项目中，可以通过以下方式模拟异常：
            // - 临时关闭数据库连接
            // - 使用@MockBean注解Mock相关的Mapper
            // - 使用TestContainers模拟数据库故障

            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            log.info("异常模拟测试结果: {}", JSON.toJSONString(result));

            // 验证异常处理是否正确
            assertThat(result).isNotNull();

        } catch (Exception e) {
            log.info("捕获到预期的异常: {}", e.getMessage());
            // 验证异常类型和消息
            assertThat(e).isNotNull();
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据脱敏异常模拟
     */
    @Test
    public void testGetUserProfileWithMaskingException() {
        log.info("=== 测试获取用户个人中心信息 - 数据脱敏异常模拟 ===");

        // 这个测试主要验证当数据脱敏工具出现异常时的处理
        // 在实际项目中，可以通过Mock DataMaskingUtil来模拟异常

        Long userId = 1L;

        try {
            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            log.info("数据脱敏异常测试结果: {}", JSON.toJSONString(result));

            // 验证结果
            assertThat(result).isNotNull();

            // 如果脱敏工具异常，应该通过全局异常处理返回错误信息
            if (result.getCode() != 200) {
                log.info("数据脱敏异常被正确处理，返回错误码: {}", result.getCode());
            }

        } catch (Exception e) {
            log.info("数据脱敏异常被捕获: {}", e.getMessage());
            assertThat(e).isNotNull();
        }
    }

    /**
     * 测试获取用户个人中心信息 - 并发访问测试
     */
    @Test
    public void testGetUserProfileConcurrentAccess() {
        log.info("=== 测试获取用户个人中心信息 - 并发访问测试 ===");

        Long userId = 1L;
        int threadCount = 5;
        int requestsPerThread = 3;

        // 使用CountDownLatch确保所有线程同时开始
        java.util.concurrent.CountDownLatch startLatch = new java.util.concurrent.CountDownLatch(1);
        java.util.concurrent.CountDownLatch endLatch = new java.util.concurrent.CountDownLatch(threadCount);

        java.util.concurrent.atomic.AtomicInteger successCount = new java.util.concurrent.atomic.AtomicInteger(0);
        java.util.concurrent.atomic.AtomicInteger errorCount = new java.util.concurrent.atomic.AtomicInteger(0);

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                try {
                    startLatch.await(); // 等待开始信号

                    for (int j = 0; j < requestsPerThread; j++) {
                        try {
                            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
                            if (result != null && result.getCode() == 200) {
                                successCount.incrementAndGet();
                            } else {
                                errorCount.incrementAndGet();
                            }
                        } catch (Exception e) {
                            log.warn("线程 {} 请求 {} 出现异常: {}", threadId, j, e.getMessage());
                            errorCount.incrementAndGet();
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    endLatch.countDown();
                }
            }).start();
        }

        try {
            startLatch.countDown(); // 发出开始信号
            endLatch.await(10, java.util.concurrent.TimeUnit.SECONDS); // 等待所有线程完成

            int totalRequests = threadCount * requestsPerThread;
            log.info("并发测试完成 - 总请求数: {}, 成功: {}, 失败: {}",
                    totalRequests, successCount.get(), errorCount.get());

            // 验证并发访问的结果
            assertThat(successCount.get() + errorCount.get()).isEqualTo(totalRequests);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("并发测试被中断", e);
        }
    }

    /**
     * 测试获取用户个人中心信息 - 内存泄漏检测
     */
    @Test
    public void testGetUserProfileMemoryLeak() {
        log.info("=== 测试获取用户个人中心信息 - 内存泄漏检测 ===");

        Long userId = 1L;
        int iterations = 100;

        // 记录初始内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        for (int i = 0; i < iterations; i++) {
            try {
                ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

                // 每10次迭代记录一次内存使用情况
                if (i % 10 == 0) {
                    long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                    log.info("迭代 {}: 内存使用 {} MB", i, currentMemory / 1024 / 1024);
                }

                // 清理引用，帮助GC
                result = null;

            } catch (Exception e) {
                log.warn("迭代 {} 出现异常: {}", i, e.getMessage());
            }

            // 每50次迭代强制GC一次
            if (i % 50 == 0) {
                System.gc();
                try {
                    Thread.sleep(100); // 给GC一些时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 最终内存检查
        System.gc();
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;

        log.info("内存泄漏检测完成 - 初始内存: {} MB, 最终内存: {} MB, 增长: {} MB",
                initialMemory / 1024 / 1024, finalMemory / 1024 / 1024, memoryIncrease / 1024 / 1024);

        // 验证内存增长在合理范围内（比如不超过50MB）
        assertThat(memoryIncrease).isLessThan(50 * 1024 * 1024);
    }
}
