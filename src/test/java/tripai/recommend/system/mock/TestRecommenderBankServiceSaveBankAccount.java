package tripai.recommend.system.mock;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankAccountDto;
import tripai.recommend.system.domain.entity.RecommenderBank;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.mapper.recommender.RecommenderBankMapper;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.RecommenderBankService;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderBankServiceSaveBankAccount
 * @author: lijunqi
 * @description:
 * @date: 2025/7/29 15:31
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderBankServiceSaveBankAccount {

    @Resource
    private RecommenderBankService recommenderBankService;

    @MockBean
//    @Resource
    private RecommenderMapper recommenderInfoMapper;

    @MockBean
//    @Resource
    private RecommenderBankMapper recommenderBankMapper;

    // ==================== 数据完整性验证测试（最优先执行） ====================

    /**
     * 测试数据完整性验证 - 确保测试环境和依赖正常
     * 分支：数据完整性验证（优先级最高）
     */
    @Test
    public void test01_DataIntegrityVerification() {
        log.info("=== 测试数据完整性验证 - 确保测试环境和依赖正常 ===");

        // 验证服务注入
        assertThat(recommenderBankService).isNotNull();
        assertThat(recommenderInfoMapper).isNotNull();
        assertThat(recommenderBankMapper).isNotNull();

        log.info("✅ 服务依赖注入验证通过");

        // 验证基础DTO构建
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        assertThat(dto).isNotNull();
        assertThat(dto.getUserId()).isNotNull();

        log.info("✅ 基础数据构建验证通过");
        log.info("数据完整性验证完成");
    }

    // ==================== 分支B1测试：userId null检查 ====================

    /**
     * 测试saveBankAccount方法 - 分支B1：userId为null的情况
     * 分支：B1 - if (userId == null) 返回失败："用户userId为空"
     */
    @Test
    public void test02_BranchB1_UserIdNull() {
        log.info("=== 测试saveBankAccount方法 - 分支B1：userId为null的情况 ===");

        // 构建userId为null的DTO
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setUserId(null);

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B1的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("用户userId为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B1测试通过：userId为null时正确返回失败结果");
    }

    // ==================== 分支B2测试：推荐方信息不存在 ====================

    /**
     * 测试saveBankAccount方法 - 分支B2：推荐方信息不存在的情况
     * 分支：B2 - if (recommenderInfo == null) 返回失败："推荐方信息不存在"
     */
    @Test
    public void test03_BranchB2_RecommenderInfoNotExists() {
        log.info("=== 测试saveBankAccount方法 - 分支B2：推荐方信息不存在的情况 ===");

        // 构建有效的DTO
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setUserId(999L); // 使用不存在的用户ID

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息查询返回null
        when(recommenderInfoMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("推荐方信息不存在");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：推荐方信息不存在时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderInfoMapper, times(1)).selectOne(any(), eq(false));
    }

    // ==================== 分支B3a + B4测试：更新现有银行账户但账户不存在 ====================

    /**
     * 测试saveBankAccount方法 - 分支B3a + B4：更新现有银行账户但账户不存在的情况
     * 分支：B3a - if (dto.getId() != null) 进入更新分支
     * 分支：B4 - if (bankInfo == null) 返回失败："银行账户信息不存在"
     */
    @Test
    public void test04_BranchB3a_B4_UpdateBankAccountNotExists() {
        log.info("=== 测试saveBankAccount方法 - 分支B3a + B4：更新现有银行账户但账户不存在的情况 ===");

        // 构建更新银行账户的DTO（包含ID）
        RecommenderBankAccountDto dto = new RecommenderBankAccountDto();
        dto.setId(1L); // 设置一个不存在的银行账户ID
        dto.setUserId(1L);
        dto.setBankCardNo("****************");
        dto.setBankName("工商银行");
        dto.setAccountName("张三");
        dto.setAccountCertificateType(1);
        dto.setAccountIdentifier("****************");
        dto.setAccountPhone("***********");
        dto.setProvince("北京市");
        dto.setCity("北京市");
        dto.setBranchName("北京支行");

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 重置Mock状态
        reset(recommenderBankMapper, recommenderInfoMapper);

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderInfoMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock银行账户查询返回null（账户不存在）
        when(recommenderBankMapper.selectById(dto.getId())).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3a + B4的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("银行账户信息不存在");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B3a + B4测试通过：更新不存在的银行账户时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderInfoMapper, times(1)).selectOne(any(LambdaQueryWrapper.class), eq(false));
        verify(recommenderBankMapper, times(1)).selectById(1L);
    }

    // ==================== 分支B3a + B4测试：更新银行账户但不属于当前用户 ====================

    /**
     * 测试saveBankAccount方法 - 分支B3a + B4：更新银行账户但不属于当前用户的情况
     * 分支：B3a - if (dto.getId() != null) 进入更新分支
     * 分支：B4 - if (!bankInfo.getRecommenderId().equals(recommenderInfo.getId())) 返回失败："银行账户信息不存在"
     */
    @Test
    public void test05_BranchB3a_B4_UpdateBankAccountNotBelongToUser() {
        log.info("=== 测试saveBankAccount方法 - 分支B3a + B4：更新银行账户但不属于当前用户的情况 ===");

        long startTime = System.currentTimeMillis();

        // 构建更新银行账户的DTO（包含ID）
        RecommenderBankAccountDto dto = new RecommenderBankAccountDto();
        dto.setId(1L); // 设置一个不存在的银行账户ID
        dto.setUserId(1L);
        dto.setBankCardNo("****************");
        dto.setBankName("工商银行");
        dto.setAccountName("张三");
        dto.setAccountCertificateType(1);
        dto.setAccountIdentifier("****************");
        dto.setAccountPhone("***********");
        dto.setProvince("北京市");
        dto.setCity("北京市");
        dto.setBranchName("北京支行");

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 重置Mock状态
        reset(recommenderBankMapper, recommenderInfoMapper);

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderInfoMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(mockRecommenderInfo);

        // 然后配置银行账户查询：模拟查询到的银行账户属于其他用户
        RecommenderBank existingBank = new RecommenderBank();
        existingBank.setId(1L);
        existingBank.setRecommenderId(999L); // 不同的推荐方ID
        existingBank.setAccountName("李四");
        existingBank.setBankCardNo("****************");
        existingBank.setIsDel(false);
        when(recommenderBankMapper.selectById(1L)).thenReturn(existingBank);

        // 调用被测试方法
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);

        long endTime = System.currentTimeMillis();
        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).contains("银行账户信息不存在");

        // 验证Mock方法被调用
        verify(recommenderBankMapper, times(1)).selectById(1L);
        verify(recommenderInfoMapper, times(1)).selectOne(any(LambdaQueryWrapper.class), eq(false));

        log.info("✅ 分支B3a + B4测试通过：更新不属于当前用户的银行账户时正确返回失败结果");

    }

    // ==================== 分支B3a + B5a + B6测试：更新银行账户成功 ====================

    /**
     * 测试saveBankAccount方法 - 分支B3a + B5a + B6：更新银行账户成功的情况
     * 分支：B3a - if (dto.getId() != null) 进入更新分支
     * 分支：B5a - if (dto.getId() != null) 执行更新操作
     * 分支：B6 - if (saveResult) 返回成功结果
     */
    @Test
    public void test06_BranchB3a_B5a_B6_UpdateBankAccountSuccess() {
        log.info("=== 测试saveBankAccount方法 - 分支B3a + B5a + B6：更新银行账户成功的情况 ===");

        // 构建更新银行账户的DTO
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setId(1L);

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderInfoMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock银行账户存在且属于当前用户
        RecommenderBank mockBankInfo = buildMockRecommenderBank();
        mockBankInfo.setRecommenderId(mockRecommenderInfo.getId());
        when(recommenderBankMapper.selectById(dto.getId())).thenReturn(mockBankInfo);

        // Mock更新操作成功
        when(recommenderBankMapper.updateById(any())).thenReturn(1);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3a + B5a + B6的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isEqualTo(true);

        log.info("✅ 分支B3a + B5a + B6测试通过：更新银行账户成功时正确返回成功结果");

        // 验证Mock调用
        verify(recommenderInfoMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderBankMapper, times(1)).selectById(dto.getId());
        verify(recommenderBankMapper, times(1)).updateById(any());
    }

    // ==================== 分支B3a + B5a + B6测试：更新银行账户失败 ====================

    /**
     * 测试saveBankAccount方法 - 分支B3a + B5a + B6：更新银行账户失败的情况
     * 分支：B3a - if (dto.getId() != null) 进入更新分支
     * 分支：B5a - if (dto.getId() != null) 执行更新操作
     * 分支：B6 - if (!saveResult) 返回失败："保存银行账户信息失败"
     */
    @Test
    public void test07_BranchB3a_B5a_B6_UpdateBankAccountFailed() {
        log.info("=== 测试saveBankAccount方法 - 分支B3a + B5a + B6：更新银行账户失败的情况 ===");

        // 构建更新银行账户的DTO
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setId(1L);

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderInfoMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock银行账户存在且属于当前用户
        RecommenderBank mockBankInfo = buildMockRecommenderBank();
        mockBankInfo.setRecommenderId(mockRecommenderInfo.getId());
        when(recommenderBankMapper.selectById(dto.getId())).thenReturn(mockBankInfo);

        // Mock更新操作失败
        when(recommenderBankMapper.updateById(any())).thenReturn(0);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3a + B5a + B6的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存银行账户信息失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B3a + B5a + B6测试通过：更新银行账户失败时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderInfoMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderBankMapper, times(1)).selectById(dto.getId());
        verify(recommenderBankMapper, times(1)).updateById(any());
    }

    // ==================== 分支B3b + B5b + B6测试：创建新银行账户成功 ====================

    /**
     * 测试saveBankAccount方法 - 分支B3b + B5b + B6：创建新银行账户成功的情况
     * 分支：B3b - else (dto.getId() == null) 进入创建分支
     * 分支：B5b - else (dto.getId() == null) 执行插入操作
     * 分支：B6 - if (saveResult) 返回成功结果
     */
    @Test
    public void test08_BranchB3b_B5b_B6_CreateBankAccountSuccess() {
        log.info("=== 测试saveBankAccount方法 - 分支B3b + B5b + B6：创建新银行账户成功的情况 ===");

        // 构建创建银行账户的DTO（不包含ID）
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setId(null); // 确保ID为null，进入创建分支

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderInfoMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock插入操作成功
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3b + B5b + B6的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isEqualTo(true);

        log.info("✅ 分支B3b + B5b + B6测试通过：创建新银行账户成功时正确返回成功结果");

        // 验证Mock调用
        verify(recommenderInfoMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderBankMapper, times(1)).insert(any());
        verify(recommenderBankMapper, never()).selectById(any()); // 创建分支不应该查询银行账户
        verify(recommenderBankMapper, never()).updateById(any()); // 创建分支不应该执行更新
    }

    // ==================== 分支B3b + B5b + B6测试：创建新银行账户失败 ====================

    /**
     * 测试saveBankAccount方法 - 分支B3b + B5b + B6：创建新银行账户失败的情况
     * 分支：B3b - else (dto.getId() == null) 进入创建分支
     * 分支：B5b - else (dto.getId() == null) 执行插入操作
     * 分支：B6 - if (!saveResult) 返回失败："保存银行账户信息失败"
     */
    @Test
    public void test09_BranchB3b_B5b_B6_CreateBankAccountFailed() {
        log.info("=== 测试saveBankAccount方法 - 分支B3b + B5b + B6：创建新银行账户失败的情况 ===");

        // 构建创建银行账户的DTO（不包含ID）
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setId(null); // 确保ID为null，进入创建分支

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderInfoMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock插入操作失败
        when(recommenderBankMapper.insert(any())).thenReturn(0);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3b + B5b + B6的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存银行账户信息失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B3b + B5b + B6测试通过：创建新银行账户失败时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderInfoMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderBankMapper, times(1)).insert(any());
        verify(recommenderBankMapper, never()).selectById(any()); // 创建分支不应该查询银行账户
        verify(recommenderBankMapper, never()).updateById(any()); // 创建分支不应该执行更新
    }

    // ==================== 分支B7测试：异常处理 ====================

    /**
     * 测试saveBankAccount方法 - 分支B7：异常处理的情况
     * 分支：B7 - catch (Exception e) 返回失败："保存银行账户信息失败"
     */
    @Test
    public void test10_BranchB7_ExceptionHandling() {
        log.info("=== 测试saveBankAccount方法 - 分支B7：异常处理的情况 ===");

        // 构建有效的DTO
        RecommenderBankAccountDto dto = buildValidBankAccountDto();

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息查询抛出异常
        when(recommenderInfoMapper.selectOne(any(), eq(false)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B7的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存银行账户信息失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B7测试通过：异常处理时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderInfoMapper, times(1)).selectOne(any(), eq(false));
    }

    // ==================== 综合场景测试 ====================

    /**
     * 测试saveBankAccount方法 - 综合场景测试：验证所有分支的组合情况
     * 分支：综合验证所有分支的执行路径
     */
    @Test
    public void test11_ComprehensiveScenarioTest() {
        log.info("=== 测试saveBankAccount方法 - 综合场景测试：验证所有分支的组合情况 ===");

        // 场景1：正常创建银行账户
        log.info("--- 场景1：正常创建银行账户 ---");
        testCreateBankAccountScenario();

        // 重置Mock
        reset(recommenderInfoMapper, recommenderBankMapper);

        // 场景2：正常更新银行账户
        log.info("--- 场景2：正常更新银行账户 ---");
        testUpdateBankAccountScenario();

        // 重置Mock
        reset(recommenderInfoMapper, recommenderBankMapper);

        // 场景3：各种失败情况
        log.info("--- 场景3：各种失败情况 ---");
        testFailureScenarios();

        log.info("✅ 综合场景测试完成：所有分支组合验证通过");
    }

    // ==================== 测试辅助方法 ====================

    /**
     * 构建有效的银行账户DTO
     */
    private RecommenderBankAccountDto buildValidBankAccountDto() {
        RecommenderBankAccountDto dto = new RecommenderBankAccountDto();
        dto.setId(1L); // 设置一个不存在的银行账户ID
        dto.setUserId(1L);
        dto.setBankCardNo("****************");
        dto.setBankName("工商银行");
        dto.setAccountName("张三");
        dto.setAccountCertificateType(1);
        dto.setAccountIdentifier("****************");
        dto.setAccountPhone("***********");
        dto.setProvince("北京市");
        dto.setCity("北京市");
        dto.setBranchName("北京支行");
        return dto;
    }

    /**
     * 构建Mock推荐方信息
     */
    private RecommenderInfo buildMockRecommenderInfo() {
        RecommenderInfo recommenderInfo = new RecommenderInfo();
        recommenderInfo.setId(1L);
        recommenderInfo.setUserId(1L);
        recommenderInfo.setName("张三");
        recommenderInfo.setType(1);
        return recommenderInfo;
    }

    /**
     * 构建Mock银行账户信息
     */
    private RecommenderBank buildMockRecommenderBank() {
        RecommenderBank bankInfo = new RecommenderBank();
        bankInfo.setId(1L);
        bankInfo.setRecommenderId(1L);
        bankInfo.setBankCardNo("****************");
        bankInfo.setBankName("工商银行");
        bankInfo.setAccountName("张三");
        bankInfo.setAccountCertificateType(1);
        return bankInfo;
    }

    /**
     * 测试创建银行账户场景
     */
    private void testCreateBankAccountScenario() {
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setId(null);

        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderInfoMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        assertThat(result.getCode()).isEqualTo(200);
        log.info("创建场景测试通过");
    }

    /**
     * 测试更新银行账户场景
     */
    private void testUpdateBankAccountScenario() {
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setId(1L);

        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        RecommenderBank mockBankInfo = buildMockRecommenderBank();
        mockBankInfo.setRecommenderId(mockRecommenderInfo.getId());

        when(recommenderInfoMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);
        when(recommenderBankMapper.selectById(dto.getId())).thenReturn(mockBankInfo);
        when(recommenderBankMapper.updateById(any())).thenReturn(1);

        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        assertThat(result.getCode()).isEqualTo(200);
        log.info("更新场景测试通过");
    }

    /**
     * 测试失败场景
     */
    private void testFailureScenarios() {
        // 测试userId为null的情况
        RecommenderBankAccountDto dto = buildValidBankAccountDto();
        dto.setUserId(null);
        ResponseResult<Long> result = recommenderBankService.saveBankAccount(dto);
        assertThat(result.getCode()).isNotEqualTo(200);
        log.info("userId为null场景测试通过");
    }
}

