package tripai.recommend.system.mock;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankInfoDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderReqDto;
import tripai.recommend.system.domain.entity.FileInfo;
import tripai.recommend.system.domain.entity.RecommenderAuditRecord;
import tripai.recommend.system.domain.entity.RecommenderBank;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.vo.recommender.RecommenderAuditDetailVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderProfileVo;
import tripai.recommend.system.mapper.recommender.RecommenderAuditRecordMapper;
import tripai.recommend.system.mapper.recommender.RecommenderBankMapper;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.RecommenderService;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/29 17:01
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderService {

    @Resource
    private RecommenderService recommenderService;

    @MockBean
    private RecommenderMapper recommenderMapper;

    @MockBean
    private RecommenderBankMapper recommenderBankMapper;

    @MockBean
    private RecommenderAuditRecordMapper recommenderAuditRecordMapper;

    // ==================== 数据完整性验证测试（最优先执行） ====================

    /**
     * 测试数据完整性验证 - 确保测试环境和依赖正常
     * 分支：数据完整性验证（优先级最高）
     */
    @Test
    public void test01_DataIntegrityVerification() {
        log.info("=== 测试数据完整性验证 - 确保测试环境和依赖正常 ===");

        // 验证服务注入
        assertThat(recommenderService).isNotNull();
        assertThat(recommenderMapper).isNotNull();
        assertThat(recommenderBankMapper).isNotNull();
        assertThat(recommenderAuditRecordMapper).isNotNull();

        log.info("✅ 服务依赖注入验证通过");

        // 验证基础DTO构建
        RecommenderReqDto saveDto = buildValidRecommenderReqDto();
        assertThat(saveDto).isNotNull();
        assertThat(saveDto.getUserId()).isNotNull();

        log.info("✅ saveProfile基础数据构建验证通过");

        // 验证getAuditDetail参数
        Long recommenderId = 1L;
        assertThat(recommenderId).isNotNull();

        log.info("✅ getAuditDetail基础数据构建验证通过");
        log.info("数据完整性验证完成");
    }

    // ==================== saveProfile方法分支测试 ====================

    /**
     * 测试saveProfile方法 - 分支B1：dto为null的情况
     * 分支：B1 - if (dto == null) 返回失败："参数为空"
     */
    @Test
    public void test02_SaveProfile_BranchB1_DtoNull() {
        log.info("=== 测试saveProfile方法 - 分支B1：dto为null的情况 ===");

        // 调用服务方法，传入null参数
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B1的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("参数为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B1测试通过：dto为null时正确返回失败结果");
    }

    /**
     * 测试saveProfile方法 - 分支B2：草稿模式跳过验证的情况
     * 分支：B2 - if (!dto.getIsDraft()) 为false，跳过validateRequest
     */
    @Test
    public void test03_SaveProfile_BranchB2_DraftModeSkipValidation() {
        log.info("=== 测试saveProfile方法 - 分支B2：草稿模式跳过验证的情况 ===");

        // 构建草稿模式的DTO（缺少必要字段但isDraft=true）
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 设置为草稿模式
        dto.setName(null); // 故意设置为null，但草稿模式应该跳过验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息不存在，需要创建新的
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(null);
        // Mock保存操作成功
        // Mock推荐方信息保存
        when(recommenderMapper.insert(any(RecommenderInfo.class))).thenAnswer(invocation -> {
            RecommenderInfo info = invocation.getArgument(0);
            info.setId(100L); // 模拟数据库生成的ID
            return 1;
        });
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果
        assertThat(result.getData()).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isNotNull();

        log.info("✅ 分支B2测试通过：草稿模式跳过验证成功");
    }

    /**
     * 测试saveProfile方法 - 分支B2：非草稿模式个人类型验证异常的情况
     * 分支：B2 - if (!dto.getIsDraft()) 为true，执行validateRequest个人类型验证并抛出异常
     */
    @Test
    public void test04_SaveProfile_BranchB2_PersonalValidationException() {
        log.info("=== 测试saveProfile方法 - 分支B2：非草稿模式个人类型验证异常的情况 ===");

        // 构建非草稿模式但缺少必要字段的DTO（个人类型）
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(false); // 非草稿模式
        dto.setType(1); // 个人类型
        dto.setName(null); // 缺少必要字段，会触发验证异常

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果（验证异常被catch块捕获）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：非草稿模式个人类型验证异常被正确处理");
    }

    /**
     * 测试saveProfile方法 - 分支B2：非草稿模式企业类型验证异常的情况
     * 分支：B2 - if (!dto.getIsDraft()) 为true，执行validateRequest企业类型验证（type == 2）并抛出异常
     */
    @Test
    public void test04_1_SaveProfile_BranchB2_EnterpriseValidationException() {
        log.info("=== 测试saveProfile方法 - 分支B2：非草稿模式企业类型验证异常的情况 ===");

        // 构建非草稿模式的企业类型DTO，但缺少营业执照
        RecommenderReqDto dto = buildValidEnterpriseRecommenderReqDto();
        dto.setIsDraft(false); // 非草稿模式
        dto.setType(2); // 企业类型
        dto.setBusinessLicenseUrl(null); // 缺少营业执照，会触发验证异常

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果（企业类型验证异常被catch块捕获）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：非草稿模式企业类型验证异常被正确处理");
    }

    /**
     * 测试saveProfile方法 - 分支B2：企业类型统一社会信用代码格式错误的情况
     * 分支：B2 - 企业类型验证分支，统一社会信用代码格式验证失败
     */
    @Test
    public void test04_2_SaveProfile_BranchB2_EnterpriseCreditCodeValidationException() {
        log.info("=== 测试saveProfile方法 - 分支B2：企业类型统一社会信用代码格式错误的情况 ===");

        // 构建企业类型DTO，但统一社会信用代码格式错误
        RecommenderReqDto dto = buildValidEnterpriseRecommenderReqDto();
        dto.setIsDraft(false); // 非草稿模式
        dto.setType(2); // 企业类型
        dto.setIdentifier("123456789012345678"); // 错误的统一社会信用代码格式

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果（统一社会信用代码格式验证异常被catch块捕获）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：企业类型统一社会信用代码格式验证异常被正确处理");
    }

    /**
     * 测试saveProfile方法 - 分支B2：企业类型银行信息验证异常的情况
     * 分支：B2 - 企业类型验证分支，银行信息验证失败
     */
    @Test
    public void test04_3_SaveProfile_BranchB2_EnterpriseBankInfoValidationException() {
        log.info("=== 测试saveProfile方法 - 分支B2：企业类型银行信息验证异常的情况 ===");

        // 构建企业类型DTO，但银行信息不匹配
        RecommenderReqDto dto = buildValidEnterpriseRecommenderReqDto();
        dto.setIsDraft(false); // 非草稿模式
        dto.setType(2); // 企业类型
        // 设置企业名称和银行开户户名不一致，触发验证异常
        dto.setName("测试企业A");
        dto.getBankInfo().setAccountName("测试企业B"); // 与企业名称不一致

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果（银行信息验证异常被catch块捕获）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：企业类型银行信息验证异常被正确处理");
    }

    /**
     * 测试saveProfile方法 - 分支B3 + B4：推荐方不存在但身份证号重复的情况
     * 分支：B3 - if (recommenderInfo == null) 为true，进入创建分支
     * 分支：B4 - if (existingInfo != null) 为true，返回失败："该身份证号/统一社会信用代码已被使用"
     */
    @Test
    public void test05_SaveProfile_BranchB3_B4_IdentifierDuplicate() {
        log.info("=== 测试saveProfile方法 - 分支B3 + B4：推荐方不存在但身份证号重复的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息不存在（第一次查询）
        when(recommenderMapper.selectOne(any(), eq(false)))
                .thenReturn(null) // 第一次查询：按userId查询，返回null
                .thenReturn(buildMockRecommenderInfo()); // 第二次查询：按身份证号查询，返回存在的记录

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3 + B4的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("该身份证号/统一社会信用代码已被使用");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B3 + B4测试通过：身份证号重复时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(2)).selectOne(any(), eq(false));
    }

    /**
     * 测试saveProfile方法 - 分支B3：推荐方不存在且身份证号不重复，创建新推荐方的情况
     * 分支：B3 - if (recommenderInfo == null) 为true，进入创建分支，身份证号不重复
     */
    @Test
    public void test06_SaveProfile_BranchB3_CreateNewRecommender() {
        log.info("=== 测试saveProfile方法 - 分支B3：推荐方不存在且身份证号不重复，创建新推荐方的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息不存在，身份证号也不重复
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(null);
        // Mock保存操作成功
        // Mock推荐方信息保存
        when(recommenderMapper.insert(any(RecommenderInfo.class))).thenAnswer(invocation -> {
            RecommenderInfo info = invocation.getArgument(0);
            info.setId(100L); // 模拟数据库生成的ID
            return 1;
        });
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3的结果
        assertThat(result.getData()).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isNotNull();

        log.info("✅ 分支B3测试通过：创建新推荐方成功");

        // 验证Mock调用
        verify(recommenderMapper, times(2)).selectOne(any(), eq(false)); // 两次查询
        verify(recommenderMapper, times(1)).insert(any()); // 插入推荐方信息
        verify(recommenderBankMapper, times(1)).insert(any()); // 插入银行信息
    }

    /**
     * 测试saveProfile方法 - 分支B5：保存推荐方基本信息失败的情况
     * 分支：B5 - if (!saveRecommenderInfo) 为true，返回失败："保存推荐方认证资料失败"
     */
    @Test
    public void test07_SaveProfile_BranchB5_SaveRecommenderInfoFailed() {
        log.info("=== 测试saveProfile方法 - 分支B5：保存推荐方基本信息失败的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在（更新场景）
        RecommenderInfo existingInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(existingInfo);
        // Mock更新操作失败
        when(recommenderMapper.updateById(any())).thenReturn(0);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B5的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B5测试通过：保存推荐方基本信息失败时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderMapper, times(1)).updateById(any());
    }

    /**
     * 测试saveProfile方法 - 分支B6：保存银行信息失败的情况
     * 分支：B6 - if (!saveBankInfo) 为true，返回失败："保存推荐方银行信息资料失败"
     */
    @Test
    public void test08_SaveProfile_BranchB6_SaveBankInfoFailed() {
        log.info("=== 测试saveProfile方法 - 分支B6：保存银行信息失败的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在
        RecommenderInfo existingInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(existingInfo);
        // Mock推荐方信息保存成功
        when(recommenderMapper.updateById(any())).thenReturn(1);
        // Mock银行信息保存失败
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(0); // 插入失败

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B6的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方银行信息资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B6测试通过：保存银行信息失败时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).updateById(any());
        verify(recommenderBankMapper, times(1)).insert(any());
    }

    /**
     * 测试saveProfile方法 - 分支B7：异常处理的情况
     * 分支：B7 - catch (Exception e) 返回失败："保存推荐方认证资料失败"
     */
    @Test
    public void test09_SaveProfile_BranchB7_ExceptionHandling() {
        log.info("=== 测试saveProfile方法 - 分支B7：异常处理的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息查询抛出异常
        when(recommenderMapper.selectOne(any(), eq(false)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B7的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("保存推荐方认证资料失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B7测试通过：异常处理时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试saveProfile方法 - 成功场景：完整流程成功的情况
     * 分支：正常流程，所有操作都成功
     */
    @Test
    public void test10_SaveProfile_SuccessScenario() {
        log.info("=== 测试saveProfile方法 - 成功场景：完整流程成功的情况 ===");

        // 构建有效的DTO
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true); // 草稿模式避免验证

        log.info("测试数据: {}", JSON.toJSONString(dto));

        // Mock推荐方信息存在
        RecommenderInfo existingInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(existingInfo);
        // Mock所有保存操作成功
        when(recommenderMapper.updateById(any())).thenReturn(1);
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证成功结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isEqualTo(existingInfo.getId());

        log.info("✅ 成功场景测试通过：完整流程执行成功");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).updateById(any());
        verify(recommenderBankMapper, times(1)).insert(any());
    }

    // ==================== getAuditDetail方法分支测试 ====================

    /**
     * 测试getAuditDetail方法 - 分支B1：recommenderId为null的情况
     * 分支：B1 - if (recommenderId == null) 返回失败："推荐方ID不能为空"
     */
    @Test
    public void test11_GetAuditDetail_BranchB1_RecommenderIdNull() {
        log.info("=== 测试getAuditDetail方法 - 分支B1：recommenderId为null的情况 ===");

        // 调用服务方法，传入null参数
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B1的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("推荐方ID不能为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B1测试通过：recommenderId为null时正确返回失败结果");
    }

    /**
     * 测试getAuditDetail方法 - 分支B2：推荐方不存在的情况
     * 分支：B2 - if (recommenderInfo == null) 返回失败："推荐方不存在"
     */
    @Test
    public void test12_GetAuditDetail_BranchB2_RecommenderNotExists() {
        log.info("=== 测试getAuditDetail方法 - 分支B2：推荐方不存在的情况 ===");

        Long recommenderId = 999L; // 不存在的推荐方ID

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock推荐方信息查询返回null
        when(recommenderMapper.selectById(recommenderId)).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("推荐方不存在");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：推荐方不存在时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectById(recommenderId);
    }

    /**
     * 测试getAuditDetail方法 - 分支B3：审核记录存在的情况
     * 分支：B3 - if (auditRecord != null) 设置审核记录信息
     */
    @Test
    public void test13_GetAuditDetail_BranchB3_AuditRecordExists() {
        log.info("=== 测试getAuditDetail方法 - 分支B3：审核记录存在的情况 ===");

        Long recommenderId = 1L;

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectById(recommenderId)).thenReturn(mockRecommenderInfo);

        // Mock审核记录存在
        RecommenderAuditRecord mockAuditRecord = buildMockAuditRecord();
        when(recommenderAuditRecordMapper.selectOne(any(), eq(false))).thenReturn(mockAuditRecord);

        // Mock银行信息存在
        RecommenderBank mockBankInfo = buildMockRecommenderBank();
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(mockBankInfo);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isEqualTo(recommenderId);
        assertThat(result.getData().getAuditStatus()).isEqualTo(mockAuditRecord.getAuditStatus());
        assertThat(result.getData().getAuditResult()).isEqualTo(mockAuditRecord.getAuditResult());
        assertThat(result.getData().getRejectReason()).isEqualTo(mockAuditRecord.getRejectReason());

        log.info("✅ 分支B3测试通过：审核记录存在时正确设置审核信息");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectById(recommenderId);
        verify(recommenderAuditRecordMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderBankMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getAuditDetail方法 - 分支B3：审核记录不存在的情况
     * 分支：B3 - else (auditRecord == null) 设置默认状态
     */
    @Test
    public void test14_GetAuditDetail_BranchB3_AuditRecordNotExists() {
        log.info("=== 测试getAuditDetail方法 - 分支B3：审核记录不存在的情况 ===");

        Long recommenderId = 1L;

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectById(recommenderId)).thenReturn(mockRecommenderInfo);

        // Mock审核记录不存在
        when(recommenderAuditRecordMapper.selectOne(any(), eq(false))).thenReturn(null);

        // Mock银行信息存在
        RecommenderBank mockBankInfo = buildMockRecommenderBank();
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(mockBankInfo);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3的结果（审核记录不存在时的默认状态）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isEqualTo(recommenderId);
        assertThat(result.getData().getAuditStatus()).isEqualTo(0); // 默认状态
        assertThat(result.getData().getSubmitTime()).isEqualTo(mockRecommenderInfo.getAuditSubmitTime());

        log.info("✅ 分支B3测试通过：审核记录不存在时正确设置默认状态");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectById(recommenderId);
        verify(recommenderAuditRecordMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderBankMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getAuditDetail方法 - 分支B4：个人类型推荐方的情况
     * 分支：B4 - if (recommenderInfo.getType() == 1) 个人类型处理
     */
    @Test
    public void test15_GetAuditDetail_BranchB4_PersonalType() {
        log.info("=== 测试getAuditDetail方法 - 分支B4：个人类型推荐方的情况 ===");

        Long recommenderId = 1L;

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock个人类型推荐方信息
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        mockRecommenderInfo.setType(1); // 个人类型
        mockRecommenderInfo.setCertificateType(1);
        mockRecommenderInfo.setIdCardFrontUrl(buildFileInfoJson("http://example.com/rms/idcard_front.jpg", "idcard_front.jpg"));
        mockRecommenderInfo.setIdCardBackUrl(buildFileInfoJson("http://example.com/rms/idcard_back.jpg", "idcard_back.jpg"));
        when(recommenderMapper.selectById(recommenderId)).thenReturn(mockRecommenderInfo);

        // Mock审核记录不存在
        when(recommenderAuditRecordMapper.selectOne(any(), eq(false))).thenReturn(null);

        // Mock银行信息不存在
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B4的结果（个人类型特有字段）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getType()).isEqualTo(1);
        assertThat(result.getData().getCertificateType()).isEqualTo(1);
        assertThat(result.getData().getBusinessLicenseUrl()).isNull(); // 个人类型不应该有营业执照

        log.info("✅ 分支B4测试通过：个人类型推荐方正确设置证件信息");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectById(recommenderId);
    }

    /**
     * 测试getAuditDetail方法 - 分支B4：企业类型推荐方的情况
     * 分支：B4 - else if (recommenderInfo.getType() == 2) 企业类型处理
     */
    @Test
    public void test16_GetAuditDetail_BranchB4_EnterpriseType() {
        log.info("=== 测试getAuditDetail方法 - 分支B4：企业类型推荐方的情况 ===");

        Long recommenderId = 1L;

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock企业类型推荐方信息
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        mockRecommenderInfo.setType(2); // 企业类型
        mockRecommenderInfo.setBusinessLicenseUrl(buildFileInfoJson("http://example.com/rms/business_license.jpg", "business_license.jpg"));
        when(recommenderMapper.selectById(recommenderId)).thenReturn(mockRecommenderInfo);

        // Mock审核记录不存在
        when(recommenderAuditRecordMapper.selectOne(any(), eq(false))).thenReturn(null);

        // Mock银行信息不存在
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B4的结果（企业类型特有字段）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getType()).isEqualTo(2);
        assertThat(result.getData().getCertificateType()).isNull(); // 企业类型不应该有证件类型
        assertThat(result.getData().getIdCardFrontUrl()).isNull(); // 企业类型不应该有身份证照片
        assertThat(result.getData().getIdCardBackUrl()).isNull();

        log.info("✅ 分支B4测试通过：企业类型推荐方正确设置营业执照信息");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectById(recommenderId);
    }

    /**
     * 测试getAuditDetail方法 - 分支B5：银行信息存在的情况
     * 分支：B5 - if (bankInfo != null) 设置银行信息
     */
    @Test
    public void test17_GetAuditDetail_BranchB5_BankInfoExists() {
        log.info("=== 测试getAuditDetail方法 - 分支B5：银行信息存在的情况 ===");

        Long recommenderId = 1L;

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectById(recommenderId)).thenReturn(mockRecommenderInfo);

        // Mock审核记录不存在
        when(recommenderAuditRecordMapper.selectOne(any(), eq(false))).thenReturn(null);

        // Mock银行信息存在
        RecommenderBank mockBankInfo = buildMockRecommenderBank();
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(mockBankInfo);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B5的结果（银行信息字段）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getAccountName()).isEqualTo(mockBankInfo.getAccountName());
        assertThat(result.getData().getBankCardNo()).isEqualTo(mockBankInfo.getBankCardNo());
        assertThat(result.getData().getBankName()).isEqualTo(mockBankInfo.getBankName());
        assertThat(result.getData().getBranchName()).isEqualTo(mockBankInfo.getBranchName());
        assertThat(result.getData().getProvince()).isEqualTo(mockBankInfo.getProvince());
        assertThat(result.getData().getCity()).isEqualTo(mockBankInfo.getCity());

        log.info("✅ 分支B5测试通过：银行信息存在时正确设置银行字段");

        // 验证Mock调用
        verify(recommenderBankMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getAuditDetail方法 - 分支B5：银行信息不存在的情况
     * 分支：B5 - else (bankInfo == null) 银行信息字段保持null
     */
    @Test
    public void test18_GetAuditDetail_BranchB5_BankInfoNotExists() {
        log.info("=== 测试getAuditDetail方法 - 分支B5：银行信息不存在的情况 ===");

        Long recommenderId = 1L;

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectById(recommenderId)).thenReturn(mockRecommenderInfo);

        // Mock审核记录不存在
        when(recommenderAuditRecordMapper.selectOne(any(), eq(false))).thenReturn(null);

        // Mock银行信息不存在
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B5的结果（银行信息字段为null）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getAccountName()).isNull();
        assertThat(result.getData().getBankCardNo()).isNull();
        assertThat(result.getData().getBankName()).isNull();
        assertThat(result.getData().getBranchName()).isNull();
        assertThat(result.getData().getProvince()).isNull();
        assertThat(result.getData().getCity()).isNull();

        log.info("✅ 分支B5测试通过：银行信息不存在时银行字段正确为null");

        // 验证Mock调用
        verify(recommenderBankMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getAuditDetail方法 - 分支B6：异常处理的情况
     * 分支：B6 - catch (Exception e) 返回失败："查询推荐方审核详情失败"
     */
    @Test
    public void test19_GetAuditDetail_BranchB6_ExceptionHandling() {
        log.info("=== 测试getAuditDetail方法 - 分支B6：异常处理的情况 ===");

        Long recommenderId = 1L;

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock推荐方信息查询抛出异常
        when(recommenderMapper.selectById(recommenderId))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B6的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("查询推荐方审核详情失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B6测试通过：异常处理时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectById(recommenderId);
    }

    /**
     * 测试getAuditDetail方法 - 成功场景：完整流程成功的情况
     * 分支：正常流程，所有数据都存在
     */
    @Test
    public void test20_GetAuditDetail_SuccessScenario() {
        log.info("=== 测试getAuditDetail方法 - 成功场景：完整流程成功的情况 ===");

        Long recommenderId = 1L;

        log.info("测试数据: recommenderId = {}", recommenderId);

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectById(recommenderId)).thenReturn(mockRecommenderInfo);

        // Mock审核记录存在
        RecommenderAuditRecord mockAuditRecord = buildMockAuditRecord();
        when(recommenderAuditRecordMapper.selectOne(any(), eq(false))).thenReturn(mockAuditRecord);

        // Mock银行信息存在
        RecommenderBank mockBankInfo = buildMockRecommenderBank();
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(mockBankInfo);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证成功结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRecommenderId()).isEqualTo(recommenderId);
        assertThat(result.getData().getName()).isEqualTo(mockRecommenderInfo.getName());
        assertThat(result.getData().getAuditStatus()).isEqualTo(mockAuditRecord.getAuditStatus());
        assertThat(result.getData().getBankCardNo()).isEqualTo(mockBankInfo.getBankCardNo());

        log.info("✅ 成功场景测试通过：完整流程执行成功");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectById(recommenderId);
        verify(recommenderAuditRecordMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderBankMapper, times(1)).selectOne(any(), eq(false));
    }

    // ==================== 综合场景测试 ====================

    /**
     * 测试综合场景 - 验证两个方法的组合使用
     * 分支：综合验证saveProfile和getAuditDetail的配合使用
     */
    @Test
    public void test21_ComprehensiveScenarioTest() {
        log.info("=== 测试综合场景 - 验证两个方法的组合使用 ===");

        // 场景1：保存推荐方信息
        log.info("--- 场景1：保存推荐方信息 ---");
        testSaveProfileScenario();

        // 重置Mock
        reset(recommenderMapper, recommenderBankMapper, recommenderAuditRecordMapper);

        // 场景2：查询审核详情
        log.info("--- 场景2：查询审核详情 ---");
        testGetAuditDetailScenario();

        log.info("✅ 综合场景测试完成：两个方法配合使用验证通过");
    }

    // ==================== 测试辅助方法 ====================

    /**
     * 构建有效的推荐方请求DTO
     */
    private RecommenderReqDto buildValidRecommenderReqDto() {
        RecommenderReqDto dto = new RecommenderReqDto();
        dto.setUserId(1L);
        dto.setName("张三");
        dto.setType(1); // 个人类型
        dto.setIdentifier("110101199001011234");
        dto.setCertificateType(1);
        dto.setIdCardFrontUrl(buildFileInfoJson("http://example.com/rms/idcard_front.jpg", "idcard_front.jpg"));
        dto.setIdCardBackUrl(buildFileInfoJson("http://example.com/rms/idcard_back.jpg", "idcard_back.jpg"));
        dto.setIsDraft(false);

        // 银行信息
        RecommenderBankInfoDto bankInfo = new RecommenderBankInfoDto();
        bankInfo.setAccountName("张三");
        bankInfo.setBankCardNo("****************");
        bankInfo.setBankName("工商银行");
        bankInfo.setBranchName("北京分行");
        bankInfo.setProvince("北京市");
        bankInfo.setCity("北京市");
        bankInfo.setAccountCertificateType(1);
        bankInfo.setAccountIdentifier("****************");
        dto.setBankInfo(bankInfo);

        return dto;
    }

    /**
     * 构建有效的企业类型推荐方请求DTO
     */
    private RecommenderReqDto buildValidEnterpriseRecommenderReqDto() {
        RecommenderReqDto dto = new RecommenderReqDto();
        dto.setUserId(2L);
        dto.setName("测试企业有限公司");
        dto.setType(2); // 企业类型
        dto.setIdentifier("91110000123456789X"); // 有效的统一社会信用代码格式
        dto.setBusinessLicenseUrl(buildFileInfoJson("http://example.com/rms/business_license.jpg", "business_license.jpg"));
        dto.setIsDraft(false);

        // 企业银行信息
        RecommenderBankInfoDto bankInfo = new RecommenderBankInfoDto();
        bankInfo.setAccountName("测试企业有限公司"); // 与企业名称一致
        bankInfo.setBankCardNo("1234567890123456789"); // 对公银行卡号
        bankInfo.setBankName("工商银行");
        bankInfo.setBranchName("北京分行");
        bankInfo.setProvince("北京市");
        bankInfo.setCity("北京市");
        bankInfo.setAccountCertificateType(2); // 企业账户
        bankInfo.setIdCardNo("91110000123456789X"); // 纳税人识别号，与统一社会信用代码一致
        bankInfo.setAccountPhone("***********"); // 开户手机号
        dto.setBankInfo(bankInfo);

        return dto;
    }

    /**
     * 构建Mock推荐方信息
     */
    private RecommenderInfo buildMockRecommenderInfo() {
        RecommenderInfo recommenderInfo = new RecommenderInfo();
        recommenderInfo.setId(1L);
        recommenderInfo.setUserId(1L);
        recommenderInfo.setName("张三");
        recommenderInfo.setType(1);
        recommenderInfo.setIdentifier("110101199001011234");
        recommenderInfo.setCertificateType(1);
        recommenderInfo.setIdCardFrontUrl(buildFileInfoJson("http://example.com/rms/idcard_front.jpg", "idcard_front.jpg"));
        recommenderInfo.setIdCardBackUrl(buildFileInfoJson("http://example.com/rms/idcard_back.jpg", "idcard_back.jpg"));
        recommenderInfo.setAvatarUrl(buildFileInfoJson("http://example.com/rms/avatar.jpg", "avatar.jpg"));
        recommenderInfo.setBusinessLicenseUrl(buildFileInfoJson("http://example.com/rms/business_license.jpg", "business_license.jpg"));
        recommenderInfo.setAuditSubmitTime(LocalDateTime.now());
        recommenderInfo.setCreateTime(LocalDateTime.now());
        recommenderInfo.setUpdateTime(LocalDateTime.now());
        return recommenderInfo;
    }

    private RecommenderProfileVo buildMockRecommenderProfileVo() {
        RecommenderProfileVo profileVo = new RecommenderProfileVo();
        profileVo.setRecommenderId(1L);
        return profileVo;
    }

    /**
     * 构建Mock银行信息
     */
    private RecommenderBank buildMockRecommenderBank() {
        RecommenderBank bankInfo = new RecommenderBank();
        bankInfo.setId(1L);
        bankInfo.setRecommenderId(1L);
        bankInfo.setAccountName("张三");
        bankInfo.setBankCardNo("****************");
        bankInfo.setBankName("工商银行");
        bankInfo.setBranchName("北京分行");
        bankInfo.setProvince("北京市");
        bankInfo.setCity("北京市");
        bankInfo.setAccountCertificateType(1);
        bankInfo.setValidateStatus(1);
        bankInfo.setCreateTime(LocalDateTime.now());
        bankInfo.setUpdateTime(LocalDateTime.now());
        return bankInfo;
    }

    /**
     * 构建Mock审核记录
     */
    private RecommenderAuditRecord buildMockAuditRecord() {
        RecommenderAuditRecord auditRecord = new RecommenderAuditRecord();
        auditRecord.setId(1L);
        auditRecord.setRecommenderId(1L);
        auditRecord.setAuditStatus(2); // 审核通过
        auditRecord.setAuditResult(1); // 通过
        auditRecord.setRejectReason(null);
        auditRecord.setAuditTime(LocalDateTime.now());
        auditRecord.setCreateTime(LocalDateTime.now());
        auditRecord.setUpdateTime(LocalDateTime.now());
        return auditRecord;
    }

    /**
     * 测试保存推荐方信息场景
     */
    private void testSaveProfileScenario() {
        RecommenderReqDto dto = buildValidRecommenderReqDto();
        dto.setIsDraft(true);

        RecommenderInfo existingInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(existingInfo);
        when(recommenderMapper.updateById(any())).thenReturn(1);
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(null);
        when(recommenderBankMapper.insert(any())).thenReturn(1);

        ResponseResult<RecommenderProfileVo> result = recommenderService.saveProfile(dto);
        assertThat(result.getCode()).isEqualTo(200);
        log.info("保存推荐方信息场景测试通过");
    }

    /**
     * 测试查询审核详情场景
     */
    private void testGetAuditDetailScenario() {
        Long recommenderId = 1L;

        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        RecommenderAuditRecord mockAuditRecord = buildMockAuditRecord();
        RecommenderBank mockBankInfo = buildMockRecommenderBank();

        when(recommenderMapper.selectById(recommenderId)).thenReturn(mockRecommenderInfo);
        when(recommenderAuditRecordMapper.selectOne(any(), eq(false))).thenReturn(mockAuditRecord);
        when(recommenderBankMapper.selectOne(any(), eq(false))).thenReturn(mockBankInfo);

        ResponseResult<RecommenderAuditDetailVo> result = recommenderService.getAuditDetail(recommenderId);
        assertThat(result.getCode()).isEqualTo(200);
        log.info("查询审核详情场景测试通过");
    }

    /**
     * 构建FileInfo JSON字符串
     * 根据数据库存储格式：{"url": "http://example.com/rms/avatar.jpg", "name": "avatar.jpg", "type": 1, "bucket": "rms"}
     */
    private FileInfo buildFileInfoJson(String url, String fileName) {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setUrl(url);
        fileInfo.setName(fileName);
        fileInfo.setType(1); // 默认类型为1
        fileInfo.setBucket("rms"); // 默认bucket为rms

        // 转换为JSON字符串
        return fileInfo;
    }

    /**
     * 构建FileInfo JSON字符串（带自定义类型）
     */
    private String buildFileInfoJson(String url, String fileName, Integer type) {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setUrl(url);
        fileInfo.setName(fileName);
        fileInfo.setType(type);
        fileInfo.setBucket("rms");

        return JSON.toJSONString(fileInfo);
    }

    /**
     * 构建FileInfo JSON字符串（带自定义类型和bucket）
     */
    private String buildFileInfoJson(String url, String fileName, Integer type, String bucket) {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setUrl(url);
        fileInfo.setName(fileName);
        fileInfo.setType(type);
        fileInfo.setBucket(bucket);

        return JSON.toJSONString(fileInfo);
    }
}
