package tripai.recommend.system.mock;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderGuideRelationQueryDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderHotelRelationQueryDto;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationListVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationListVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationVo;
import tripai.recommend.system.domain.vo.user.MobileInfo;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.mapper.recommender.RecommenderRelationMapper;
import tripai.recommend.system.service.RecommenderRelationService;
import tripai.recommend.system.service.UserInfoService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.mock
 * @className: TestRecommenderRelationService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 10:59
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderRelationService {

    @Resource
    private RecommenderRelationService recommenderRelationService;

    @MockBean
    private RecommenderMapper recommenderMapper;

    @MockBean
    private RecommenderRelationMapper recommenderRelationMapper;

    @MockBean
    private UserInfoService userInfoService;

    // ==================== 数据完整性验证测试（最优先执行） ====================

    /**
     * 测试数据完整性验证 - 确保测试环境和依赖正常
     * 分支：数据完整性验证（优先级最高）
     */
    @Test
    public void test01_DataIntegrityVerification() {
        log.info("=== 测试数据完整性验证 - 确保测试环境和依赖正常 ===");

        // 验证服务注入
        assertThat(recommenderRelationService).isNotNull();
        assertThat(recommenderMapper).isNotNull();
        assertThat(recommenderRelationMapper).isNotNull();
        assertThat(userInfoService).isNotNull();

        log.info("✅ 服务依赖注入验证通过");

        // 验证基础DTO构建
        RecommenderHotelRelationQueryDto hotelQueryDto = buildValidHotelRelationQueryDto();
        assertThat(hotelQueryDto).isNotNull();
        assertThat(hotelQueryDto.getUserId()).isNotNull();

        RecommenderGuideRelationQueryDto guideQueryDto = buildValidGuideRelationQueryDto();
        assertThat(guideQueryDto).isNotNull();
        assertThat(guideQueryDto.getUserId()).isNotNull();

        log.info("✅ 基础数据构建验证通过");
        log.info("数据完整性验证完成");
    }

    // ==================== getRecommenderHotelRelationList方法分支测试 ====================

    /**
     * 测试getRecommenderHotelRelationList方法 - 分支B1：queryDto为null的情况
     * 分支：B1 - if (queryDto == null) 返回失败："查询条件不能为空"
     */
    @Test
    public void test02_GetHotelRelationList_BranchB1_QueryDtoNull() {
        log.info("=== 测试getRecommenderHotelRelationList方法 - 分支B1：queryDto为null的情况 ===");

        log.info("测试数据: queryDto = null");

        // 调用服务方法，传入null参数
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B1的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("查询条件不能为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B1测试通过：queryDto为null时正确返回失败结果");
    }

    /**
     * 测试getRecommenderHotelRelationList方法 - 分支B2：userId为null的情况
     * 分支：B2 - if (queryDto.getUserId() == null) 返回失败："用户ID不能为空"
     */
    @Test
    public void test03_GetHotelRelationList_BranchB2_UserIdNull() {
        log.info("=== 测试getRecommenderHotelRelationList方法 - 分支B2：userId为null的情况 ===");

        // 构建userId为null的DTO
        RecommenderHotelRelationQueryDto queryDto = buildValidHotelRelationQueryDto();
        queryDto.setUserId(null);

        log.info("测试数据: {}", JSON.toJSONString(queryDto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("用户ID不能为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：userId为null时正确返回失败结果");
    }

    /**
     * 测试getRecommenderHotelRelationList方法 - 分支B3：推荐方不存在的情况
     * 分支：B3 - if (recommenderInfo == null) 返回失败："推荐方不存在"
     */
    @Test
    public void test04_GetHotelRelationList_BranchB3_RecommenderNotExists() {
        log.info("=== 测试getRecommenderHotelRelationList方法 - 分支B3：推荐方不存在的情况 ===");

        // 构建有效的DTO
        RecommenderHotelRelationQueryDto queryDto = buildValidHotelRelationQueryDto();
        queryDto.setUserId(999L); // 使用不存在的用户ID

        log.info("测试数据: {}", JSON.toJSONString(queryDto));

        // Mock推荐方信息查询返回null
        when(recommenderMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("推荐方不存在");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B3测试通过：推荐方不存在时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getRecommenderHotelRelationList方法 - 分支B4：正常流程成功的情况
     * 分支：B4 - 正常流程，所有操作都成功
     */
    @Test
    public void test05_GetHotelRelationList_BranchB4_SuccessScenario() {
        log.info("=== 测试getRecommenderHotelRelationList方法 - 分支B4：正常流程成功的情况 ===");

        // 构建有效的DTO
        RecommenderHotelRelationQueryDto queryDto = buildValidHotelRelationQueryDto();

        log.info("测试数据: {}", JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock酒店关系查询结果
        mockHotelRelationQueryResults();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B4的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getRelations()).isNotNull();
        assertThat(result.getData().getTotal()).isNotNull();

        log.info("✅ 分支B4测试通过：正常流程执行成功");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderRelationMapper, times(1)).selectRecommenderHotelRelationList(any());
        verify(recommenderRelationMapper, times(1)).selectRecommenderHotelRelationCount(any());
        verify(recommenderRelationMapper, times(1)).selectRecommenderHotelRelationStatistics(any());
    }

    /**
     * 测试getRecommenderHotelRelationList方法 - 分支B5：异常处理的情况
     * 分支：B5 - catch (Exception e) 返回失败："查询推荐方酒店关系列表失败"
     */
    @Test
    public void test06_GetHotelRelationList_BranchB5_ExceptionHandling() {
        log.info("=== 测试getRecommenderHotelRelationList方法 - 分支B5：异常处理的情况 ===");

        // 构建有效的DTO
        RecommenderHotelRelationQueryDto queryDto = buildValidHotelRelationQueryDto();

        log.info("测试数据: {}", JSON.toJSONString(queryDto));

        // Mock推荐方信息查询抛出异常
        when(recommenderMapper.selectOne(any(LambdaQueryWrapper.class), eq(false)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B5的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("查询推荐方酒店关系列表失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B5测试通过：异常处理时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    // ==================== getRecommenderGuideRelationList方法分支测试 ====================

    /**
     * 测试getRecommenderGuideRelationList方法 - 分支B1：queryDto为null的情况
     * 分支：B1 - if (queryDto == null) 返回失败："查询条件不能为空"
     */
    @Test
    public void test07_GetGuideRelationList_BranchB1_QueryDtoNull() {
        log.info("=== 测试getRecommenderGuideRelationList方法 - 分支B1：queryDto为null的情况 ===");

        log.info("测试数据: queryDto = null");

        // 调用服务方法，传入null参数
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getRecommenderGuideRelationList(null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B1的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("查询条件不能为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B1测试通过：queryDto为null时正确返回失败结果");
    }

    /**
     * 测试getRecommenderGuideRelationList方法 - 分支B2：userId为null的情况
     * 分支：B2 - if (queryDto.getUserId() == null) 返回失败："用户ID不能为空"
     */
    @Test
    public void test08_GetGuideRelationList_BranchB2_UserIdNull() {
        log.info("=== 测试getRecommenderGuideRelationList方法 - 分支B2：userId为null的情况 ===");

        // 构建userId为null的DTO
        RecommenderGuideRelationQueryDto queryDto = buildValidGuideRelationQueryDto();
        queryDto.setUserId(null);

        log.info("测试数据: {}", JSON.toJSONString(queryDto));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getRecommenderGuideRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("用户ID不能为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B2测试通过：userId为null时正确返回失败结果");
    }

    /**
     * 测试getRecommenderGuideRelationList方法 - 分支B3：推荐方不存在的情况
     * 分支：B3 - if (recommenderInfo == null) 返回失败："推荐方不存在"
     */
    @Test
    public void test09_GetGuideRelationList_BranchB3_RecommenderNotExists() {
        log.info("=== 测试getRecommenderGuideRelationList方法 - 分支B3：推荐方不存在的情况 ===");

        // 构建有效的DTO
        RecommenderGuideRelationQueryDto queryDto = buildValidGuideRelationQueryDto();
        queryDto.setUserId(999L); // 使用不存在的用户ID

        log.info("测试数据: {}", JSON.toJSONString(queryDto));

        // Mock推荐方信息查询返回null
        when(recommenderMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getRecommenderGuideRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("推荐方不存在");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B3测试通过：推荐方不存在时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getRecommenderGuideRelationList方法 - 分支B4：正常流程成功的情况
     * 分支：B4 - 正常流程，所有操作都成功
     */
    @Test
    public void test10_GetGuideRelationList_BranchB4_SuccessScenario() {
        log.info("=== 测试getRecommenderGuideRelationList方法 - 分支B4：正常流程成功的情况 ===");

        // 构建有效的DTO
        RecommenderGuideRelationQueryDto queryDto = buildValidGuideRelationQueryDto();

        log.info("测试数据: {}", JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock导游关系查询结果
        mockGuideRelationQueryResults();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getRecommenderGuideRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B4的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getSuppliers()).isNotNull();
        assertThat(result.getData().getTotal()).isNotNull();

        log.info("✅ 分支B4测试通过：正常流程执行成功");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
        verify(recommenderRelationMapper, times(1)).selectRecommenderGuideRelationList(any());
        verify(recommenderRelationMapper, times(1)).selectRecommenderGuideRelationCount(any());
    }

    /**
     * 测试getRecommenderGuideRelationList方法 - 分支B5：异常处理的情况
     * 分支：B5 - catch (Exception e) 返回失败："查询推荐方导游关系列表失败"
     */
    @Test
    public void test11_GetGuideRelationList_BranchB5_ExceptionHandling() {
        log.info("=== 测试getRecommenderGuideRelationList方法 - 分支B5：异常处理的情况 ===");

        // 构建有效的DTO
        RecommenderGuideRelationQueryDto queryDto = buildValidGuideRelationQueryDto();

        log.info("测试数据: {}", JSON.toJSONString(queryDto));

        // Mock推荐方信息查询抛出异常
        when(recommenderMapper.selectOne(any(LambdaQueryWrapper.class), eq(false)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getRecommenderGuideRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B5的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("查询推荐方导游关系列表失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B5测试通过：异常处理时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试综合场景 - 验证两个方法的完整功能
     * 分支：综合验证getRecommenderHotelRelationList和getRecommenderGuideRelationList方法的各种场景
     */
    @Test
    public void test12_ComprehensiveScenarioTest() {
        log.info("=== 测试综合场景 - 验证两个方法的完整功能 ===");

        // 场景1：酒店关系列表查询成功场景
        log.info("--- 场景1：酒店关系列表查询成功场景 ---");
        testHotelRelationListSuccessScenario();

        // 重置Mock
        reset(recommenderMapper, recommenderRelationMapper, userInfoService);

        // 场景2：导游关系列表查询成功场景
        log.info("--- 场景2：导游关系列表查询成功场景 ---");
        testGuideRelationListSuccessScenario();

        // 重置Mock
        reset(recommenderMapper, recommenderRelationMapper, userInfoService);

        // 场景3：异常处理场景
        log.info("--- 场景3：异常处理场景 ---");
        testExceptionScenarios();

        log.info("✅ 综合场景测试完成：两个方法功能验证通过");
    }

    // ==================== 测试辅助方法 ====================

    /**
     * 构建有效的酒店关系查询DTO
     */
    private RecommenderHotelRelationQueryDto buildValidHotelRelationQueryDto() {
        RecommenderHotelRelationQueryDto queryDto = new RecommenderHotelRelationQueryDto();
        queryDto.setUserId(1L);
        queryDto.setKeyword("测试酒店");
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        queryDto.setSortType(1);
        return queryDto;
    }

    /**
     * 构建有效的导游关系查询DTO
     */
    private RecommenderGuideRelationQueryDto buildValidGuideRelationQueryDto() {
        RecommenderGuideRelationQueryDto queryDto = new RecommenderGuideRelationQueryDto();
        queryDto.setUserId(1L);
        queryDto.setKeyword("测试导游");
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        return queryDto;
    }

    /**
     * 构建Mock推荐方信息
     */
    private RecommenderInfo buildMockRecommenderInfo() {
        RecommenderInfo recommenderInfo = new RecommenderInfo();
        recommenderInfo.setId(1L);
        recommenderInfo.setUserId(1L);
        recommenderInfo.setName("测试推荐方");
        recommenderInfo.setType(1);
        recommenderInfo.setCreateTime(LocalDateTime.now());
        recommenderInfo.setUpdateTime(LocalDateTime.now());
        return recommenderInfo;
    }

    /**
     * Mock酒店关系查询结果
     */
    private void mockHotelRelationQueryResults() {
        List<RecommenderHotelRelationVo> mockRelationList = buildMockHotelRelationList();
        when(recommenderRelationMapper.selectRecommenderHotelRelationList(any())).thenReturn(mockRelationList);
        when(recommenderRelationMapper.selectRecommenderHotelRelationCount(any())).thenReturn(2L);

        RecommenderHotelRelationListVo.RecommenderHotelRelationStatisticsVo mockStatistics =
                new RecommenderHotelRelationListVo.RecommenderHotelRelationStatisticsVo();
        when(recommenderRelationMapper.selectRecommenderHotelRelationStatistics(any())).thenReturn(mockStatistics);

        // Mock手机号解密
        MobileInfo mockMobileInfo = new MobileInfo();
        mockMobileInfo.setMobile("13800138000");
        when(userInfoService.mobileAesDecryptPublic(any())).thenReturn(mockMobileInfo);
    }

    /**
     * 构建Mock酒店关系列表
     */
    private List<RecommenderHotelRelationVo> buildMockHotelRelationList() {
        List<RecommenderHotelRelationVo> relationList = new ArrayList<>();

        RecommenderHotelRelationVo relation1 = new RecommenderHotelRelationVo();
        relation1.setRelationId(1L);
        relation1.setRecommenderId(1L);
        relation1.setHotelId(1L);
        relation1.setHotelName("测试酒店1");
        relation1.setSupplierMobile("encrypted_mobile_1");
        relationList.add(relation1);

        RecommenderHotelRelationVo relation2 = new RecommenderHotelRelationVo();
        relation2.setRelationId(2L);
        relation2.setRecommenderId(1L);
        relation2.setHotelId(2L);
        relation2.setHotelName("测试酒店2");
        relation2.setSupplierMobile("encrypted_mobile_2");
        relationList.add(relation2);

        return relationList;
    }

    /**
     * Mock导游关系查询结果
     */
    private void mockGuideRelationQueryResults() {
        List<RecommenderGuideRelationVo> mockRelationList = buildMockGuideRelationList();
        when(recommenderRelationMapper.selectRecommenderGuideRelationList(any())).thenReturn(mockRelationList);
        when(recommenderRelationMapper.selectRecommenderGuideRelationCount(any())).thenReturn(2L);

        // Mock服务数和订单数查询
        when(recommenderRelationMapper.selectServiceCountByGuideId(any())).thenReturn(5);
        when(recommenderRelationMapper.selectOrderCountByGuideId(any(), any(), any())).thenReturn(10);

        // Mock手机号解密
        MobileInfo mockMobileInfo = new MobileInfo();
        mockMobileInfo.setMobile("13800138000");
        when(userInfoService.mobileAesDecryptPublic(any())).thenReturn(mockMobileInfo);
    }

    /**
     * 构建Mock导游关系列表
     */
    private List<RecommenderGuideRelationVo> buildMockGuideRelationList() {
        List<RecommenderGuideRelationVo> relationList = new ArrayList<>();

        RecommenderGuideRelationVo relation1 = new RecommenderGuideRelationVo();
        relation1.setRelationId(1L);
        relation1.setRecommenderId(1L);
        relation1.setGuideId(1L);
        relation1.setGuideName("测试导游1");
        relation1.setPhone("encrypted_phone_1");
        relation1.setLanguage("[\"中文\",\"英文\"]");
        relation1.setServiceCities("[\"北京\",\"上海\"]");
        relation1.setRelationCreateTime(LocalDateTime.now());
        relationList.add(relation1);

        RecommenderGuideRelationVo relation2 = new RecommenderGuideRelationVo();
        relation2.setRelationId(2L);
        relation2.setRecommenderId(1L);
        relation2.setGuideId(2L);
        relation2.setGuideName("测试导游2");
        relation2.setPhone("encrypted_phone_2");
        relation2.setLanguage("[\"中文\",\"日文\"]");
        relation2.setServiceCities("[\"广州\",\"深圳\"]");
        relation2.setRelationCreateTime(LocalDateTime.now());
        relationList.add(relation2);

        return relationList;
    }

    /**
     * 测试酒店关系列表查询成功场景
     */
    private void testHotelRelationListSuccessScenario() {
        RecommenderHotelRelationQueryDto queryDto = buildValidHotelRelationQueryDto();

        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);
        mockHotelRelationQueryResults();

        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        assertThat(result.getCode()).isEqualTo(200);
        log.info("酒店关系列表查询成功场景测试通过");
    }

    /**
     * 测试导游关系列表查询成功场景
     */
    private void testGuideRelationListSuccessScenario() {
        RecommenderGuideRelationQueryDto queryDto = buildValidGuideRelationQueryDto();

        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);
        mockGuideRelationQueryResults();

        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getRecommenderGuideRelationList(queryDto);
        assertThat(result.getCode()).isEqualTo(200);
        log.info("导游关系列表查询成功场景测试通过");
    }

    /**
     * 测试异常处理场景
     */
    private void testExceptionScenarios() {
        // 测试酒店关系查询异常
        RecommenderHotelRelationQueryDto hotelQueryDto = buildValidHotelRelationQueryDto();
        when(recommenderMapper.selectOne(any(), eq(false)))
                .thenThrow(new RuntimeException("数据库异常"));

        ResponseResult<RecommenderHotelRelationListVo> hotelResult = recommenderRelationService.getRecommenderHotelRelationList(hotelQueryDto);
        assertThat(hotelResult.getCode()).isNotEqualTo(200);
        log.info("酒店关系查询异常场景测试通过");

        // 重置Mock
        reset(recommenderMapper);

        // 测试导游关系查询异常
        RecommenderGuideRelationQueryDto guideQueryDto = buildValidGuideRelationQueryDto();
        when(recommenderMapper.selectOne(any(), eq(false)))
                .thenThrow(new RuntimeException("数据库异常"));

        ResponseResult<RecommenderGuideRelationListVo> guideResult = recommenderRelationService.getRecommenderGuideRelationList(guideQueryDto);
        assertThat(guideResult.getCode()).isNotEqualTo(200);
        log.info("导游关系查询异常场景测试通过");
    }
}
