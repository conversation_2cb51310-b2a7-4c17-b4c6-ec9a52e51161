package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderOrderQueryDto;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderListVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderVo;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.mapper.recommender.RecommenderOrderMapper;
import tripai.recommend.system.service.RecommenderOrderService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderOrderService
 * @author: lijunqi
 * @description: RecommenderOrderService分支覆盖测试
 * @date: 2025/7/29 18:30
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderOrderService {

    @Resource
    private RecommenderOrderService recommenderOrderService;

    @MockBean
    private RecommenderMapper recommenderMapper;

    @MockBean
    private RecommenderOrderMapper recommenderOrderMapper;

    // ==================== 数据完整性验证测试（最优先执行） ====================

    /**
     * 测试数据完整性验证 - 确保测试环境和依赖正常
     * 分支：数据完整性验证（优先级最高）
     */
    @Test
    public void test01_DataIntegrityVerification() {
        log.info("=== 测试数据完整性验证 - 确保测试环境和依赖正常 ===");

        // 验证服务注入
        assertThat(recommenderOrderService).isNotNull();
        assertThat(recommenderMapper).isNotNull();
        assertThat(recommenderOrderMapper).isNotNull();

        log.info("✅ 服务依赖注入验证通过");

        // 验证基础DTO构建
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();
        assertThat(queryDto).isNotNull();
        assertThat(queryDto.getRecommenderId()).isNotNull();

        log.info("✅ 基础数据构建验证通过");

        // 验证用户ID
        Long userId = 1L;
        assertThat(userId).isNotNull();

        log.info("✅ 用户ID验证通过");
        log.info("数据完整性验证完成");
    }

    // ==================== getRecommenderOrderListByUserId方法分支测试 ====================

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支B1：userId为null的情况
     * 分支：B1 - if (userId == null) 返回失败："用户ID不能为空"
     */
    @Test
    public void test02_GetOrderListByUserId_BranchB1_UserIdNull() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 分支B1：userId为null的情况 ===");

        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        log.info("测试数据: userId = null, queryDto = {}", JSON.toJSONString(queryDto));

        // 调用服务方法，传入null的userId
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(null, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B1的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("用户ID不能为空");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B1测试通过：userId为null时正确返回失败结果");
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支B2：queryDto为null的情况
     * 分支：B2 - if (queryDto == null) 创建默认queryDto并继续执行
     */
    @Test
    public void test03_GetOrderListByUserId_BranchB2_QueryDtoNull() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 分支B2：queryDto为null的情况 ===");

        Long userId = 1L;

        log.info("测试数据: userId = {}, queryDto = null", userId);

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock订单查询结果
        mockOrderQueryResults();

        // 调用服务方法，传入null的queryDto
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, null);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B2的结果（queryDto为null时创建默认对象并继续执行）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();

        log.info("✅ 分支B2测试通过：queryDto为null时创建默认对象并正确执行");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支B3：推荐方不存在的情况
     * 分支：B3 - if (recommenderInfo == null) 返回失败："推荐方不存在"
     */
    @Test
    public void test04_GetOrderListByUserId_BranchB3_RecommenderNotExists() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 分支B3：推荐方不存在的情况 ===");

        Long userId = 999L; // 不存在的用户ID
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息不存在
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(null);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B3的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("推荐方不存在");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B3测试通过：推荐方不存在时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支B4：异常处理的情况
     * 分支：B4 - catch (Exception e) 返回失败："查询订单列表失败"
     */
    @Test
    public void test05_GetOrderListByUserId_BranchB4_ExceptionHandling() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 分支B4：异常处理的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息查询抛出异常
        when(recommenderMapper.selectOne(any(), eq(false)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B4的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("查询订单列表失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B4测试通过：异常处理时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 成功场景：完整流程成功的情况
     * 分支：正常流程，所有操作都成功
     */
    @Test
    public void test06_GetOrderListByUserId_SuccessScenario() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 成功场景：完整流程成功的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock订单查询结果
        mockOrderQueryResults();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证成功结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).isNotNull();
        assertThat(result.getData().getTotal()).isNotNull();

        log.info("✅ 成功场景测试通过：完整流程执行成功");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectOne(any(), eq(false));
    }

    // ==================== getRecommenderOrderListByUserId方法内部逻辑测试 ====================

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支：不同订单类型查询（全部订单）
     * 分支：内部调用getRecommenderOrderList时orderType为null，查询全部订单
     */
    @Test
    public void test07_GetOrderListByUserId_QueryAllOrders() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 查询全部订单的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();
        queryDto.setOrderType(null); // 查询全部订单

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock订单查询结果
        List<RecommenderOrderVo> mockOrderList = buildMockOrderList();
        when(recommenderOrderMapper.selectRecommenderOrderList(any())).thenReturn(mockOrderList);
        when(recommenderOrderMapper.selectRecommenderOrderCount(any())).thenReturn(2L);
        when(recommenderOrderMapper.selectRecommenderOrderCommissionSum(any())).thenReturn(new BigDecimal("150.00"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).hasSize(2);
        assertThat(result.getData().getTotal()).isEqualTo(2L);
        assertThat(result.getData().getTotalCommissionAmount()).isEqualTo(new BigDecimal("150.00"));

        log.info("✅ 查询全部订单测试通过");

        // 验证Mock调用
        verify(recommenderOrderMapper, times(1)).selectRecommenderOrderList(any());
        verify(recommenderOrderMapper, times(1)).selectRecommenderOrderCount(any());
        verify(recommenderOrderMapper, times(1)).selectRecommenderOrderCommissionSum(any());
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支：查询酒店订单
     * 分支：内部调用getRecommenderOrderList时orderType为1，查询酒店订单
     */
    @Test
    public void test08_GetOrderListByUserId_QueryHotelOrders() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 查询酒店订单的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();
        queryDto.setOrderType(1); // 酒店订单类型

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock查询酒店订单的结果
        List<RecommenderOrderVo> mockHotelOrderList = buildMockHotelOrderList();
        when(recommenderOrderMapper.selectHotelOrderList(any())).thenReturn(mockHotelOrderList);
        when(recommenderOrderMapper.selectHotelOrderCount(any())).thenReturn(1L);
        when(recommenderOrderMapper.selectHotelOrderCommissionSum(any())).thenReturn(new BigDecimal("80.00"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).hasSize(1);
        assertThat(result.getData().getTotal()).isEqualTo(1L);
        assertThat(result.getData().getTotalCommissionAmount()).isEqualTo(new BigDecimal("80.00"));

        log.info("✅ 查询酒店订单测试通过");

        // 验证Mock调用
        verify(recommenderOrderMapper, times(1)).selectHotelOrderList(any());
        verify(recommenderOrderMapper, times(1)).selectHotelOrderCount(any());
        verify(recommenderOrderMapper, times(1)).selectHotelOrderCommissionSum(any());
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支：查询导游订单
     * 分支：内部调用getRecommenderOrderList时orderType为2，查询导游订单
     */
    @Test
    public void test09_GetOrderListByUserId_QueryTourGuideOrders() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 查询导游订单的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();
        queryDto.setOrderType(2); // 导游订单类型

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock查询导游订单的结果
        List<RecommenderOrderVo> mockTourGuideOrderList = buildMockTourGuideOrderList();
        when(recommenderOrderMapper.selectTourGuideOrderList(any())).thenReturn(mockTourGuideOrderList);
        when(recommenderOrderMapper.selectTourGuideOrderCount(any())).thenReturn(1L);
        when(recommenderOrderMapper.selectTourGuideOrderCommissionSum(any())).thenReturn(new BigDecimal("70.00"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).hasSize(1);
        assertThat(result.getData().getTotal()).isEqualTo(1L);
        assertThat(result.getData().getTotalCommissionAmount()).isEqualTo(new BigDecimal("70.00"));

        log.info("✅ 查询导游订单测试通过");

        // 验证Mock调用
        verify(recommenderOrderMapper, times(1)).selectTourGuideOrderList(any());
        verify(recommenderOrderMapper, times(1)).selectTourGuideOrderCount(any());
        verify(recommenderOrderMapper, times(1)).selectTourGuideOrderCommissionSum(any());
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支：关键词查询
     * 分支：使用关键词进行订单查询
     */
    @Test
    public void test10_GetOrderListByUserId_KeywordQuery() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 关键词查询的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();
        queryDto.setKeyword("测试关键词");

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock订单查询结果
        List<RecommenderOrderVo> mockOrderList = buildMockOrderList();
        when(recommenderOrderMapper.selectRecommenderOrderList(any())).thenReturn(mockOrderList);
        when(recommenderOrderMapper.selectRecommenderOrderCount(any())).thenReturn(2L);
        when(recommenderOrderMapper.selectRecommenderOrderCommissionSum(any())).thenReturn(new BigDecimal("150.00"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).hasSize(2);

        log.info("✅ 关键词查询测试通过");
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支：无效订单类型处理
     * 分支：内部调用getRecommenderOrderList时orderType无效，被设置为null查询全部订单
     */
    @Test
    public void test12_GetOrderListByUserId_InvalidOrderType() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 无效订单类型处理的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();
        queryDto.setOrderType(999); // 无效的订单类型

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock订单查询结果（无效订单类型会被设置为null，查询全部订单）
        List<RecommenderOrderVo> mockOrderList = buildMockOrderList();
        when(recommenderOrderMapper.selectRecommenderOrderList(any())).thenReturn(mockOrderList);
        when(recommenderOrderMapper.selectRecommenderOrderCount(any())).thenReturn(2L);
        when(recommenderOrderMapper.selectRecommenderOrderCommissionSum(any())).thenReturn(new BigDecimal("150.00"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果（无效订单类型被处理为查询全部）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).hasSize(2);
        assertThat(result.getData().getTotal()).isEqualTo(2L);

        log.info("✅ 无效订单类型处理测试通过：无效订单类型被正确处理为查询全部订单");

        // 验证Mock调用（调用了查询全部订单的方法）
        verify(recommenderOrderMapper, times(1)).selectRecommenderOrderList(any());
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支：关键词trim处理
     * 分支：内部调用getRecommenderOrderList时关键词包含前后空格，被trim处理
     */
    @Test
    public void test13_GetOrderListByUserId_KeywordTrimProcessing() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 关键词trim处理的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();
        queryDto.setKeyword("  测试关键词  "); // 包含前后空格的关键词

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock订单查询结果
        List<RecommenderOrderVo> mockOrderList = buildMockOrderList();
        when(recommenderOrderMapper.selectRecommenderOrderList(any())).thenReturn(mockOrderList);
        when(recommenderOrderMapper.selectRecommenderOrderCount(any())).thenReturn(2L);
        when(recommenderOrderMapper.selectRecommenderOrderCommissionSum(any())).thenReturn(new BigDecimal("150.00"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();

        log.info("✅ 关键词trim处理测试通过：关键词前后空格被正确去除");

        // 验证关键词已被trim处理
        assertThat(queryDto.getKeyword()).isEqualTo("测试关键词");
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支：空订单列表场景
     * 分支：查询结果为空列表的情况
     */
    @Test
    public void test14_GetOrderListByUserId_EmptyOrderList() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 空订单列表场景 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock空订单查询结果
        List<RecommenderOrderVo> emptyOrderList = new ArrayList<>();
        when(recommenderOrderMapper.selectRecommenderOrderList(any())).thenReturn(emptyOrderList);
        when(recommenderOrderMapper.selectRecommenderOrderCount(any())).thenReturn(0L);
        when(recommenderOrderMapper.selectRecommenderOrderCommissionSum(any())).thenReturn(BigDecimal.ZERO);

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).isEmpty();
        assertThat(result.getData().getTotal()).isEqualTo(0L);
        assertThat(result.getData().getTotalCommissionAmount()).isEqualTo(BigDecimal.ZERO);

        log.info("✅ 空订单列表场景测试通过");
    }

    /**
     * 测试getRecommenderOrderListByUserId方法 - 分支：分佣金额为null的处理
     * 分支：查询分佣金额返回null时的处理
     */
    @Test
    public void test15_GetOrderListByUserId_NullCommissionAmount() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 - 分佣金额为null的处理 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock订单查询结果，分佣金额为null
        List<RecommenderOrderVo> mockOrderList = buildMockOrderList();
        when(recommenderOrderMapper.selectRecommenderOrderList(any())).thenReturn(mockOrderList);
        when(recommenderOrderMapper.selectRecommenderOrderCount(any())).thenReturn(2L);
        when(recommenderOrderMapper.selectRecommenderOrderCommissionSum(any())).thenReturn(null); // 返回null

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果（null被处理为BigDecimal.ZERO）
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).hasSize(2);
        assertThat(result.getData().getTotalCommissionAmount()).isEqualTo(BigDecimal.ZERO);

        log.info("✅ 分佣金额为null的处理测试通过：null被正确处理为BigDecimal.ZERO");
    }

    // ==================== 综合场景测试 ====================

    /**
     * 测试综合场景 - 验证getRecommenderOrderListByUserId方法的完整功能
     * 分支：综合验证getRecommenderOrderListByUserId方法的各种场景
     */
    @Test
    public void test16_ComprehensiveScenarioTest() {
        log.info("=== 测试综合场景 - 验证getRecommenderOrderListByUserId方法的完整功能 ===");

        // 场景1：成功查询订单列表
        log.info("--- 场景1：成功查询订单列表 ---");
        testSuccessScenario();

        // 重置Mock
        reset(recommenderMapper, recommenderOrderMapper);

        // 场景2：异常处理场景
        log.info("--- 场景2：异常处理场景 ---");
        testExceptionScenario();

        log.info("✅ 综合场景测试完成：getRecommenderOrderListByUserId方法功能验证通过");
    }

    // ==================== 测试辅助方法 ====================

    /**
     * 测试成功场景
     */
    private void testSuccessScenario() {
        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        List<RecommenderOrderVo> mockOrderList = buildMockOrderList();
        when(recommenderOrderMapper.selectRecommenderOrderList(any())).thenReturn(mockOrderList);
        when(recommenderOrderMapper.selectRecommenderOrderCount(any())).thenReturn(2L);
        when(recommenderOrderMapper.selectRecommenderOrderCommissionSum(any())).thenReturn(new BigDecimal("150.00"));

        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData().getOrders()).hasSize(2);
        log.info("成功场景测试通过");
    }

    /**
     * 测试异常场景
     */
    private void testExceptionScenario() {
        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        when(recommenderMapper.selectOne(any(), eq(false)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        assertThat(result.getCode()).isNotEqualTo(200);
        log.info("异常场景测试通过");
    }

    /**
     * 构建有效的查询DTO
     */
    private RecommenderOrderQueryDto buildValidQueryDto() {
        RecommenderOrderQueryDto queryDto = new RecommenderOrderQueryDto();
        queryDto.setRecommenderId(1L);
        queryDto.setOrderType(null); // 默认查询全部
        queryDto.setKeyword(null);
        queryDto.setSettlementStartDate(LocalDate.now().minusDays(30));
        queryDto.setSettlementEndDate(LocalDate.now());
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        return queryDto;
    }

    /**
     * 构建Mock推荐方信息
     */
    private RecommenderInfo buildMockRecommenderInfo() {
        RecommenderInfo recommenderInfo = new RecommenderInfo();
        recommenderInfo.setId(1L);
        recommenderInfo.setUserId(1L);
        recommenderInfo.setName("测试推荐方");
        recommenderInfo.setType(1);
        recommenderInfo.setCreateTime(LocalDateTime.now());
        recommenderInfo.setUpdateTime(LocalDateTime.now());
        return recommenderInfo;
    }

    /**
     * 构建Mock订单列表
     */
    private List<RecommenderOrderVo> buildMockOrderList() {
        List<RecommenderOrderVo> orderList = new ArrayList<>();

        // 酒店订单
        RecommenderOrderVo hotelOrder = new RecommenderOrderVo();
        hotelOrder.setOrderNo("H202501290001");
        hotelOrder.setOrderType(1); // 会自动设置orderTypeDesc
        hotelOrder.setSupplierName("测试酒店");
        hotelOrder.setCommissionAmount(new BigDecimal("80.00"));
        hotelOrder.setSettlementTime(LocalDateTime.now().minusDays(1));
        hotelOrder.setRecommenderId(1L);
        orderList.add(hotelOrder);

        // 导游订单
        RecommenderOrderVo tourGuideOrder = new RecommenderOrderVo();
        tourGuideOrder.setOrderNo("T202501290002");
        tourGuideOrder.setOrderType(2); // 会自动设置orderTypeDesc
        tourGuideOrder.setSupplierName("测试导游服务");
        tourGuideOrder.setCommissionAmount(new BigDecimal("70.00"));
        tourGuideOrder.setSettlementTime(LocalDateTime.now().minusDays(2));
        tourGuideOrder.setRecommenderId(1L);
        orderList.add(tourGuideOrder);

        return orderList;
    }

    /**
     * 构建Mock酒店订单列表
     */
    private List<RecommenderOrderVo> buildMockHotelOrderList() {
        List<RecommenderOrderVo> orderList = new ArrayList<>();

        RecommenderOrderVo hotelOrder = new RecommenderOrderVo();
        hotelOrder.setOrderNo("H202501290001");
        hotelOrder.setOrderType(1); // 会自动设置orderTypeDesc
        hotelOrder.setSupplierName("测试酒店");
        hotelOrder.setCommissionAmount(new BigDecimal("80.00"));
        hotelOrder.setSettlementTime(LocalDateTime.now().minusDays(1));
        hotelOrder.setRecommenderId(1L);
        orderList.add(hotelOrder);

        return orderList;
    }

    /**
     * 构建Mock导游订单列表
     */
    private List<RecommenderOrderVo> buildMockTourGuideOrderList() {
        List<RecommenderOrderVo> orderList = new ArrayList<>();

        RecommenderOrderVo tourGuideOrder = new RecommenderOrderVo();
        tourGuideOrder.setOrderNo("T202501290002");
        tourGuideOrder.setOrderType(2); // 会自动设置orderTypeDesc
        tourGuideOrder.setSupplierName("测试导游服务");
        tourGuideOrder.setCommissionAmount(new BigDecimal("70.00"));
        tourGuideOrder.setSettlementTime(LocalDateTime.now().minusDays(2));
        tourGuideOrder.setRecommenderId(1L);
        orderList.add(tourGuideOrder);

        return orderList;
    }


    /**
     * 测试getRecommenderOrderList内部方法 - 分支B8：异常处理的情况
     * 分支：B8 - catch (Exception e) 返回失败："查询订单列表失败"
     * 注意：通过getRecommenderOrderListByUserId间接测试内部方法
     */
    @Test
    public void test14_GetOrderList_BranchB8_ExceptionHandlingViaUserId() {
        log.info("=== 测试getRecommenderOrderList内部方法 - 分支B8：异常处理的情况 ===");

        Long userId = 1L;
        RecommenderOrderQueryDto queryDto = buildValidQueryDto();

        log.info("测试数据: userId = {}, queryDto = {}", userId, JSON.toJSONString(queryDto));

        // Mock推荐方信息存在（通过userId查询）
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectOne(any(), eq(false))).thenReturn(mockRecommenderInfo);

        // Mock推荐方信息查询抛出异常（在内部方法中）
        when(recommenderMapper.selectById(any()))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证分支B8的结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
        assertThat(result.getMessage()).isEqualTo("查询订单列表失败");
        assertThat(result.getData()).isNull();

        log.info("✅ 分支B8测试通过：异常处理时正确返回失败结果");

        // 验证Mock调用
        verify(recommenderMapper, times(1)).selectById(any());
    }

    /**
     * Mock订单查询结果
     */
    private void mockOrderQueryResults() {
        List<RecommenderOrderVo> mockOrderList = buildMockOrderList();
        when(recommenderOrderMapper.selectRecommenderOrderList(any())).thenReturn(mockOrderList);
        when(recommenderOrderMapper.selectRecommenderOrderCount(any())).thenReturn(2L);
        when(recommenderOrderMapper.selectRecommenderOrderCommissionSum(any())).thenReturn(new BigDecimal("150.00"));

        // Mock推荐方信息查询（用于getRecommenderOrderList方法）
        RecommenderInfo mockRecommenderInfo = buildMockRecommenderInfo();
        when(recommenderMapper.selectById(any())).thenReturn(mockRecommenderInfo);
    }
}