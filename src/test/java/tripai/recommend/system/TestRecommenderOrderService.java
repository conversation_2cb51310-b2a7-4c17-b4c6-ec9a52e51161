package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderOrderQueryDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderListVo;
import tripai.recommend.system.service.RecommenderOrderService;

import java.time.LocalDate;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderOrderService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/25 10:00
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderOrderService {

    @Resource
    private RecommenderOrderService recommenderOrderService;

}

