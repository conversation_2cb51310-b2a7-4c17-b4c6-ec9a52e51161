package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderOrderQueryDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderListVo;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.mapper.recommender.RecommenderOrderMapper;
import tripai.recommend.system.service.RecommenderOrderService;

import java.time.LocalDate;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderOrderService
 * @author: lijunqi
 * @description:
 * @date: 2025/7/30 10:25
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderOrderService {
    @Resource
    private RecommenderOrderService recommenderOrderService;

    @Resource
    private RecommenderMapper recommenderMapper;

    @Resource
    private RecommenderOrderMapper recommenderOrderMapper;

    /**
     * 测试getRecommenderOrderListByUserId方法
     */
    @Test
    public void test_GetOrderListByUserId() {
        log.info("=== 测试getRecommenderOrderListByUserId方法 ===");
        RecommenderOrderQueryDto queryDto = new RecommenderOrderQueryDto();
        queryDto.setRecommenderId(1L);
        queryDto.setOrderType(1); // 订单类型：null=全部，1=酒店订单，2=导游订单
        queryDto.setKeyword("45654564"); // 关键词搜索（订单号、酒店名称、导游供应商姓名）
        queryDto.setSettlementStartDate(LocalDate.now().minusDays(30));
        queryDto.setSettlementEndDate(LocalDate.now());
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);

        Long userId = 1L;

        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(userId, queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

}
