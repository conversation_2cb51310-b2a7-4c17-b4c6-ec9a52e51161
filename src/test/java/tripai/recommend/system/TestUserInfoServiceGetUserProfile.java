package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DataAccessException;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.vo.user.UserProfileVo;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.service.UserInfoService;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestUserInfoServiceGetUserProfile
 * @author: lijunqi
 * @description:
 * @date: 2025/7/29 10:05
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestUserInfoServiceGetUserProfile {

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private RecommenderMapper recommenderMapper;

    // ==================== getUserProfile方法100%分支覆盖率测试 ====================

    // ==================== 用户个人中心测试 ====================

    /**
     * 测试获取用户个人中心信息 - 正常流程
     */
    @Test
    public void testGetUserProfileSuccess() {
        log.info("=== 测试获取用户个人中心信息 - 正常流程 ===");

        Long userId = 1L; // 使用存在的用户ID

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试获取用户个人中心信息 - 无效用户ID
     */
    @Test
    public void testGetUserProfileWithInvalidUserId() {
        log.info("=== 测试获取用户个人中心信息 - 无效用户ID ===");

        Long userId = -1L; // 无效的用户ID

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("无效用户ID测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 空用户ID
     */
    @Test
    public void testGetUserProfileWithNullUserId() {
        log.info("=== 测试获取用户个人中心信息 - 空用户ID ===");

        Long userId = null;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("空用户ID测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 用户存在但推荐方信息不存在
     */
    @Test
    public void testGetUserProfileWithNoRecommenderInfo() {
        log.info("=== 测试获取用户个人中心信息 - 用户存在但推荐方信息不存在 ===");

        // 使用一个存在的用户ID，但该用户没有推荐方信息
        Long userId = 6L; // 假设这个用户存在但没有推荐方信息

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 用户未绑定微信
     */
    @Test
    public void testGetUserProfileWithNoWechatBinding() {
        log.info("=== 测试获取用户个人中心信息 - 用户未绑定微信 ===");

        // 使用一个未绑定微信的用户ID
        Long userId = 2L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("未绑定微信测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试获取用户个人中心信息 - 用户已绑定微信
     */
    @Test
    public void testGetUserProfileWithWechatBinding() {
        log.info("=== 测试获取用户个人中心信息 - 用户已绑定微信 ===");

        // 使用一个已绑定微信的用户ID
        Long userId = 1L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("已绑定微信测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 推荐方存在但无银行信息
     */
    @Test
    public void testGetUserProfileWithNoBankInfo() {
        log.info("=== 测试获取用户个人中心信息 - 推荐方存在但无银行信息 ===");

        // 使用一个有推荐方信息但无银行信息的用户ID
        Long userId = 996L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("无银行信息测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证银行相关字段应该为null
            assertThat(profile.getBankCardNo()).isNull();
            assertThat(profile.getBankName()).isNull();
            assertThat(profile.getBankValidateStatus()).isNull();
            log.info("无银行信息的情况测试通过");
        }
    }

    /**
     * 测试获取用户个人中心信息 - 推荐方存在且有银行信息
     */
    @Test
    public void testGetUserProfileWithBankInfo() {
        log.info("=== 测试获取用户个人中心信息 - 推荐方存在且有银行信息 ===");

        // 使用一个有完整银行信息的用户ID
        Long userId = 995L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("有银行信息测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证银行信息字段不为null（如果数据存在的话）
            if (profile.getBankCardNo() != null) {
                assertThat(profile.getBankCardNo()).isNotEmpty();
                log.info("银行卡号: {}", profile.getBankCardNo());
            }
            if (profile.getBankName() != null) {
                assertThat(profile.getBankName()).isNotEmpty();
                log.info("银行名称: {}", profile.getBankName());
            }
            log.info("银行信息存在的情况测试通过");
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户手机号为空
     */
    @Test
    public void testGetUserProfileWithEmptyMobile() {
        log.info("=== 测试获取用户个人中心信息 - 用户手机号为空 ===");

        // 使用一个手机号为空的用户ID
        Long userId = 994L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("手机号为空测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        if (result != null && result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();
            // 验证手机号字段的处理
            log.info("手机号字段: {}", profile.getMobile());
            // 手机号为空时，脱敏处理分支不会执行，字段应该为null
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户邮箱为空（False分支）
     * 目标：覆盖 if (StrUtil.isNotBlank(userInfo.getEmail())) 的False分支
     */
    @Test
    public void testGetUserProfileWithEmptyEmail() {
        log.info("=== 测试获取用户个人中心信息 - 用户邮箱为空（False分支） ===");

        // 测试多个可能邮箱为空的用户ID
        Long[] testUserIds = {6L};

        boolean foundEmptyEmailUser = false;

        for (Long userId : testUserIds) {
            log.info("测试用户ID: {}", userId);

            // 调用服务方法
            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            log.info("用户ID {} 测试结果: {}", userId, JSON.toJSONString(result));

            // 验证结果
            if (result != null && result.getCode() == 200 && result.getData() != null) {
                UserProfileVo profile = result.getData();
                log.info("用户ID {} 邮箱字段: {}", userId, profile.getEmail());

                // 如果邮箱字段为null，说明覆盖了False分支
                if (profile.getEmail() == null) {
                    log.info("✓ 成功覆盖邮箱为空的False分支，用户ID: {}", userId);
                    foundEmptyEmailUser = true;

                    // 验证邮箱为空时，脱敏处理分支不会执行，字段应该为null
                    assertThat(profile.getEmail()).isNull();
                    break;
                }
            } else if (result != null && result.getCode() != 200) {
                log.info("用户ID {} 不存在或查询失败: {}", userId, result.getMessage());
            }
        }

        if (!foundEmptyEmailUser) {
            log.warn("⚠ 未找到邮箱为空的用户，可能需要准备测试数据");
        }
    }

    /**
     * 测试获取用户个人中心信息 - 用户邮箱为空
     */
    @Test
    public void testGetUserProfileWithNotEmptyEmail() {
        log.info("=== 测试获取用户个人中心信息 - 用户邮箱不为空 ===");

        // 使用一个邮箱不为空的用户ID
        Long userId = 5L;

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("邮箱不为空测试结果: {}", JSON.toJSONString(result));

    }

    /**
     * 测试获取用户个人中心信息 - 边界值测试
     */
    @Test
    public void testGetUserProfileBoundaryValues() {
        log.info("=== 测试获取用户个人中心信息 - 边界值测试 ===");

        // 测试各种边界值
        Long[] boundaryUserIds = {0L, -1L, Long.MAX_VALUE, Long.MIN_VALUE};

        for (Long userId : boundaryUserIds) {
            log.info("测试边界值用户ID: {}", userId);

            try {
                ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);
                log.info("边界值 {} 测试结果: {}", userId,
                        result != null ? result.getCode() : "null");

                // 验证结果不为null
                assertThat(result).isNotNull();

                // 对于无效的用户ID，应该返回用户不存在的错误
                if (userId <= 0) {
                    // 预期返回用户不存在的错误
                    log.info("边界值 {} 返回预期的错误结果", userId);
                }

            } catch (Exception e) {
                log.warn("边界值 {} 测试出现异常: {}", userId, e.getMessage());
                // 边界值测试出现异常是可以接受的
            }
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据完整性验证
     */
    @Test
    public void testGetUserProfileDataIntegrity() {
        log.info("=== 测试获取用户个人中心信息 - 数据完整性验证 ===");

        Long userId = 1L; // 使用存在的用户ID

        // 调用服务方法
        ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

        log.info("数据完整性测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();

        if (result.getCode() == 200 && result.getData() != null) {
            UserProfileVo profile = result.getData();

            // 验证必需字段
            assertThat(profile.getUserId()).isNotNull();
            log.info("用户ID验证通过: {}", profile.getUserId());

            // 验证微信绑定状态字段（必须有值，0或1）
            assertThat(profile.getWechatBindStatus()).isNotNull();
            assertThat(profile.getWechatBindStatus()).isIn(0, 1);
            log.info("微信绑定状态验证通过: {}", profile.getWechatBindStatus());

            // 验证数据脱敏效果（如果字段不为空）
            if (profile.getMobile() != null) {
                assertThat(profile.getMobile()).contains("*");
                log.info("手机号脱敏验证通过: {}", profile.getMobile());
            }

            if (profile.getEmail() != null) {
                assertThat(profile.getEmail()).contains("*");
                log.info("邮箱脱敏验证通过: {}", profile.getEmail());
            }

            if (profile.getBankCardNo() != null) {
                assertThat(profile.getBankCardNo()).contains("*");
                log.info("银行卡号脱敏验证通过: {}", profile.getBankCardNo());
            }
        }
    }

    /**
     * 测试获取用户个人中心信息 - 组合场景测试
     */
    @Test
    public void testGetUserProfileCombinationScenarios() {
        log.info("=== 测试获取用户个人中心信息 - 组合场景测试 ===");

        // 测试不同的用户ID组合，覆盖各种数据组合情况
        Long[] testUserIds = {1L, 2L, 3L, 4L, 5L};

        for (Long userId : testUserIds) {
            log.info("测试组合场景，用户ID: {}", userId);

            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            if (result != null) {
                log.info("用户ID {} 测试结果码: {}", userId, result.getCode());

                if (result.getCode() == 200 && result.getData() != null) {
                    UserProfileVo profile = result.getData();

                    // 记录各个字段的状态，用于分析覆盖情况
                    log.info("用户ID {}: 推荐方信息={}, 微信绑定={}, 银行信息={}, 手机号={}, 邮箱={}",
                            userId,
                            profile.getName() != null ? "存在" : "不存在",
                            profile.getWechatBindStatus(),
                            profile.getBankCardNo() != null ? "存在" : "不存在",
                            profile.getMobile() != null ? "存在" : "不存在",
                            profile.getEmail() != null ? "存在" : "不存在"
                    );
                }
            }
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据库查询异常模拟
     * 注意：这个测试需要使用Mock来模拟异常情况
     */
    @Test
    public void testGetUserProfileWithDatabaseException() {
        log.info("=== 测试获取用户个人中心信息 - 数据库查询异常模拟 ===");

        // 这里可以通过以下方式模拟异常：
        // 1. 使用无效的数据库连接
        // 2. 使用Mock框架模拟异常
        // 3. 使用特殊的测试数据触发异常

        Long userId = 1L;
        Long recommenderId = 1L;

        try {
            // 在实际项目中，可以通过以下方式模拟异常：
            // - 临时关闭数据库连接
            // - 使用@MockBean注解Mock相关的Mapper
            // - 使用TestContainers模拟数据库故障
            // Mock推荐方信息查询抛出异常
            LambdaQueryWrapper<RecommenderInfo> recommenderWrapper = new LambdaQueryWrapper<>();
            recommenderWrapper.eq(RecommenderInfo::getUserId, userId)
                    .eq(RecommenderInfo::getIsDel, 0);
            when(recommenderMapper.selectOne(recommenderWrapper))
                    .thenThrow(new DataAccessException("数据库连接异常") {
                    });

            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            log.info("异常模拟测试结果: {}", JSON.toJSONString(result));

            // 验证异常处理是否正确
            assertThat(result).isNotNull();

        } catch (Exception e) {
            log.info("捕获到预期的异常: {}", e.getMessage());
            // 验证异常类型和消息
            assertThat(e).isNotNull();
        }
    }

    /**
     * 测试获取用户个人中心信息 - 数据脱敏异常模拟
     */
    @Test
    public void testGetUserProfileWithMaskingException() {
        log.info("=== 测试获取用户个人中心信息 - 数据脱敏异常模拟 ===");

        // 这个测试主要验证当数据脱敏工具出现异常时的处理
        // 在实际项目中，可以通过Mock DataMaskingUtil来模拟异常

        Long userId = 1L;

        try {
            ResponseResult<UserProfileVo> result = userInfoService.getUserProfile(userId);

            log.info("数据脱敏异常测试结果: {}", JSON.toJSONString(result));

            // 验证结果
            assertThat(result).isNotNull();

            // 如果脱敏工具异常，应该通过全局异常处理返回错误信息
            if (result.getCode() != 200) {
                log.info("数据脱敏异常被正确处理，返回错误码: {}", result.getCode());
            }

        } catch (Exception e) {
            log.info("数据脱敏异常被捕获: {}", e.getMessage());
            assertThat(e).isNotNull();
        }
    }


}
