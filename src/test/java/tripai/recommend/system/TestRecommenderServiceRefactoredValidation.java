package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.dto.recommender.RecommenderBankInfoDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderReqDto;
import tripai.recommend.system.domain.entity.FileInfo;
import tripai.recommend.system.exception.BusinessException;
import tripai.recommend.system.service.impl.RecommenderServiceImpl_Refactored_Validation;

import static org.assertj.core.api.Assertions.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderServiceRefactoredValidation
 * @author: lijunqi
 * @description: 重构后验证逻辑的测试类
 * @date: 2025/7/29 16:45
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderServiceRefactoredValidation {

    private final RecommenderServiceImpl_Refactored_Validation validator = 
            new RecommenderServiceImpl_Refactored_Validation();

    // ==================== 数据完整性验证测试 ====================

    /**
     * 测试数据完整性验证 - 确保测试环境正常
     */
    @Test
    public void test01_DataIntegrityVerification() {
        log.info("=== 测试数据完整性验证 - 确保测试环境正常 ===");

        assertThat(validator).isNotNull();

        // 验证基础DTO构建
        RecommenderReqDto personalDto = buildValidPersonalRecommenderDto();
        assertThat(personalDto).isNotNull();
        assertThat(personalDto.getType()).isEqualTo(1);

        RecommenderReqDto enterpriseDto = buildValidEnterpriseRecommenderDto();
        assertThat(enterpriseDto).isNotNull();
        assertThat(enterpriseDto.getType()).isEqualTo(2);

        log.info("✅ 数据完整性验证通过");
    }

    // ==================== 基础字段验证测试 ====================

    /**
     * 测试基础字段验证 - 姓名为空
     */
    @Test
    public void test02_ValidateBasicFields_NameEmpty() {
        log.info("=== 测试基础字段验证 - 姓名为空 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setName(null);

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方名称不能为空");

        log.info("✅ 姓名为空验证通过");
    }

    /**
     * 测试基础字段验证 - 证件号为空
     */
    @Test
    public void test03_ValidateBasicFields_IdentifierEmpty() {
        log.info("=== 测试基础字段验证 - 证件号为空 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setIdentifier(null);

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方证件号不能为空");

        log.info("✅ 证件号为空验证通过");
    }

    /**
     * 测试基础字段验证 - 银行信息为空
     */
    @Test
    public void test04_ValidateBasicFields_BankInfoNull() {
        log.info("=== 测试基础字段验证 - 银行信息为空 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setBankInfo(null);

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方银行卡信息不能为空");

        log.info("✅ 银行信息为空验证通过");
    }

    // ==================== 个人类型验证测试 ====================

    /**
     * 测试个人类型验证 - 姓名长度超限
     */
    @Test
    public void test05_ValidatePersonal_NameTooLong() {
        log.info("=== 测试个人类型验证 - 姓名长度超限 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setName("这是一个非常非常非常非常非常长的姓名超过了二十个字符的限制");

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方姓名不能超过20个字符");

        log.info("✅ 个人姓名长度验证通过");
    }

    /**
     * 测试个人类型验证 - 身份证号格式错误
     */
    @Test
    public void test06_ValidatePersonal_InvalidIdCard() {
        log.info("=== 测试个人类型验证 - 身份证号格式错误 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setIdentifier("123456789012345678"); // 错误格式

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方身份证证件号格式不正确");

        log.info("✅ 身份证号格式验证通过");
    }

    /**
     * 测试个人类型验证 - 证件类型为空
     */
    @Test
    public void test07_ValidatePersonal_CertificateTypeNull() {
        log.info("=== 测试个人类型验证 - 证件类型为空 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setCertificateType(null);

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方证件类型不能为空");

        log.info("✅ 证件类型验证通过");
    }

    /**
     * 测试个人类型验证 - 非草稿模式缺少身份证照片
     */
    @Test
    public void test08_ValidatePersonal_MissingIdCardPhotos() {
        log.info("=== 测试个人类型验证 - 非草稿模式缺少身份证照片 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setIsDraft(false);
        dto.setIdCardFrontUrl(null);

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("个人推荐方必须上传身份证正反面照片");

        log.info("✅ 身份证照片验证通过");
    }

    /**
     * 测试个人类型验证 - 银行信息一致性：姓名不一致
     */
    @Test
    public void test09_ValidatePersonal_BankNameInconsistent() {
        log.info("=== 测试个人类型验证 - 银行信息一致性：姓名不一致 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setName("张三");
        dto.getBankInfo().setAccountName("李四");

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方银行卡信息开户户名和推荐方名称不一致");

        log.info("✅ 个人银行姓名一致性验证通过");
    }

    /**
     * 测试个人类型验证 - 银行信息一致性：身份证号不一致
     */
    @Test
    public void test10_ValidatePersonal_BankIdCardInconsistent() {
        log.info("=== 测试个人类型验证 - 银行信息一致性：身份证号不一致 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.setIdentifier("110101199001011234");
        dto.getBankInfo().setIdCardNo("110101199001015678");

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方银行卡信息身份证证件号和推荐方身份证证件号不一致");

        log.info("✅ 个人银行身份证号一致性验证通过");
    }

    // ==================== 企业类型验证测试 ====================

    /**
     * 测试企业类型验证 - 企业名称长度超限
     */
    @Test
    public void test11_ValidateEnterprise_NameTooLong() {
        log.info("=== 测试企业类型验证 - 企业名称长度超限 ===");

        RecommenderReqDto dto = buildValidEnterpriseRecommenderDto();
        dto.setName("这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的企业名称超过了五十个字符的限制");

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方企业全称不能超过50个字符");

        log.info("✅ 企业名称长度验证通过");
    }

    /**
     * 测试企业类型验证 - 统一社会信用代码格式错误
     */
    @Test
    public void test12_ValidateEnterprise_InvalidCreditCode() {
        log.info("=== 测试企业类型验证 - 统一社会信用代码格式错误 ===");

        RecommenderReqDto dto = buildValidEnterpriseRecommenderDto();
        dto.setIdentifier("123456789012345678"); // 错误格式

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方社会信用代码格式不正确");

        log.info("✅ 统一社会信用代码格式验证通过");
    }

    /**
     * 测试企业类型验证 - 非草稿模式缺少营业执照
     */
    @Test
    public void test13_ValidateEnterprise_MissingBusinessLicense() {
        log.info("=== 测试企业类型验证 - 非草稿模式缺少营业执照 ===");

        RecommenderReqDto dto = buildValidEnterpriseRecommenderDto();
        dto.setIsDraft(false);
        dto.setBusinessLicenseUrl(null);

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("企业推荐方必须上传营业执照");

        log.info("✅ 营业执照验证通过");
    }

    /**
     * 测试企业类型验证 - 银行信息一致性：企业名称不一致
     */
    @Test
    public void test14_ValidateEnterprise_BankNameInconsistent() {
        log.info("=== 测试企业类型验证 - 银行信息一致性：企业名称不一致 ===");

        RecommenderReqDto dto = buildValidEnterpriseRecommenderDto();
        dto.setName("测试企业A有限公司");
        dto.getBankInfo().setAccountName("测试企业B有限公司");

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方银行卡信息开户户名和推荐方名称不一致");

        log.info("✅ 企业银行名称一致性验证通过");
    }

    /**
     * 测试企业类型验证 - 银行信息一致性：纳税人识别号不一致
     */
    @Test
    public void test15_ValidateEnterprise_BankTaxIdInconsistent() {
        log.info("=== 测试企业类型验证 - 银行信息一致性：纳税人识别号不一致 ===");

        RecommenderReqDto dto = buildValidEnterpriseRecommenderDto();
        dto.setIdentifier("91110000123456789X");
        dto.getBankInfo().setIdCardNo("91110000987654321Y");

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("推荐方银行卡信息纳税人识别号和推荐方社会信用代码不一致");

        log.info("✅ 企业银行纳税人识别号一致性验证通过");
    }

    // ==================== 银行信息验证测试 ====================

    /**
     * 测试银行信息验证 - 开户人姓名为空
     */
    @Test
    public void test16_ValidateBankInfo_AccountNameEmpty() {
        log.info("=== 测试银行信息验证 - 开户人姓名为空 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.getBankInfo().setAccountName(null);

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("开户人姓名不能为空");

        log.info("✅ 开户人姓名验证通过");
    }

    /**
     * 测试银行信息验证 - 银行卡号为空
     */
    @Test
    public void test17_ValidateBankInfo_BankCardNoEmpty() {
        log.info("=== 测试银行信息验证 - 银行卡号为空 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();
        dto.getBankInfo().setBankCardNo(null);

        assertThatThrownBy(() -> validator.validateRequest(dto))
                .isInstanceOf(BusinessException.class)
                .hasMessage("银行卡号不能为空");

        log.info("✅ 银行卡号验证通过");
    }

    /**
     * 测试成功场景 - 个人类型验证通过
     */
    @Test
    public void test18_ValidateSuccess_Personal() {
        log.info("=== 测试成功场景 - 个人类型验证通过 ===");

        RecommenderReqDto dto = buildValidPersonalRecommenderDto();

        assertThatCode(() -> validator.validateRequest(dto))
                .doesNotThrowAnyException();

        log.info("✅ 个人类型验证成功");
    }

    /**
     * 测试成功场景 - 企业类型验证通过
     */
    @Test
    public void test19_ValidateSuccess_Enterprise() {
        log.info("=== 测试成功场景 - 企业类型验证通过 ===");

        RecommenderReqDto dto = buildValidEnterpriseRecommenderDto();

        assertThatCode(() -> validator.validateRequest(dto))
                .doesNotThrowAnyException();

        log.info("✅ 企业类型验证成功");
    }

    // ==================== 测试辅助方法 ====================

    /**
     * 构建有效的个人类型推荐方DTO
     */
    private RecommenderReqDto buildValidPersonalRecommenderDto() {
        RecommenderReqDto dto = new RecommenderReqDto();
        dto.setUserId(1L);
        dto.setName("张三");
        dto.setType(1); // 个人类型
        dto.setIdentifier("110101199001011234");
        dto.setCertificateType(1);
        dto.setIdCardFrontUrl(buildFileInfoJson("http://example.com/rms/front.jpg", "front.jpg"));
        dto.setIdCardBackUrl(buildFileInfoJson("http://example.com/rms/back.jpg", "back.jpg"));
        dto.setIsDraft(true); // 默认草稿模式

        // 个人银行信息
        RecommenderBankInfoDto bankInfo = new RecommenderBankInfoDto();
        bankInfo.setAccountName("张三");
        bankInfo.setAccountPhone("***********");
        bankInfo.setBankCardNo("****************");
        bankInfo.setBankName("工商银行");
        bankInfo.setProvince("北京市");
        bankInfo.setCity("北京市");
        bankInfo.setBranchName("北京分行");
        bankInfo.setIdCardNo("110101199001011234");
        bankInfo.setAccountCertificateType(1);
        bankInfo.setAccountIdentifier("110101199001011234");
        dto.setBankInfo(bankInfo);

        return dto;
    }

    /**
     * 构建有效的企业类型推荐方DTO
     */
    private RecommenderReqDto buildValidEnterpriseRecommenderDto() {
        RecommenderReqDto dto = new RecommenderReqDto();
        dto.setUserId(2L);
        dto.setName("测试企业有限公司");
        dto.setType(2); // 企业类型
        dto.setIdentifier("91110000123456789X");
        dto.setBusinessLicenseUrl(buildFileInfoJson("http://example.com/rms/license.jpg", "license.jpg"));
        dto.setIsDraft(true); // 默认草稿模式

        // 企业银行信息
        RecommenderBankInfoDto bankInfo = new RecommenderBankInfoDto();
        bankInfo.setAccountName("测试企业有限公司");
        bankInfo.setAccountPhone("***********");
        bankInfo.setBankCardNo("1234567890123456789");
        bankInfo.setBankName("工商银行");
        bankInfo.setProvince("北京市");
        bankInfo.setCity("北京市");
        bankInfo.setBranchName("北京分行");
        bankInfo.setIdCardNo("91110000123456789X");
        bankInfo.setAccountIdentifier("91110000123456789X");
        dto.setBankInfo(bankInfo);

        return dto;
    }

    /**
     * 构建FileInfo JSON字符串
     */
    private String buildFileInfoJson(String url, String fileName) {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setUrl(url);
        fileInfo.setName(fileName);
        fileInfo.setType(1);
        fileInfo.setBucket("rms");
        
        return JSON.toJSONString(fileInfo);
    }
}
