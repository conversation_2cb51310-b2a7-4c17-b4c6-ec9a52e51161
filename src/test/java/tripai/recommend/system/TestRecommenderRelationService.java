package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderGuideRelationQueryDto;
import tripai.recommend.system.domain.dto.recommender.RecommenderHotelRelationQueryDto;
import tripai.recommend.system.domain.vo.recommender.RecommenderGuideRelationListVo;
import tripai.recommend.system.domain.vo.recommender.RecommenderHotelRelationListVo;
import tripai.recommend.system.service.RecommenderRelationService;

import java.time.LocalDate;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestRecommenderRelationService
 * @author: lijunqi
 * @description: RecommenderRelationService测试类
 * @date: 2025/7/28 15:30
 * @version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestRecommenderRelationService {

    @Resource
    private RecommenderRelationService recommenderRelationService;

    // ==================== 酒店关系列表查询测试 ====================

    /**
     * 测试查询推荐方酒店关系列表 - 正常流程
     */
    @Test
    public void testGetRecommenderHotelRelationListSuccess() {
        log.info("=== 测试查询推荐方酒店关系列表 - 正常流程 ===");

        // 构建测试数据
        RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
        queryDto.setUserId(1L); // 使用存在的用户ID

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试酒店关系列表查询 - 关键词搜索
     */
    @Test
    public void testGetHotelRelationListWithKeyword() {
        log.info("=== 测试酒店关系列表查询 - 关键词搜索 ===");

        // 构建测试数据
        RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
        queryDto.setUserId(1L);
        queryDto.setKeyword("酒店"); // 设置搜索关键词

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("关键词搜索结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试酒店关系列表查询 - 日期筛选
     */
    @Test
    public void testGetHotelRelationListWithDateFilter() {
        log.info("=== 测试酒店关系列表查询 - 日期筛选 ===");

        // 构建测试数据
        RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
        queryDto.setUserId(1L);
        queryDto.setRelationStartDate(LocalDate.of(2025, 1, 1));
        queryDto.setRelationEndDate(LocalDate.of(2025, 12, 31));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("日期筛选结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试酒店关系列表查询 - 不同分页大小
     */
    @Test
    public void testGetHotelRelationListWithDifferentPageSizes() {
        log.info("=== 测试酒店关系列表查询 - 不同分页大小 ===");

        Integer[] pageSizes = {50, 100, 150, 200};

        for (Integer pageSize : pageSizes) {
            log.info("测试分页大小: {}", pageSize);

            // 构建测试数据
            RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
            queryDto.setUserId(1L);
            queryDto.setPageSize(pageSize);

            // 调用服务方法
            long startTime = System.currentTimeMillis();
            ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
            long endTime = System.currentTimeMillis();

            log.info("分页大小: {}, 执行时间: {}ms", pageSize, endTime - startTime);
        }
    }

    /**
     * 测试酒店关系列表查询 - 不同排序类型
     */
    @Test
    public void testGetHotelRelationListWithDifferentSortTypes() {
        log.info("=== 测试酒店关系列表查询 - 不同排序类型 ===");

        Integer[] sortTypes = {1, 2, 3};

        for (Integer sortType : sortTypes) {
            log.info("测试排序类型: {}", sortType);

            // 构建测试数据
            RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
            queryDto.setUserId(1L);
            queryDto.setSortType(sortType);

            // 调用服务方法
            long startTime = System.currentTimeMillis();
            ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
            long endTime = System.currentTimeMillis();

            log.info("排序类型: {}, 执行时间: {}ms", sortType, endTime - startTime);
        }
    }

    // ==================== 导游供应商列表查询测试 ====================

    /**
     * 测试查询推荐方导游供应商列表 - 正常流程
     */
    @Test
    public void testGetTourGuideSupplierListSuccess() {
        log.info("=== 测试查询推荐方导游供应商列表 - 正常流程 ===");

        // 构建测试数据
        RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
        queryDto.setRecommenderId(1L); // 使用存在的推荐方ID

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试导游供应商列表查询 - 产线筛选
     */
    @Test
    public void testGetGuideSupplierListWithProductionLineFilter() {
        log.info("=== 测试导游供应商列表查询 - 产线筛选 ===");

        Integer[] productionLines = {1, 2, 3}; // 1=司机，2=导游，3=司兼导

        for (Integer productionLine : productionLines) {
            log.info("测试产线筛选: {}", productionLine);

            // 构建测试数据
            RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
            queryDto.setRecommenderId(1L);
            queryDto.setProductionLine(productionLine);

            // 调用服务方法
            long startTime = System.currentTimeMillis();
            ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
            long endTime = System.currentTimeMillis();

            log.info("产线筛选: {}, 执行时间: {}ms", productionLine, endTime - startTime);
        }
    }

    /**
     * 测试导游供应商列表查询 - 状态筛选
     */
    @Test
    public void testGetGuideSupplierListWithControlStatusFilter() {
        log.info("=== 测试导游供应商列表查询 - 状态筛选 ===");

        String[] controlStatuses = {"normal", "controlled", "all"};

        for (String controlStatus : controlStatuses) {
            log.info("测试状态筛选: {}", controlStatus);

            // 构建测试数据
            RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
            queryDto.setRecommenderId(1L);
            queryDto.setControlStatus(controlStatus);

            // 调用服务方法
            long startTime = System.currentTimeMillis();
            ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
            long endTime = System.currentTimeMillis();

            log.info("状态筛选: {}, 执行时间: {}ms", controlStatus, endTime - startTime);
        }
    }

    /**
     * 测试导游供应商列表查询 - 关键词搜索
     */
    @Test
    public void testGetGuideSupplierListWithKeyword() {
        log.info("=== 测试导游供应商列表查询 - 关键词搜索 ===");

        // 构建测试数据
        RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
        queryDto.setRecommenderId(1L);
        queryDto.setKeyword("张三"); // 设置搜索关键词

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("关键词搜索结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试导游供应商列表查询 - 日期筛选
     */
    @Test
    public void testGetGuideSupplierListWithDateFilter() {
        log.info("=== 测试导游供应商列表查询 - 日期筛选 ===");

        // 构建测试数据
        RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
        queryDto.setRecommenderId(1L);
        queryDto.setRelationStartDate(LocalDate.of(2025, 1, 1));
        queryDto.setRelationEndDate(LocalDate.of(2025, 12, 31));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("日期筛选结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试导游供应商列表查询 - 不同分页大小
     */
    @Test
    public void testGetGuideSupplierListWithDifferentPageSizes() {
        log.info("=== 测试导游供应商列表查询 - 不同分页大小 ===");

        Integer[] pageSizes = {50, 100, 150, 200};

        for (Integer pageSize : pageSizes) {
            log.info("测试分页大小: {}", pageSize);

            // 构建测试数据
            RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
            queryDto.setRecommenderId(1L);
            queryDto.setPageSize(pageSize);

            // 调用服务方法
            long startTime = System.currentTimeMillis();
            ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
            long endTime = System.currentTimeMillis();

            log.info("分页大小: {}, 执行时间: {}ms", pageSize, endTime - startTime);
        }
    }

    // ==================== 边界条件和异常测试 ====================

    /**
     * 测试酒店关系列表查询 - 无效用户ID
     */
    @Test
    public void testGetHotelRelationListWithInvalidUserId() {
        log.info("=== 测试酒店关系列表查询 - 无效用户ID ===");

        // 构建测试数据
        RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
        queryDto.setUserId(-1L); // 无效的用户ID

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("无效用户ID测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试酒店关系列表查询 - 空关键词
     */
    @Test
    public void testGetHotelRelationListWithEmptyKeyword() {
        log.info("=== 测试酒店关系列表查询 - 空关键词 ===");

        // 构建测试数据
        RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
        queryDto.setUserId(1L);
        queryDto.setKeyword(""); // 空关键词

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("空关键词测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试酒店关系列表查询 - 日期范围颠倒
     */
    @Test
    public void testGetHotelRelationListWithReversedDateRange() {
        log.info("=== 测试酒店关系列表查询 - 日期范围颠倒 ===");

        // 构建测试数据
        RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
        queryDto.setUserId(1L);
        queryDto.setRelationStartDate(LocalDate.of(2025, 12, 31)); // 开始日期晚于结束日期
        queryDto.setRelationEndDate(LocalDate.of(2025, 1, 1));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("日期范围颠倒测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试导游供应商列表查询 - 无效推荐方ID
     */
    @Test
    public void testGetGuideSupplierListWithInvalidRecommenderId() {
        log.info("=== 测试导游供应商列表查询 - 无效推荐方ID ===");

        // 构建测试数据
        RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
        queryDto.setRecommenderId(-1L); // 无效的推荐方ID

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("无效推荐方ID测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试导游供应商列表查询 - 无效产线类型
     */
    @Test
    public void testGetGuideSupplierListWithInvalidProductionLine() {
        log.info("=== 测试导游供应商列表查询 - 无效产线类型 ===");

        // 构建测试数据
        RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
        queryDto.setRecommenderId(1L);
        queryDto.setProductionLine(999); // 无效的产线类型

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("无效产线类型测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试导游供应商列表查询 - 无效状态筛选
     */
    @Test
    public void testGetGuideSupplierListWithInvalidControlStatus() {
        log.info("=== 测试导游供应商列表查询 - 无效状态筛选 ===");

        // 构建测试数据
        RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
        queryDto.setRecommenderId(1L);
        queryDto.setControlStatus("invalid_status"); // 无效的状态

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("无效状态筛选测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试导游供应商列表查询 - 极大页码
     */
    @Test
    public void testGetGuideSupplierListWithLargePageNum() {
        log.info("=== 测试导游供应商列表查询 - 极大页码 ===");

        // 构建测试数据
        RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
        queryDto.setRecommenderId(1L);
        queryDto.setPageNum(999999); // 极大的页码

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("极大页码测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试导游供应商列表查询 - 组合筛选条件
     */
    @Test
    public void testGetGuideSupplierListWithCombinedFilters() {
        log.info("=== 测试导游供应商列表查询 - 组合筛选条件 ===");

        // 构建测试数据
        RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
        queryDto.setRecommenderId(1L);
        queryDto.setProductionLine(2); // 导游
        queryDto.setControlStatus("normal"); // 正常状态
        queryDto.setKeyword("张"); // 关键词搜索
        queryDto.setRelationStartDate(LocalDate.of(2025, 1, 1));
        queryDto.setRelationEndDate(LocalDate.of(2025, 6, 30));

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("组合筛选条件测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 性能测试 ====================

    /**
     * 测试酒店关系列表查询 - 性能测试
     */
    @Test
    public void testGetHotelRelationListPerformance() {
        log.info("=== 测试酒店关系列表查询 - 性能测试 ===");

        int testCount = 10;
        long totalTime = 0;

        for (int i = 0; i < testCount; i++) {
            // 构建测试数据
            RecommenderHotelRelationQueryDto queryDto = buildHotelQueryDto();
            queryDto.setUserId(1L);

            // 调用服务方法
            long startTime = System.currentTimeMillis();
            ResponseResult<RecommenderHotelRelationListVo> result = recommenderRelationService.getRecommenderHotelRelationList(queryDto);
            long endTime = System.currentTimeMillis();

            long executionTime = endTime - startTime;
            totalTime += executionTime;

            log.info("第{}次执行时间: {}ms", i + 1, executionTime);
        }

        long averageTime = totalTime / testCount;
        log.info("性能测试完成，平均执行时间: {}ms", averageTime);
    }

    /**
     * 测试导游供应商列表查询 - 性能测试
     */
    @Test
    public void testGetGuideSupplierListPerformance() {
        log.info("=== 测试导游供应商列表查询 - 性能测试 ===");

        int testCount = 10;
        long totalTime = 0;

        for (int i = 0; i < testCount; i++) {
            // 构建测试数据
            RecommenderGuideRelationQueryDto queryDto = buildGuideQueryDto();
            queryDto.setRecommenderId(1L);

            // 调用服务方法
            long startTime = System.currentTimeMillis();
            ResponseResult<RecommenderGuideRelationListVo> result = recommenderRelationService.getTourGuideSupplierList(queryDto);
            long endTime = System.currentTimeMillis();

            long executionTime = endTime - startTime;
            totalTime += executionTime;

            log.info("第{}次执行时间: {}ms", i + 1, executionTime);
        }

        long averageTime = totalTime / testCount;
        log.info("性能测试完成，平均执行时间: {}ms", averageTime);
    }

    // ==================== 辅助方法 ====================

    /**
     * 构建酒店关系查询DTO
     */
    private RecommenderHotelRelationQueryDto buildHotelQueryDto() {
        RecommenderHotelRelationQueryDto queryDto = new RecommenderHotelRelationQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(50);
        queryDto.setSortType("1"); // 默认排序
        return queryDto;
    }

    /**
     * 构建导游关系查询DTO
     */
    private RecommenderGuideRelationQueryDto buildGuideQueryDto() {
        RecommenderGuideRelationQueryDto queryDto = new RecommenderGuideRelationQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(50);
        queryDto.setSortType("1"); // 默认排序
        return queryDto;
    }
}
