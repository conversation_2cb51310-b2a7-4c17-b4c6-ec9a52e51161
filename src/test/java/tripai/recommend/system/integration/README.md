# 集成测试目录

## 目录说明

此目录包含所有的集成测试，主要特点：

- **真实数据库**：连接真实的测试数据库
- **完整流程测试**：测试完整的业务流程
- **数据一致性验证**：验证数据的正确性和一致性
- **性能测试**：验证系统在真实环境下的性能

## 测试类命名规范

- 集成测试类以 `Integration` 开头
- 例如：`IntegrationRecommenderOrderServiceTest.java`

## 数据库配置

集成测试使用独立的测试数据库配置：

```yaml
# application-test.yml
spring:
  datasource:
    url: *****************************************
    username: test_user
    password: test_password
  jpa:
    hibernate:
      ddl-auto: create-drop  # 测试结束后清理数据
```

## 运行方式

```bash
# 运行所有集成测试
mvn test -Dtest="tripai.recommend.system.integration.**" -Dspring.profiles.active=test

# 运行特定服务的集成测试
mvn test -Dtest="tripai.recommend.system.integration.IntegrationRecommenderOrderServiceTest" -Dspring.profiles.active=test
```

## 测试特点

1. **真实环境**：使用真实的数据库和外部服务
2. **数据准备**：每个测试前准备必要的测试数据
3. **数据清理**：每个测试后清理测试数据
4. **事务回滚**：使用 `@Transactional` 和 `@Rollback` 确保数据隔离
5. **端到端测试**：验证完整的业务流程
