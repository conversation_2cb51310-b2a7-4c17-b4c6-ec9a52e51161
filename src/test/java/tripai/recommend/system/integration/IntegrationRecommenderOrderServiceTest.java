package tripai.recommend.system.integration;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.dto.recommender.RecommenderOrderQueryDto;
import tripai.recommend.system.domain.entity.RecommenderInfo;
import tripai.recommend.system.domain.vo.recommender.RecommenderOrderListVo;
import tripai.recommend.system.mapper.recommender.RecommenderMapper;
import tripai.recommend.system.mapper.recommender.RecommenderOrderMapper;
import tripai.recommend.system.service.RecommenderOrderService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system.integration
 * @className: IntegrationRecommenderOrderServiceTest
 * @author: lijunqi
 * @description: RecommenderOrderService集成测试 - 使用真实数据库测试
 * @date: 2025/7/30 10:00
 * @version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("test") // 使用测试环境配置
@Transactional // 确保测试数据回滚
@Slf4j
public class IntegrationRecommenderOrderServiceTest {

    @Resource
    private RecommenderOrderService recommenderOrderService;

    @Resource
    private RecommenderMapper recommenderMapper;

    @Resource
    private RecommenderOrderMapper recommenderOrderMapper;

    private Long testUserId;
    private Long testRecommenderId;

    // ==================== 测试数据准备和清理 ====================

    /**
     * 每个测试前准备测试数据
     */
    @Before
    public void setUp() {
        log.info("=== 集成测试 - 准备测试数据 ===");
        
        // 创建测试推荐方数据
        RecommenderInfo testRecommender = createTestRecommender();
        testUserId = testRecommender.getUserId();
        testRecommenderId = testRecommender.getId();
        
        // 创建测试订单数据
        createTestOrders();
        
        log.info("测试数据准备完成 - userId: {}, recommenderId: {}", testUserId, testRecommenderId);
    }

    /**
     * 每个测试后清理测试数据
     */
    @After
    public void tearDown() {
        log.info("=== 集成测试 - 清理测试数据 ===");
        
        // 清理测试订单数据
        cleanTestOrders();
        
        // 清理测试推荐方数据
        cleanTestRecommender();
        
        log.info("测试数据清理完成");
    }

    // ==================== 集成测试用例 ====================

    /**
     * 集成测试 - 验证真实数据库环境下的基本功能
     */
    @Test
    public void test01_IntegrationBasicFunctionality() {
        log.info("=== 集成测试 - 基本功能验证 ===");

        // 构建查询条件
        RecommenderOrderQueryDto queryDto = buildRealQueryDto();
        
        log.info("查询条件: {}", JSON.toJSONString(queryDto));

        // 调用服务方法
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDto);

        log.info("查询结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getOrders()).isNotEmpty();
        assertThat(result.getData().getTotal()).isGreaterThan(0);

        log.info("✅ 集成测试基本功能验证通过");
    }

    /**
     * 集成测试 - 验证分页功能
     */
    @Test
    public void test02_IntegrationPaginationFunctionality() {
        log.info("=== 集成测试 - 分页功能验证 ===");

        // 测试第一页
        RecommenderOrderQueryDto queryDto1 = buildRealQueryDto();
        queryDto1.setPageNum(1);
        queryDto1.setPageSize(2);

        ResponseResult<RecommenderOrderListVo> result1 = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDto1);

        // 验证第一页结果
        assertThat(result1.getCode()).isEqualTo(200);
        assertThat(result1.getData().getPageNum()).isEqualTo(1);
        assertThat(result1.getData().getPageSize()).isEqualTo(2);

        log.info("第一页查询结果: 总数={}, 当前页数据量={}", 
                result1.getData().getTotal(), 
                result1.getData().getOrders().size());

        // 如果有多页数据，测试第二页
        if (result1.getData().getTotalPages() > 1) {
            RecommenderOrderQueryDto queryDto2 = buildRealQueryDto();
            queryDto2.setPageNum(2);
            queryDto2.setPageSize(2);

            ResponseResult<RecommenderOrderListVo> result2 = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDto2);

            assertThat(result2.getCode()).isEqualTo(200);
            assertThat(result2.getData().getPageNum()).isEqualTo(2);

            log.info("第二页查询结果: 当前页数据量={}", result2.getData().getOrders().size());
        }

        log.info("✅ 集成测试分页功能验证通过");
    }

    /**
     * 集成测试 - 验证订单类型筛选功能
     */
    @Test
    public void test03_IntegrationOrderTypeFilter() {
        log.info("=== 集成测试 - 订单类型筛选功能验证 ===");

        // 查询全部订单
        RecommenderOrderQueryDto queryDtoAll = buildRealQueryDto();
        queryDtoAll.setOrderType(null);
        ResponseResult<RecommenderOrderListVo> resultAll = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDtoAll);

        log.info("全部订单查询结果: 总数={}", resultAll.getData().getTotal());

        // 查询酒店订单
        RecommenderOrderQueryDto queryDtoHotel = buildRealQueryDto();
        queryDtoHotel.setOrderType(1);
        ResponseResult<RecommenderOrderListVo> resultHotel = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDtoHotel);

        log.info("酒店订单查询结果: 总数={}", resultHotel.getData().getTotal());

        // 查询导游订单
        RecommenderOrderQueryDto queryDtoTourGuide = buildRealQueryDto();
        queryDtoTourGuide.setOrderType(2);
        ResponseResult<RecommenderOrderListVo> resultTourGuide = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDtoTourGuide);

        log.info("导游订单查询结果: 总数={}", resultTourGuide.getData().getTotal());

        // 验证结果逻辑
        assertThat(resultAll.getCode()).isEqualTo(200);
        assertThat(resultHotel.getCode()).isEqualTo(200);
        assertThat(resultTourGuide.getCode()).isEqualTo(200);

        // 验证分类查询的总数不超过全部查询的总数
        long totalByType = resultHotel.getData().getTotal() + resultTourGuide.getData().getTotal();
        assertThat(totalByType).isLessThanOrEqualTo(resultAll.getData().getTotal());

        log.info("✅ 集成测试订单类型筛选功能验证通过");
    }

    /**
     * 集成测试 - 验证日期范围筛选功能
     */
    @Test
    public void test04_IntegrationDateRangeFilter() {
        log.info("=== 集成测试 - 日期范围筛选功能验证 ===");

        // 查询最近30天的订单
        RecommenderOrderQueryDto queryDto30Days = buildRealQueryDto();
        queryDto30Days.setSettlementStartDate(LocalDate.now().minusDays(30));
        queryDto30Days.setSettlementEndDate(LocalDate.now());

        ResponseResult<RecommenderOrderListVo> result30Days = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDto30Days);

        log.info("最近30天订单查询结果: 总数={}", result30Days.getData().getTotal());

        // 查询最近7天的订单
        RecommenderOrderQueryDto queryDto7Days = buildRealQueryDto();
        queryDto7Days.setSettlementStartDate(LocalDate.now().minusDays(7));
        queryDto7Days.setSettlementEndDate(LocalDate.now());

        ResponseResult<RecommenderOrderListVo> result7Days = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDto7Days);

        log.info("最近7天订单查询结果: 总数={}", result7Days.getData().getTotal());

        // 验证结果
        assertThat(result30Days.getCode()).isEqualTo(200);
        assertThat(result7Days.getCode()).isEqualTo(200);

        // 验证7天的数据量不超过30天的数据量
        assertThat(result7Days.getData().getTotal()).isLessThanOrEqualTo(result30Days.getData().getTotal());

        log.info("✅ 集成测试日期范围筛选功能验证通过");
    }

    /**
     * 集成测试 - 验证数据一致性
     */
    @Test
    public void test05_IntegrationDataConsistency() {
        log.info("=== 集成测试 - 数据一致性验证 ===");

        RecommenderOrderQueryDto queryDto = buildRealQueryDto();
        ResponseResult<RecommenderOrderListVo> result = recommenderOrderService.getRecommenderOrderListByUserId(testUserId, queryDto);

        // 验证数据一致性
        assertThat(result.getCode()).isEqualTo(200);
        
        RecommenderOrderListVo data = result.getData();
        
        // 验证分页信息一致性
        int expectedTotalPages = (int) Math.ceil((double) data.getTotal() / data.getPageSize());
        assertThat(data.getTotalPages()).isEqualTo(expectedTotalPages);
        
        // 验证订单数据完整性
        data.getOrders().forEach(order -> {
            assertThat(order.getOrderNo()).isNotNull();
            assertThat(order.getOrderType()).isNotNull();
            assertThat(order.getRecommenderId()).isEqualTo(testRecommenderId);
            assertThat(order.getCommissionAmount()).isNotNull();
            assertThat(order.getCommissionAmount()).isGreaterThanOrEqualTo(BigDecimal.ZERO);
        });

        log.info("✅ 集成测试数据一致性验证通过");
    }

    // ==================== 测试数据管理方法 ====================

    /**
     * 创建测试推荐方数据
     */
    private RecommenderInfo createTestRecommender() {
        RecommenderInfo recommender = new RecommenderInfo();
        recommender.setUserId(99999L); // 使用特殊的测试用户ID
        recommender.setName("集成测试推荐方");
        recommender.setType(1);
        recommender.setCreateTime(LocalDateTime.now());
        recommender.setUpdateTime(LocalDateTime.now());
        
        // 插入数据库
        recommenderMapper.insert(recommender);
        
        return recommender;
    }

    /**
     * 创建测试订单数据
     */
    private void createTestOrders() {
        // 这里应该调用相应的Mapper插入测试订单数据
        // 由于订单数据结构复杂，这里只是示例
        log.info("创建测试订单数据 - recommenderId: {}", testRecommenderId);
        
        // 实际实现中，您需要根据具体的订单表结构来插入测试数据
        // 例如：
        // TestOrderEntity order1 = new TestOrderEntity();
        // order1.setRecommenderId(testRecommenderId);
        // order1.setOrderType(1);
        // order1.setCommissionAmount(new BigDecimal("100.00"));
        // orderMapper.insert(order1);
    }

    /**
     * 清理测试推荐方数据
     */
    private void cleanTestRecommender() {
        if (testRecommenderId != null) {
            recommenderMapper.deleteById(testRecommenderId);
            log.info("清理测试推荐方数据 - recommenderId: {}", testRecommenderId);
        }
    }

    /**
     * 清理测试订单数据
     */
    private void cleanTestOrders() {
        // 这里应该清理相关的测试订单数据
        log.info("清理测试订单数据 - recommenderId: {}", testRecommenderId);
        
        // 实际实现中，您需要根据具体的订单表结构来清理测试数据
        // 例如：
        // orderMapper.deleteByRecommenderId(testRecommenderId);
    }

    /**
     * 构建真实查询DTO
     */
    private RecommenderOrderQueryDto buildRealQueryDto() {
        RecommenderOrderQueryDto queryDto = new RecommenderOrderQueryDto();
        queryDto.setRecommenderId(testRecommenderId);
        queryDto.setOrderType(null); // 默认查询全部
        queryDto.setKeyword(null);
        queryDto.setSettlementStartDate(LocalDate.now().minusDays(30));
        queryDto.setSettlementEndDate(LocalDate.now());
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        return queryDto;
    }
}
