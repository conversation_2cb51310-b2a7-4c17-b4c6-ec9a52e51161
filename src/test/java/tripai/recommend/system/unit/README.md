# 单元测试目录

## 目录说明

此目录包含所有的单元测试，主要特点：

- **使用Mock数据**：不依赖真实数据库
- **分支覆盖率测试**：专注于代码逻辑分支的完整覆盖
- **快速执行**：测试执行速度快，适合频繁运行
- **隔离性强**：测试之间相互独立，不会相互影响

## 测试类命名规范

- 单元测试类以 `Unit` 开头
- 例如：`UnitRecommenderOrderServiceTest.java`

## 运行方式

```bash
# 运行所有单元测试
mvn test -Dtest="tripai.recommend.system.unit.**"

# 运行特定服务的单元测试
mvn test -Dtest="tripai.recommend.system.unit.UnitRecommenderOrderServiceTest"

# 生成覆盖率报告
mvn clean test jacoco:report -Dtest="tripai.recommend.system.unit.**"
```

## 测试特点

1. **Mock策略**：使用 `@MockBean` 模拟所有外部依赖
2. **分支覆盖**：确保每个方法的所有分支都被测试
3. **边界测试**：测试各种边界条件和异常情况
4. **参数验证**：验证方法参数的各种组合
