package tripai.recommend.system;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.config.AuthDefaultSource;
import me.zhyd.oauth.model.AuthUser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tripai.recommend.system.domain.ResponseResult;
import tripai.recommend.system.domain.vo.AuthCallBackVo;
import tripai.recommend.system.domain.vo.user.*;
import tripai.recommend.system.service.UserInfoService;

import static org.assertj.core.api.Assertions.*;

/**
 * @projectName: somytrip-recommend-system
 * @package: tripai.recommend.system
 * @className: TestUserInfoService
 * @author: lijunqi
 * @description: UserInfoService测试类（不包含getUserProfile相关测试）
 * @date: 2025/7/28 18:30
 * @version: 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TestUserInfoService {

    @Resource
    private UserInfoService userInfoService;

    // ==================== 邮箱相关测试 ====================

    /**
     * 测试发送绑定邮箱验证码 - 正常流程
     */
    @Test
    public void testSendBoundEmailCodeSuccess() {
        log.info("=== 测试发送绑定邮箱验证码 - 正常流程 ===");

        String email = "<EMAIL>";

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.sendBoundEmailCode(email);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(200);
    }

    /**
     * 测试发送绑定邮箱验证码 - 无效邮箱
     */
    @Test
    public void testSendBoundEmailCodeWithInvalidEmail() {
        log.info("=== 测试发送绑定邮箱验证码 - 无效邮箱 ===");

        String email = "invalid-email";

        // 调用服务方法
        ResponseResult<Boolean> result = userInfoService.sendBoundEmailCode(email);

        log.info("无效邮箱测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试修改绑定邮箱 - 正常流程
     */
    @Test
    public void testEditEmailSuccess() {
        log.info("=== 测试修改绑定邮箱 - 正常流程 ===");

        // 构建测试数据
        EditUserEmailVo vo = buildEditUserEmailVo();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.editEmail(vo, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 短信验证码相关测试 ====================

    /**
     * 测试发送登录注册短信 - 正常流程
     */
    @Test
    public void testSendLoginSmsSuccess() {
        log.info("=== 测试发送登录注册短信 - 正常流程 ===");

        // 构建测试数据
        CaptchaCodeVo vo = buildCaptchaCodeVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.sendLoginSms(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试发送重置密码短信 - 正常流程
     */
    @Test
    public void testSendResetPasswordSmsSuccess() {
        log.info("=== 测试发送重置密码短信 - 正常流程 ===");

        // 构建测试数据
        CaptchaCodeVo vo = buildCaptchaCodeVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.sendResetPasswordSms(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 登录相关测试 ====================

    /**
     * 测试渠道登录 - 正常流程
     */
    @Test
    public void testChannelLoginSuccess() {
        log.info("=== 测试渠道登录 - 正常流程 ===");

        // 构建测试数据
        AuthUser authUser = buildAuthUser();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.login(authUser);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试手机号登录 - 正常流程
     */
    @Test
    public void testMobileLoginSuccess() {
        log.info("=== 测试手机号登录 - 正常流程 ===");

        // 构建测试数据
        MobileLoginVo vo = buildMobileLoginVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.login(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试用户名密码登录 - 正常流程
     */
    @Test
    public void testUsernameLoginSuccess() {
        log.info("=== 测试用户名密码登录 - 正常流程 ===");

        // 构建测试数据
        UsernameLoginVo vo = buildUsernameLoginVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.login(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 注册相关测试 ====================

    /**
     * 测试扫码注册 - 正常流程
     */
    @Test
    public void testScanQrRegisterSuccess() {
        log.info("=== 测试扫码注册 - 正常流程 ===");

        // 构建测试数据
        ScanQrRegisterVo vo = buildScanQrRegisterVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.register(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试手机号注册 - 正常流程
     */
    @Test
    public void testMobileRegisterSuccess() {
        log.info("=== 测试手机号注册 - 正常流程 ===");

        // 构建测试数据
        MobileRegisterVo vo = buildMobileRegisterVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<LoginResultVo> result = userInfoService.register(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试手机号注册 - 密码不一致
     */
    @Test
    public void testMobileRegisterWithPasswordMismatch() {
        log.info("=== 测试手机号注册 - 密码不一致 ===");

        // 构建测试数据
        MobileRegisterVo vo = buildMobileRegisterVo();
        vo.setPassword("password123");
        vo.setConfirmPassword("password456"); // 不一致的密码

        // 调用服务方法
        ResponseResult<LoginResultVo> result = userInfoService.register(vo);

        log.info("密码不一致测试结果: {}", JSON.toJSONString(result));

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isNotEqualTo(200);
    }

    // ==================== 密码重置测试 ====================

    /**
     * 测试重设密码 - 正常流程
     */
    @Test
    public void testResetPasswordSuccess() {
        log.info("=== 测试重设密码 - 正常流程 ===");

        // 构建测试数据
        MobileRegisterVo vo = buildMobileRegisterVo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.resetPassword(vo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 用户信息修改测试 ====================

    /**
     * 测试修改昵称 - 正常流程
     */
    @Test
    public void testEditNicknameSuccess() {
        log.info("=== 测试修改昵称 - 正常流程 ===");

        String newNickname = "新昵称" + System.currentTimeMillis();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.editNickname(newNickname, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试修改手机号 - 正常流程
     */
    @Test
    public void testEditMobileSuccess() {
        log.info("=== 测试修改手机号 - 正常流程 ===");

        // 构建测试数据
        MobileLoginVo vo = buildMobileLoginVo();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.editMobile(vo, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试修改头像 - 正常流程
     */
    @Test
    public void testEditHeadImageSuccess() {
        log.info("=== 测试修改头像 - 正常流程 ===");

        // 构建测试数据
        EditUserHeadImageVo vo = buildEditUserHeadImageVo();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<String> result = userInfoService.editHeadImage(vo, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 绑定/解绑测试 ====================

    /**
     * 测试用户绑定渠道 - 正常流程
     */
    @Test
    public void testBoundUserSuccess() {
        log.info("=== 测试用户绑定渠道 - 正常流程 ===");

        // 构建测试数据
        AuthUser authUser = buildAuthUser();
        String token = buildValidToken();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.boundUser(authUser, token);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试解绑邮箱 - 正常流程
     */
    @Test
    public void testUnbindEmailSuccess() {
        log.info("=== 测试解绑邮箱 - 正常流程 ===");

        String token = buildValidToken();
        int op = 0; // 0 = 邮箱

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.unbindEmailOrQrChannel(token, op);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试解绑微信渠道 - 正常流程
     */
    @Test
    public void testUnbindWechatChannelSuccess() {
        log.info("=== 测试解绑微信渠道 - 正常流程 ===");

        String token = buildValidToken();
        int op = 1; // 1 = 微信渠道

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        ResponseResult<Boolean> result = userInfoService.unbindEmailOrQrChannel(token, op);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("测试结果: {}", JSON.toJSONString(result));
    }

    // ==================== 加密解密测试 ====================

    /**
     * 测试手机号AES加密 - 正常流程
     */
    @Test
    public void testMobileAesEncryptSuccess() {
        log.info("=== 测试手机号AES加密 - 正常流程 ===");

        // 构建测试数据
        MobileInfo mobileInfo = buildMobileInfo();

        // 调用服务方法
        long startTime = System.currentTimeMillis();
        String encryptedMobile = userInfoService.mobileAesEncryptPublic(mobileInfo);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("原始手机号: {}", JSON.toJSONString(mobileInfo));
        log.info("加密后手机号: {}", encryptedMobile);

        // 验证结果
        assertThat(encryptedMobile).isNotNull();
        assertThat(encryptedMobile).isNotEmpty();
    }

    /**
     * 测试手机号AES解密 - 正常流程
     */
    @Test
    public void testMobileAesDecryptSuccess() {
        log.info("=== 测试手机号AES解密 - 正常流程 ===");

        // 先加密一个手机号
        MobileInfo originalMobileInfo = buildMobileInfo();
        String encryptedMobile = userInfoService.mobileAesEncryptPublic(originalMobileInfo);

        // 调用解密服务方法
        long startTime = System.currentTimeMillis();
        MobileInfo decryptedMobileInfo = userInfoService.mobileAesDecryptPublic(encryptedMobile);
        long endTime = System.currentTimeMillis();

        log.info("执行时间: {}ms", endTime - startTime);
        log.info("加密手机号: {}", encryptedMobile);
        log.info("解密后手机号: {}", JSON.toJSONString(decryptedMobileInfo));

        // 验证结果
        assertThat(decryptedMobileInfo).isNotNull();
        assertThat(decryptedMobileInfo.getMobile()).isEqualTo(originalMobileInfo.getMobile());
        assertThat(decryptedMobileInfo.getCountryCode()).isEqualTo(originalMobileInfo.getCountryCode());
    }

    /**
     * 测试手机号加密解密 - 完整流程
     */
    @Test
    public void testMobileEncryptDecryptFullFlow() {
        log.info("=== 测试手机号加密解密 - 完整流程 ===");

        // 构建测试数据
        MobileInfo originalMobileInfo = buildMobileInfo();
        log.info("原始手机号信息: {}", JSON.toJSONString(originalMobileInfo));

        // 加密
        String encryptedMobile = userInfoService.mobileAesEncryptPublic(originalMobileInfo);
        log.info("加密后: {}", encryptedMobile);
        assertThat(encryptedMobile).isNotNull();
        assertThat(encryptedMobile).isNotEmpty();

        // 解密
        MobileInfo decryptedMobileInfo = userInfoService.mobileAesDecryptPublic(encryptedMobile);
        log.info("解密后手机号信息: {}", JSON.toJSONString(decryptedMobileInfo));

        // 验证加密解密的一致性
        assertThat(decryptedMobileInfo).isNotNull();
        assertThat(decryptedMobileInfo.getMobile()).isEqualTo(originalMobileInfo.getMobile());
        assertThat(decryptedMobileInfo.getCountryCode()).isEqualTo(originalMobileInfo.getCountryCode());

        log.info("手机号加密解密测试通过");
    }

    // ==================== 测试辅助方法 ====================

    /**
     * 构建EditUserEmailVo测试数据
     */
    private EditUserEmailVo buildEditUserEmailVo() {
        EditUserEmailVo vo = new EditUserEmailVo();
        vo.setEmail("<EMAIL>");
        vo.setVerificationCode("123456");
        return vo;
    }

    /**
     * 构建CaptchaCodeVo测试数据
     */
    private CaptchaCodeVo buildCaptchaCodeVo() {
        CaptchaCodeVo vo = new CaptchaCodeVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setCaptchaInfo(buildCaptchaVo());
        return vo;
    }

    /**
     * 构建CaptchaVo测试数据
     */
    private CaptchaVo buildCaptchaVo() {
        CaptchaVo vo = new CaptchaVo();
        vo.setSign("test-sign-" + System.currentTimeMillis());
        vo.setCaptcha("1234");
        return vo;
    }

    /**
     * 构建MobileInfo测试数据
     */
    private MobileInfo buildMobileInfo() {
        MobileInfo mobileInfo = new MobileInfo();
        mobileInfo.setCountryCode("86");
        mobileInfo.setMobile("13800138000");
        return mobileInfo;
    }

    /**
     * 构建AuthUser测试数据
     */
    private AuthUser buildAuthUser() {
        AuthUser authUser = new AuthUser();
        authUser.setUuid("test-uuid-" + System.currentTimeMillis());
        authUser.setUsername("testuser");
        authUser.setNickname("测试用户");
        authUser.setAvatar("https://example.com/avatar.jpg");
        authUser.setEmail("<EMAIL>");
        authUser.setSource(AuthDefaultSource.WECHAT_OPEN.toString());
        return authUser;
    }

    /**
     * 构建MobileLoginVo测试数据
     */
    private MobileLoginVo buildMobileLoginVo() {
        MobileLoginVo vo = new MobileLoginVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setVerificationCode("123456");
        return vo;
    }

    /**
     * 构建UsernameLoginVo测试数据
     */
    private UsernameLoginVo buildUsernameLoginVo() {
        UsernameLoginVo vo = new UsernameLoginVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setPassword("password123");
        return vo;
    }

    /**
     * 构建ScanQrRegisterVo测试数据
     */
    private ScanQrRegisterVo buildScanQrRegisterVo() {
        ScanQrRegisterVo vo = new ScanQrRegisterVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setVerificationCode("123456");
        vo.setAuthCallBackValue(buildAuthCallBackVo());
        vo.setSource("wechat");
        return vo;
    }

    /**
     * 构建AuthCallBackVo测试数据
     */
    private AuthCallBackVo buildAuthCallBackVo() {
        AuthCallBackVo vo = new AuthCallBackVo();
        // 根据实际的AuthCallBackVo字段设置测试数据
        return vo;
    }

    /**
     * 构建MobileRegisterVo测试数据
     */
    private MobileRegisterVo buildMobileRegisterVo() {
        MobileRegisterVo vo = new MobileRegisterVo();
        vo.setMobileInfo(buildMobileInfo());
        vo.setVerificationCode("123456");
        vo.setPassword("password123");
        vo.setConfirmPassword("password123");
        return vo;
    }

    /**
     * 构建EditUserHeadImageVo测试数据
     */
    private EditUserHeadImageVo buildEditUserHeadImageVo() {
        EditUserHeadImageVo vo = new EditUserHeadImageVo();
        // 注意：这里需要MockMultipartFile，在实际测试中可能需要mock
        // vo.setImage(mockMultipartFile);
        return vo;
    }

    /**
     * 构建有效的token（用于测试）
     */
    private String buildValidToken() {
        // 这里应该返回一个有效的JWT token
        // 在实际测试中，可能需要使用测试用的token或者mock JwtUtils
        return "test-token-" + System.currentTimeMillis();
    }
}
