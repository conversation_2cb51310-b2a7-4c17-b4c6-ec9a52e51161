# 集成测试专用配置文件
spring:
  profiles:
    active: test
  
  # 测试数据库配置
  datasource:
    url: ******************************************************************************************************************************************************
    username: ${DB_USERNAME:test_user}
    username: ${DB_PASSWORD:test_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 10
      auto-commit: true
      idle-timeout: 30000
      pool-name: TestHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 测试结束后自动清理表结构
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect

  # 事务配置
  transaction:
    rollback-on-commit-failure: true

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 打印SQL日志
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    tripai.recommend.system: DEBUG
    org.springframework.transaction: DEBUG
    org.springframework.orm.jpa: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [%t] %-40.40logger{39} : %m%n"

# 测试专用配置
test:
  # 测试数据配置
  data:
    cleanup-enabled: true  # 是否启用测试数据清理
    auto-rollback: true    # 是否自动回滚事务
  
  # 性能测试配置
  performance:
    timeout: 5000  # 测试超时时间（毫秒）
    max-execution-time: 1000  # 最大执行时间（毫秒）
