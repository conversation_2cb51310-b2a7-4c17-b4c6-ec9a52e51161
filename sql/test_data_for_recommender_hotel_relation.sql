-- =====================================================
-- 测试数据SQL：RecommenderRelationService.getRecommenderHotelRelationList()
-- 用于测试方法：testGetRecommenderHotelRelationListSuccess
-- 创建时间：2025-07-28
-- =====================================================

-- 清理现有测试数据（可选，谨慎使用）
-- DELETE FROM recommender_relation WHERE recommender_id IN (1, 2);
-- DELETE FROM recommender_info WHERE id IN (1, 2);
-- DELETE FROM recommender_user_info WHERE id IN (1, 2, 3, 4);
-- DELETE FROM hotel_info WHERE id IN (1, 2, 3, 4, 5);
-- DELETE FROM hotel_rooms WHERE hotel_id IN (1, 2, 3, 4, 5);
-- DELETE FROM manager_order WHERE hotel_id IN (1, 2, 3, 4, 5);

-- =====================================================
-- 1. 用户信息表数据 (recommender_user_info)
-- =====================================================

INSERT INTO `recommender_user_info` (`id`, `mobile`, `email`, `recommender_code`, `nick_name`, `gender`, `password`, `head_url`, `create_time`, `update_time`, `role_type`) VALUES
(1, '13800138001', '<EMAIL>', 'REC001', '测试推荐方1', 'M', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqJyuVw6CmpkjzMBCOigSXy', 'https://example.com/avatar1.jpg', '2025-01-15 10:00:00', '2025-01-15 10:00:00', 'USER'),
(2, '13800138002', '<EMAIL>', 'REC002', '测试推荐方2', 'F', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqJyuVw6CmpkjzMBCOigSXy', 'https://example.com/avatar2.jpg', '2025-01-16 10:00:00', '2025-01-16 10:00:00', 'USER'),
(3, '13800138003', '<EMAIL>', 'SUP001', '酒店供应商1', 'M', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqJyuVw6CmpkjzMBCOigSXy', 'https://example.com/avatar3.jpg', '2025-01-10 10:00:00', '2025-01-10 10:00:00', 'USER'),
(4, '13800138004', '<EMAIL>', 'SUP002', '酒店供应商2', 'F', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqJyuVw6CmpkjzMBCOigSXy', 'https://example.com/avatar4.jpg', '2025-01-11 10:00:00', '2025-01-11 10:00:00', 'USER');

-- =====================================================
-- 2. 推荐方信息表数据 (recommender_info)
-- =====================================================

INSERT INTO `recommender_info` (`id`, `invitation_code`, `user_id`, `type`, `name`, `certificate_type`, `identifier`, `status`, `avatar_url`, `create_time`, `update_time`, `audit_submit_time`, `is_draft`, `id_card_front_url`, `id_card_back_url`, `business_license_url`, `is_del`) VALUES
(1, 'INV001234567890A', 1, 1, '张三', 1, '110101199001011234', 1, '{"url":"https://example.com/avatar1.jpg","name":"avatar1.jpg","size":102400}', '2025-01-15 10:30:00', '2025-01-15 10:30:00', '2025-01-15 11:00:00', 0, '{"url":"https://example.com/id_front1.jpg","name":"id_front1.jpg","size":204800}', '{"url":"https://example.com/id_back1.jpg","name":"id_back1.jpg","size":204800}', NULL, 0),
(2, 'INV001234567890B', 2, 2, '测试企业有限公司', NULL, '91110000123456789X', 1, '{"url":"https://example.com/avatar2.jpg","name":"avatar2.jpg","size":102400}', '2025-01-16 10:30:00', '2025-01-16 10:30:00', '2025-01-16 11:00:00', 0, NULL, NULL, '{"url":"https://example.com/license1.jpg","name":"license1.jpg","size":307200}', 0);

-- =====================================================
-- 3. 酒店信息表数据 (hotel_info)
-- =====================================================

INSERT INTO `hotel_info` (`id`, `hotel_name`, `hotel_supplier_code`, `hotel_status`, `address`, `star_rate`, `score`, `city_name`, `create_time`, `update_time`, `is_del`) VALUES
(1, '北京王府井大酒店', 'SUP001', 4, '北京市东城区王府井大街100号', 5, '4.8', '北京市', '2025-01-20 09:00:00', '2025-01-20 09:00:00', 0),
(2, '上海外滩精品酒店', 'SUP001', 4, '上海市黄浦区外滩18号', 4, '4.6', '上海市', '2025-01-21 09:00:00', '2025-01-21 09:00:00', 0),
(3, '广州珠江新城商务酒店', 'SUP002', 4, '广州市天河区珠江新城CBD核心区', 4, '4.5', '广州市', '2025-01-22 09:00:00', '2025-01-22 09:00:00', 0),
(4, '深圳南山科技园酒店', 'SUP002', 3, '深圳市南山区科技园南区', 3, '4.2', '深圳市', '2025-01-23 09:00:00', '2025-01-23 09:00:00', 0),
(5, '杭州西湖度假村', 'SUP001', 4, '杭州市西湖区西湖风景名胜区', 5, '4.9', '杭州市', '2025-01-24 09:00:00', '2025-01-24 09:00:00', 0);

-- =====================================================
-- 4. 推荐方关系表数据 (recommender_relation)
-- =====================================================

INSERT INTO `recommender_relation` (`id`, `recommender_id`, `biz_type`, `biz_id`, `user_id`, `relation_status`, `create_time`, `order_no`, `is_del`) VALUES
(1, 1, 1, 1, 1, 1, '2025-01-25 10:00:00', 'REL202501250001', 0),
(2, 1, 1, 2, 1, 1, '2025-01-26 10:00:00', 'REL202501260001', 0),
(3, 1, 1, 3, 1, 1, '2025-01-27 10:00:00', 'REL202501270001', 0),
(4, 1, 1, 4, 1, 2, '2025-01-28 10:00:00', 'REL202501280001', 0),
(5, 1, 1, 5, 1, 1, '2025-01-29 10:00:00', 'REL202501290001', 0),
(6, 2, 1, 1, 2, 1, '2025-01-30 10:00:00', 'REL202501300001', 0),
(7, 2, 1, 2, 2, 1, '2025-01-31 10:00:00', 'REL202501310001', 0);

-- =====================================================
-- 5. 酒店房型表数据 (hotel_rooms) - 用于统计上线房型数
-- =====================================================

INSERT INTO `hotel_rooms` (`id`, `hotel_id`, `room_type`, `room_name`, `room_status`, `price`, `create_time`, `update_time`, `del_flag`) VALUES
-- 北京王府井大酒店 (hotel_id=1) - 8个房型，6个上线
(1, 1, 'STANDARD', '标准双人间', 3, 388.00, '2025-01-20 10:00:00', '2025-01-20 10:00:00', 0),
(2, 1, 'DELUXE', '豪华大床房', 3, 588.00, '2025-01-20 10:00:00', '2025-01-20 10:00:00', 0),
(3, 1, 'SUITE', '商务套房', 3, 888.00, '2025-01-20 10:00:00', '2025-01-20 10:00:00', 0),
(4, 1, 'EXECUTIVE', '行政房', 3, 688.00, '2025-01-20 10:00:00', '2025-01-20 10:00:00', 0),
(5, 1, 'FAMILY', '家庭房', 3, 788.00, '2025-01-20 10:00:00', '2025-01-20 10:00:00', 0),
(6, 1, 'PRESIDENTIAL', '总统套房', 3, 1888.00, '2025-01-20 10:00:00', '2025-01-20 10:00:00', 0),
(7, 1, 'SINGLE', '单人间', 2, 288.00, '2025-01-20 10:00:00', '2025-01-20 10:00:00', 0),
(8, 1, 'TWIN', '双床房', 1, 388.00, '2025-01-20 10:00:00', '2025-01-20 10:00:00', 0),

-- 上海外滩精品酒店 (hotel_id=2) - 5个房型，4个上线
(9, 2, 'STANDARD', '江景标准间', 3, 688.00, '2025-01-21 10:00:00', '2025-01-21 10:00:00', 0),
(10, 2, 'DELUXE', '外滩豪华房', 3, 988.00, '2025-01-21 10:00:00', '2025-01-21 10:00:00', 0),
(11, 2, 'SUITE', '外滩套房', 3, 1588.00, '2025-01-21 10:00:00', '2025-01-21 10:00:00', 0),
(12, 2, 'EXECUTIVE', '行政江景房', 3, 1288.00, '2025-01-21 10:00:00', '2025-01-21 10:00:00', 0),
(13, 2, 'FAMILY', '家庭江景房', 2, 1088.00, '2025-01-21 10:00:00', '2025-01-21 10:00:00', 0),

-- 广州珠江新城商务酒店 (hotel_id=3) - 4个房型，3个上线
(14, 3, 'STANDARD', '商务标准间', 3, 468.00, '2025-01-22 10:00:00', '2025-01-22 10:00:00', 0),
(15, 3, 'DELUXE', '商务豪华房', 3, 668.00, '2025-01-22 10:00:00', '2025-01-22 10:00:00', 0),
(16, 3, 'SUITE', '商务套房', 3, 968.00, '2025-01-22 10:00:00', '2025-01-22 10:00:00', 0),
(17, 3, 'EXECUTIVE', '行政商务房', 1, 768.00, '2025-01-22 10:00:00', '2025-01-22 10:00:00', 0),

-- 深圳南山科技园酒店 (hotel_id=4) - 3个房型，2个上线
(18, 4, 'STANDARD', '科技园标准间', 3, 368.00, '2025-01-23 10:00:00', '2025-01-23 10:00:00', 0),
(19, 4, 'DELUXE', '科技园豪华房', 3, 568.00, '2025-01-23 10:00:00', '2025-01-23 10:00:00', 0),
(20, 4, 'SUITE', '科技园套房', 2, 768.00, '2025-01-23 10:00:00', '2025-01-23 10:00:00', 0),

-- 杭州西湖度假村 (hotel_id=5) - 6个房型，5个上线
(21, 5, 'STANDARD', '湖景标准间', 3, 588.00, '2025-01-24 10:00:00', '2025-01-24 10:00:00', 0),
(22, 5, 'DELUXE', '湖景豪华房', 3, 788.00, '2025-01-24 10:00:00', '2025-01-24 10:00:00', 0),
(23, 5, 'SUITE', '湖景套房', 3, 1288.00, '2025-01-24 10:00:00', '2025-01-24 10:00:00', 0),
(24, 5, 'VILLA', '独栋别墅', 3, 2888.00, '2025-01-24 10:00:00', '2025-01-24 10:00:00', 0),
(25, 5, 'FAMILY', '家庭湖景房', 3, 988.00, '2025-01-24 10:00:00', '2025-01-24 10:00:00', 0),
(26, 5, 'PRESIDENTIAL', '总统湖景套房', 1, 3888.00, '2025-01-24 10:00:00', '2025-01-24 10:00:00', 0);

-- =====================================================
-- 6. 订单相关表数据 - 用于统计成单数
-- =====================================================

-- 6.1 管理订单表 (manager_order)
INSERT INTO `manager_order` (`id`, `order_no`, `hotel_id`, `room_id`, `order_state`, `total_amount`, `commission_amount`, `create_time`, `update_time`) VALUES
-- 北京王府井大酒店的订单 (hotel_id=1) - 5个已完成订单
(1, 'MO202501250001', 1, 1, 'COMPLETED', 776.00, 77.60, '2025-01-25 15:00:00', '2025-01-26 10:00:00'),
(2, 'MO202501260001', 1, 2, 'COMPLETED', 1176.00, 117.60, '2025-01-26 15:00:00', '2025-01-27 10:00:00'),
(3, 'MO202501270001', 1, 3, 'COMPLETED', 1776.00, 177.60, '2025-01-27 15:00:00', '2025-01-28 10:00:00'),
(4, 'MO202501280001', 1, 4, 'COMPLETED', 1376.00, 137.60, '2025-01-28 15:00:00', '2025-01-29 10:00:00'),
(5, 'MO202501290001', 1, 5, 'COMPLETED', 1576.00, 157.60, '2025-01-29 15:00:00', '2025-01-30 10:00:00'),

-- 上海外滩精品酒店的订单 (hotel_id=2) - 3个已完成订单
(6, 'MO202501300001', 2, 9, 'COMPLETED', 1376.00, 137.60, '2025-01-30 15:00:00', '2025-01-31 10:00:00'),
(7, 'MO202501310001', 2, 10, 'COMPLETED', 1976.00, 197.60, '2025-01-31 15:00:00', '2025-02-01 10:00:00'),
(8, 'MO202502010001', 2, 11, 'COMPLETED', 3176.00, 317.60, '2025-02-01 15:00:00', '2025-02-02 10:00:00'),

-- 广州珠江新城商务酒店的订单 (hotel_id=3) - 2个已完成订单
(9, 'MO202502020001', 3, 14, 'COMPLETED', 936.00, 93.60, '2025-02-02 15:00:00', '2025-02-03 10:00:00'),
(10, 'MO202502030001', 3, 15, 'COMPLETED', 1336.00, 133.60, '2025-02-03 15:00:00', '2025-02-04 10:00:00'),

-- 深圳南山科技园酒店的订单 (hotel_id=4) - 1个已完成订单
(11, 'MO202502040001', 4, 18, 'COMPLETED', 736.00, 73.60, '2025-02-04 15:00:00', '2025-02-05 10:00:00'),

-- 杭州西湖度假村的订单 (hotel_id=5) - 4个已完成订单
(12, 'MO202502050001', 5, 21, 'COMPLETED', 1176.00, 117.60, '2025-02-05 15:00:00', '2025-02-06 10:00:00'),
(13, 'MO202502060001', 5, 22, 'COMPLETED', 1576.00, 157.60, '2025-02-06 15:00:00', '2025-02-07 10:00:00'),
(14, 'MO202502070001', 5, 23, 'COMPLETED', 2576.00, 257.60, '2025-02-07 15:00:00', '2025-02-08 10:00:00'),
(15, 'MO202502080001', 5, 24, 'COMPLETED', 5776.00, 577.60, '2025-02-08 15:00:00', '2025-02-09 10:00:00');

-- 6.2 支付订单表 (pay_order)
INSERT INTO `pay_order` (`id`, `order_no`, `pay_status`, `pay_amount`, `pay_time`, `create_time`, `update_time`) VALUES
(1, 'MO202501250001', 'PAID', 776.00, '2025-01-25 15:30:00', '2025-01-25 15:00:00', '2025-01-25 15:30:00'),
(2, 'MO202501260001', 'PAID', 1176.00, '2025-01-26 15:30:00', '2025-01-26 15:00:00', '2025-01-26 15:30:00'),
(3, 'MO202501270001', 'PAID', 1776.00, '2025-01-27 15:30:00', '2025-01-27 15:00:00', '2025-01-27 15:30:00'),
(4, 'MO202501280001', 'PAID', 1376.00, '2025-01-28 15:30:00', '2025-01-28 15:00:00', '2025-01-28 15:30:00'),
(5, 'MO202501290001', 'PAID', 1576.00, '2025-01-29 15:30:00', '2025-01-29 15:00:00', '2025-01-29 15:30:00'),
(6, 'MO202501300001', 'PAID', 1376.00, '2025-01-30 15:30:00', '2025-01-30 15:00:00', '2025-01-30 15:30:00'),
(7, 'MO202501310001', 'PAID', 1976.00, '2025-01-31 15:30:00', '2025-01-31 15:00:00', '2025-01-31 15:30:00'),
(8, 'MO202502010001', 'PAID', 3176.00, '2025-02-01 15:30:00', '2025-02-01 15:00:00', '2025-02-01 15:30:00'),
(9, 'MO202502020001', 'PAID', 936.00, '2025-02-02 15:30:00', '2025-02-02 15:00:00', '2025-02-02 15:30:00'),
(10, 'MO202502030001', 'PAID', 1336.00, '2025-02-03 15:30:00', '2025-02-03 15:00:00', '2025-02-03 15:30:00'),
(11, 'MO202502040001', 'PAID', 736.00, '2025-02-04 15:30:00', '2025-02-04 15:00:00', '2025-02-04 15:30:00'),
(12, 'MO202502050001', 'PAID', 1176.00, '2025-02-05 15:30:00', '2025-02-05 15:00:00', '2025-02-05 15:30:00'),
(13, 'MO202502060001', 'PAID', 1576.00, '2025-02-06 15:30:00', '2025-02-06 15:00:00', '2025-02-06 15:30:00'),
(14, 'MO202502070001', 'PAID', 2576.00, '2025-02-07 15:30:00', '2025-02-07 15:00:00', '2025-02-07 15:30:00'),
(15, 'MO202502080001', 'PAID', 5776.00, '2025-02-08 15:30:00', '2025-02-08 15:00:00', '2025-02-08 15:30:00');

-- 6.3 供应商操作订单日志表 (supplier_operation_order_log)
INSERT INTO `supplier_operation_order_log` (`id`, `order_id`, `action_type`, `action_desc`, `operator_id`, `create_time`) VALUES
(1, 1, 1, '订单确认', 3, '2025-01-26 09:00:00'),
(2, 2, 1, '订单确认', 3, '2025-01-27 09:00:00'),
(3, 3, 1, '订单确认', 3, '2025-01-28 09:00:00'),
(4, 4, 1, '订单确认', 3, '2025-01-29 09:00:00'),
(5, 5, 1, '订单确认', 3, '2025-01-30 09:00:00'),
(6, 6, 1, '订单确认', 3, '2025-01-31 09:00:00'),
(7, 7, 1, '订单确认', 3, '2025-02-01 09:00:00'),
(8, 8, 1, '订单确认', 3, '2025-02-02 09:00:00'),
(9, 9, 1, '订单确认', 4, '2025-02-03 09:00:00'),
(10, 10, 1, '订单确认', 4, '2025-02-04 09:00:00'),
(11, 11, 1, '订单确认', 4, '2025-02-05 09:00:00'),
(12, 12, 1, '订单确认', 3, '2025-02-06 09:00:00'),
(13, 13, 1, '订单确认', 3, '2025-02-07 09:00:00'),
(14, 14, 1, '订单确认', 3, '2025-02-08 09:00:00'),
(15, 15, 1, '订单确认', 3, '2025-02-09 09:00:00');

-- =====================================================
-- 数据验证查询（可选执行）
-- =====================================================

-- 验证用户ID=1对应的推荐方信息
-- SELECT ri.id as recommender_id, ri.name, ui.nick_name, ui.mobile 
-- FROM recommender_info ri 
-- JOIN recommender_user_info ui ON ri.user_id = ui.id 
-- WHERE ui.id = 1;

-- 验证推荐方ID=1的酒店关系数据
-- SELECT COUNT(*) as relation_count FROM recommender_relation WHERE recommender_id = 1 AND biz_type = 1 AND is_del = 0;

-- 验证各酒店的上线房型数统计
-- SELECT hotel_id, COUNT(*) as online_room_count 
-- FROM hotel_rooms 
-- WHERE room_status = 3 AND del_flag = 0 
-- GROUP BY hotel_id;

-- 验证各酒店的成单数统计
-- SELECT mo.hotel_id, COUNT(*) as order_count 
-- FROM manager_order mo 
-- INNER JOIN pay_order po ON mo.order_no = po.order_no 
-- INNER JOIN supplier_operation_order_log sol ON mo.id = sol.order_id 
-- WHERE mo.order_state = 'COMPLETED' AND sol.action_type = 1 
-- GROUP BY mo.hotel_id;

-- =====================================================
-- 测试数据总结
-- =====================================================
-- 用户ID=1 对应推荐方ID=1，拥有5个酒店关系：
-- 1. 北京王府井大酒店：6个上线房型，5个成单
-- 2. 上海外滩精品酒店：4个上线房型，3个成单  
-- 3. 广州珠江新城商务酒店：3个上线房型，2个成单
-- 4. 深圳南山科技园酒店：2个上线房型，1个成单（关系状态为管控）
-- 5. 杭州西湖度假村：5个上线房型，4个成单
-- 
-- 总计：20个上线房型，15个成单
-- 支持测试排序功能：按建立时间、上线房型数、成单数排序
-- =====================================================
